{"name": "thedevimpact", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@stripe/stripe-js": "^7.3.1", "@tailwindcss/vite": "^4.1.3", "pinia": "^3.0.3", "stripe": "^18.2.1", "tailwindcss": "^4.1.3", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue3-marquee": "^4.2.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "daisyui": "^5.0.13", "vite": "^6.2.0"}}