<!DOCTYPE html>
<html lang="fr">
  <head>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-P3DLVKK2");
    </script>
    <!-- End Google Tag Manager -->

    <!-- Google Analytics est configuré via Google Tag Manager -->
    <meta charset="UTF-8" />
    <link
      rel="icon"
      type="image/png"
      href="/src/assets/logo-TheDI-removeBG.png"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="/src/styles.css" rel="stylesheet" />

    <!-- FontAwesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" />

    <!-- SEO Meta Tags Primaires -->
    <title>
      The Dev Impact | Création de Sites Web Sur Mesure et Performants
    </title>
    <meta
      name="description"
      content="Agence spécialisée dans la création de sites web sur mesure, performants et optimisés pour convertir vos visiteurs en clients. Développement web professionnel, design moderne et solutions adaptées à vos besoins."
    />
    <meta
      name="keywords"
      content="création site web, développement web, site sur mesure, site vitrine, site responsive, agence web, performance web, SEO, expérience utilisateur, design web, développeur freelance"
    />
    <meta name="author" content="The Dev Impact" />
    <meta name="robots" content="index, follow" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://thedevimpact.com/" />
    <meta
      property="og:title"
      content="The Dev Impact | Sites Web Sur Mesure et Performants"
    />
    <meta
      property="og:description"
      content="Création de sites web sur mesure, performants et optimisés pour convertir vos visiteurs en clients. Développement web professionnel et design moderne."
    />
    <meta property="og:image" content="https://thedevimpact.com/og-image.jpg" />
    <meta property="og:locale" content="fr_FR" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://thedevimpact.com/" />
    <meta
      property="twitter:title"
      content="The Dev Impact | Sites Web Sur Mesure et Performants"
    />
    <meta
      property="twitter:description"
      content="Création de sites web sur mesure, performants et optimisés pour convertir vos visiteurs en clients. Développement web professionnel et design moderne."
    />
    <meta
      property="twitter:image"
      content="https://thedevimpact.com/twitter-image.jpg"
    />
    <meta property="twitter:creator" content="@thedevimpact" />

    <!-- Balises canoniques et alternatives linguistiques -->
    <link rel="canonical" href="https://thedevimpact.com/" />

    <!-- Structured Data / Schema.org -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "The Dev Impact",
        "url": "https://thedevimpact.com/",
        "description": "Agence spécialisée dans la création de sites web sur mesure, performants et optimisés pour convertir vos visiteurs en clients.",
        "inLanguage": "fr-FR",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://thedevimpact.com/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }
    </script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "The Dev Impact",
        "url": "https://thedevimpact.com/",
        "logo": "https://thedevimpact.com/logo.png",
        "description": "Agence spécialisée dans la création de sites web sur mesure, performants et optimisés pour convertir vos visiteurs en clients.",
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "+33-6-12-34-56-78",
          "contactType": "customer service",
          "availableLanguage": ["French"],
          "email": "<EMAIL>"
        },
        "sameAs": [
          "https://www.facebook.com/thedevimpact/",
          "https://twitter.com/thedevimpact/",
          "https://www.instagram.com/thedevimpact/",
          "https://www.linkedin.com/company/thedevimpact/",
          "https://www.comeup.com/fr/@thedevimpact"
        ]
      }
    </script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Service",
        "serviceType": "Création de sites web",
        "provider": {
          "@type": "Organization",
          "name": "The Dev Impact",
          "url": "https://thedevimpact.com/"
        },
        "areaServed": "France",
        "description": "Création de sites web sur mesure, performants et optimisés pour convertir vos visiteurs en clients.",
        "offers": {
          "@type": "AggregateOffer",
          "highPrice": "2500",
          "lowPrice": "850",
          "priceCurrency": "EUR",
          "offerCount": "3"
        }
      }
    </script>

    <!-- Preconnect et Preload pour optimisation des performances -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" />
    <link rel="preload" as="image" href="/src/assets/logo-TheDI-removeBG.png" />

    <!-- PWA Support -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#000000" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <link rel="apple-touch-icon" href="/icons/apple-touch-icon.png" />
  </head>
  <body>
   <!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-P3DLVKK2"
  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>

    <!-- Formulaire caché pour Netlify Forms - Ne pas supprimer -->
    <form name="demande-devis" netlify netlify-honeypot="bot-field" hidden>
      <input type="text" name="prenom" />
      <input type="text" name="nom" />
      <input type="email" name="email" />
      <input type="text" name="entreprise" />
      <input type="text" name="type-site" />
      <textarea name="projet"></textarea>
      <input type="text" name="options" />
      <input type="checkbox" name="rappel" />
      <input type="tel" name="telephone" />
      <input type="hidden" name="form-name" value="demande-devis" />
    </form>

    <!-- Formulaire caché pour Netlify Forms - Demande de démo -->
    <form name="demande-demo" netlify netlify-honeypot="bot-field" hidden>
      <input type="text" name="prenom" />
      <input type="text" name="nom" />
      <input type="email" name="email" />
      <input type="tel" name="telephone" />
      <input type="text" name="produit" />
      <textarea name="message"></textarea>
      <input type="hidden" name="form-name" value="demande-demo" />
    </form>

    <!-- Enregistrement du Service Worker pour les PWA -->
    <script>
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker
            .register("/service-worker.js")
            .then((registration) => {
              // console.log('Service Worker enregistré avec succès:', registration.scope);
            })
            .catch((error) => {
              // console.log('Erreur lors de l\'enregistrement du Service Worker:', error);
            });
        });
      }
    </script>
  </body>
</html>
