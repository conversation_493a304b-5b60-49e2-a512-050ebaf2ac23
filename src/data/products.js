// Données des produits - Sites web comme œuvres d'art
export const products = [
  {
    id: 'restaurant-ore',
    name: 'Restaurateurs',
    subtitle: "Exemple d'application : Site vitrine pour Restaurants (gastronomiques, traditionnels, bistronomiques, etc.), Traiteurs, Chefs à domicile, Foodtrucks, Boulangeries / Pâtisseries, Cavistes, Bars à vins, Bars à cocktails, Chocolatiers, Glaciers, et autres",
    price: 882,
    originalPrice: 980,
    promotion: {
      discount: 10,
      endDate: '2024-08-31',
      label: 'Offre Été -10%'
    },
    category: 'Restauration',
    featured: true,
    description: 'Faites rayonner votre restaurant avec un site vitrine élégant, moderne et immersif. Ce modèle a été conçu pour impressionner vos visiteurs dès les premières secondes, tout en présentant votre établissement sous son meilleur jour.',
    detailedDescription: `
      <h3>🍽️ Site vitrine pour restaurateur – Modèle personnalisable</h3>
      <p>Faites rayonner votre restaurant avec un site vitrine élégant, moderne et immersif. Ce modèle a été conçu pour impressionner vos visiteurs dès les premières secondes, tout en présentant votre établissement sous son meilleur jour.</p>
      
      <h4>✨ Ce que ce site offre :</h4>
      <ul>
        <li>✅ <strong>Design haut de gamme</strong> – Une mise en page soignée, des animations fluides et une ambiance visuelle digne des plus belles tables.</li>
        <li>✅ <strong>Navigation fluide & mobile-friendly</strong> – Compatible tous supports (mobile, tablette, ordinateur).</li>
        <li>✅ <strong>Sections clés pour un restaurant :</strong>
          <ul>
            <li>Accueil visuel fort (slogan + image immersive)</li>
            <li>Présentation de votre établissement</li>
            <li>Carte du menu (entrées, plats, desserts, boissons)</li>
            <li>Galerie photos des plats et de la salle</li>
            <li>Témoignages clients</li>
            <li>Informations pratiques (horaires, plan, contact)</li>
          </ul>
        </li>
        <li>✅ <strong>Personnalisable facilement</strong> – Lorsque vous achetez ce site, je le personnalise pour vous :
          <ul>
            <li>Insertion de votre logo, vos textes, vos photos, vos couleurs</li>
            <li>Mise en ligne incluse (aucune compétence technique nécessaire)</li>
          </ul>
        </li>
      </ul>
      
      <h4>👨‍🍳 Pour qui est-ce ?</h4>
      <p>Ce site est idéal pour :</p>
      <ul>
        <li>Restaurants gastronomiques ou traditionnels</li>
        <li>Bistros, brasseries, pizzerias, foodtrucks</li>
        <li>Chefs indépendants souhaitant valoriser leur cuisine</li>
      </ul>
      
      <h4>🛠️ Délai de livraison</h4>
      <p>Site personnalisé : 3 à 5 jours ouvrés après réception de vos contenus</p>
      <p>Version démo visible avant achat (vous visitez le site comme s'il était déjà le vôtre)</p>
      
      <h4>💬 Besoin d'un menu interactif, d'une réservation en ligne ou d'un design encore plus spécifique ?</h4>
      <p>Contactez-moi, je propose également des options sur mesure.</p>
    `,
    shortDescription: `💻 Site vitrine moderne pour restaurateur (démo visible)
🎨 Personnalisation complète : logo, textes, photos, couleurs
📱 Adapté aux mobiles et tablettes
🕐 Livraison personnalisée sous 3 à 5 jours
🔧 Mise en ligne incluse, vous n'avez rien à faire !`,
    technologies: ['Vue.js 3', 'Tailwind CSS', 'Responsive Design', 'Animations CSS'],
    images: [
      '/src/assets/jardindore/jardindore-1.webp',
      '/src/assets/jardindore/jardindore-2.webp',
      '/src/assets/jardindore/jardindore-3.webp',
      '/src/assets/jardindore/jardindore-4.webp'
    ],
    previewUrl: 'https://ore-demo.thedevimpact.com',
    deliverables: [
      'Site personnalisé avec vos contenus',
      'Mise en ligne incluse',
      'Formation à la gestion du site',
      'Fichiers sources',
      'Support 30 jours'
    ],
    features: [
      'Design haut de gamme',
      'Mobile-friendly',
      'Galerie photos',
      'Carte du menu',
      'Informations pratiques',
      'Personnalisation complète'
    ],
    tags: ['Restaurant', 'Vitrine', 'Gastronomie', 'Personnalisable'],
    stripeEnabled: true,
    stripePriceId: 'price_restaurant_ore_930'
  }
];

// Fonctions utilitaires
export function getProductById(id) {
  return products.find(product => product.id === id);
}

export function getFeaturedProducts() {
  return products.filter(product => product.featured);
}

export function getProductsByCategory(category) {
  return products.filter(product => product.category === category);
}

export function getCategories() {
  const categoryMap = new Map();
  
  // Ajouter la catégorie "all" en premier
  categoryMap.set('all', { id: 'all', name: 'Tous', count: products.length });
  
  // Définir toutes les catégories disponibles
  const allCategories = [
    'Restauration',
    'Beauté', 
    'Artisanat',
    'Tourisme',
    'Services',
    'Freelance',
    'Événementiel',
    'Boutiques',
    'Nouveaux'
  ];
  
  // Initialiser toutes les catégories avec count 0
  allCategories.forEach(category => {
    categoryMap.set(category, {
      id: category,
      name: category,
      count: 0
    });
  });
  
  // Compter les produits par catégorie
  products.forEach(product => {
    const category = product.category;
    if (categoryMap.has(category)) {
      categoryMap.get(category).count++;
    }
  });
  
  return Array.from(categoryMap.values());
} 