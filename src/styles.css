/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";
@plugin "daisyui";


/* Configuration du thème modernisé */
@theme {
  /* Couleurs principales conservées */
  --color-b1: #040F0F;
  --color-b2: #57737A;
  --color-b3: #85BDBF;
  --color-b4: #B7E4E8;
  --color-b5: #C9FBFF;
  --color-b6: #EF946C;
  
  /* Nouvelles couleurs d'accent */
  --color-accent: #3B82F6;
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  
  /* Ajout des couleurs dans la palette Tailwind */
  --color-primary: var(--color-b3);
  --color-secondary: var(--color-b6);
  --color-neutral: var(--color-b1);
  --color-light: var(--color-b5);
  --color-lighter: var(--color-b4);
  
  /* Gradients modernes */
  --gradient-primary: linear-gradient(135deg, var(--color-b3), var(--color-b4));
  --gradient-accent: linear-gradient(135deg, var(--color-b6), #FF7B7B);
  --gradient-dark: linear-gradient(135deg, var(--color-b1), var(--color-b2));
  
  /* Typography moderne */
  --font-display: "Inter", sans-serif;
  --font-mono: "JetBrains Mono", monospace;
  
  /* Breakpoints */
  --breakpoint-xs: 430px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Spacing scale moderne */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  
  /* Shadows modernes */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-glow: 0 0 20px var(--color-b3);
  
  /* Border radius moderne */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Animations personnalisées */
  --animate-fade-in-up: fadeInUp 0.6s ease-out;
}

/* Styles de base */
@layer base {
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--color-b5) 0%, var(--color-b4) 100%);
    min-height: 100vh;
  }
}

/* Composants réutilisables pour TheShop */
@layer components {
  /* Boutons personnalisés */
  .btn-primary {
    @apply bg-b3 hover:bg-b2 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-b6 hover:bg-b6/90 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }

  .btn-outline {
    @apply border-2 border-b3 text-b3 hover:bg-b3 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300;
  }

  .btn-outline-b6 {
    @apply border-2 border-b6 text-b6 hover:bg-b6 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300;
  }

  /* Cards */
  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1;
  }

  .card-art {
    @apply bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-white/20;
  }

  /* Navigation */
  .nav-link {
    @apply text-b1 hover:text-b3 font-medium transition-colors duration-300 relative;
  }

  .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 50%;
    background: var(--color-b3);
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  .nav-link:hover::after {
    width: 100%;
  }

  /* Layouts responsive */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Formulaires */
  .form-input {
    @apply px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-b3 focus:ring-2 focus:ring-b3/20 transition-all;
  }

  /* Prix */
  .price-tag {
    @apply text-2xl font-bold text-b6 bg-white/90 px-4 py-2 rounded-full shadow-lg;
  }

  /* Styles pour les boutons dans la carte */
  .card-art .btn-primary,
  .card-art .btn-secondary,
  .card-art .btn-outline {
    @apply text-sm py-2 px-4;
  }

  /* Styles pour la prose (description détaillée) */
  .prose h3 {
    @apply text-xl font-bold text-b1 mt-8 mb-4;
  }

  .prose h4 {
    @apply text-lg font-semibold text-b2 mt-6 mb-3;
  }

  .prose ul {
    @apply list-disc pl-6 space-y-2 text-gray-700;
  }

  .prose li {
    @apply leading-relaxed;
  }

  .prose p {
    @apply mb-4 text-gray-700 leading-relaxed;
  }
}

/* Animations personnalisées */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--color-b3);
  }
  50% {
    box-shadow: 0 0 20px var(--color-b3), 0 0 30px var(--color-b3);
  }
}

/* Animations flottantes pour les formes décoratives */
@keyframes float-slow {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg) scale(1); 
  }
  33% { 
    transform: translateY(-20px) rotate(2deg) scale(1.05); 
  }
  66% { 
    transform: translateY(-10px) rotate(-1deg) scale(0.98); 
  }
}

@keyframes float-delayed {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg) scale(1); 
  }
  25% { 
    transform: translateY(-15px) rotate(-2deg) scale(1.02); 
  }
  50% { 
    transform: translateY(-25px) rotate(1deg) scale(0.96); 
  }
  75% { 
    transform: translateY(-8px) rotate(-1deg) scale(1.03); 
  }
}

/* Utilitaires personnalisés */
@layer utilities {
  /* Texte avec gradient */
  .text-gradient {
    background: linear-gradient(135deg, var(--color-b2), var(--color-b3));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Animation personnalisée */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
}

/* Classes utilitaires personnalisées */
.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 12s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 15s ease-in-out infinite;
  animation-delay: -3s;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--color-b3), var(--color-b4), var(--color-b6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-b1);
}

::-webkit-scrollbar-thumb {
  background: var(--color-b3);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-b4);
}

/* Selection styling */
::selection {
  background-color: var(--color-b3);
  color: white;
}

/* Focus states */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-b3 focus:ring-offset-2 focus:ring-offset-b1;
}

/* Boutique preparation styles */
.shop-badge {
  position: relative;
  overflow: hidden;
}

.shop-badge::before {
  content: "BOUTIQUE - BIENTÔT";
  position: absolute;
  width: 220px;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(135deg, var(--color-b6), #FF7B7B);
  color: white;
  padding: 0.25rem 2.2rem;
  font-size: 0.75rem;
  font-weight: 600;
  transform: rotate(45deg) translate(35%, -55%);
  transform-origin: center;
  white-space: nowrap;
}


.mobile-menu-line {
  @apply block w-full h-0.5 bg-b1 transition-all duration-300 absolute;
}

.nav-link {
  @apply text-b1 hover:text-b3 font-medium transition-colors duration-300 relative;
}

/* Styles pour les filtres */
.filter-btn {
  @apply px-4 py-2 rounded-full font-medium transition-all duration-300 border border-transparent;
}

.select-style {
  @apply px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-b3/20 focus:border-b3;
}

/* Styles pour les filtres */
.filter-btn {
  @apply px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 border border-transparent;
}

.filter-btn:hover {
  @apply border-b3/20 transform scale-105;
}

.select-style {
  @apply px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-b3/20 focus:border-b3 transition-all;
  min-width: 140px;
}

/* Boutons de filtres compacts */
.filter-btn-compact {
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background: white;
  color: #64748b;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-btn-compact:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.filter-btn-compact.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Select compact */
.select-style-compact {
  appearance: none;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 32px 8px 12px;
  font-size: 14px;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.select-style-compact:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.select-style-compact:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Cartes d'art améliorées avec fond artistique */
.card-art-enhanced {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.92));
  backdrop-filter: blur(12px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(59, 130, 246, 0.04);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.card-art-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), rgba(20, 184, 166, 0.3), transparent);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.card-art-enhanced:hover::before {
  transform: scaleX(1);
}

.card-art-enhanced:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12), 0 8px 20px rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.99), rgba(248, 250, 252, 0.95));
}