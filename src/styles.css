/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";
@plugin "daisyui";


/* Configuration du thème modernisé */
@theme {
  /* Couleurs principales conservées */
  --color-b1: #040F0F;
  --color-b2: #57737A;
  --color-b3: #85BDBF;
  --color-b4: #B7E4E8;
  --color-b5: #C9FBFF;
  --color-b6: #EF946C;
  --color-b7: #22C55E;
  --color-b8: #FB923C;
  --color-b9: #06B6D4;
  
  /* Nouvelles couleurs d'accent */
  --color-accent: #3B82F6;
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  
  /* Ajout des couleurs dans la palette Tailwind */
  --color-primary: var(--color-b3);
  --color-secondary: var(--color-b6);
  --color-neutral: var(--color-b1);
  --color-light: var(--color-b5);
  --color-lighter: var(--color-b4);
  
  /* Gradients modernes */
  --gradient-primary: linear-gradient(135deg, var(--color-b3), var(--color-b4));
  --gradient-accent: linear-gradient(135deg, var(--color-b6), #FF7B7B);
  --gradient-dark: linear-gradient(135deg, var(--color-b1), var(--color-b2));
  
  /* Typography moderne */
  --font-display: "Inter", sans-serif;
  --font-mono: "JetBrains Mono", monospace;
  
  /* Breakpoints */
  --breakpoint-xs: 430px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Spacing scale moderne */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  
  /* Shadows modernes */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-glow: 0 0 20px var(--color-b3);
  
  /* Border radius moderne */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Animations personnalisées */
  --animate-fade-in-up: fadeInUp 0.6s ease-out;
}

/* Styles de base */
@layer base {
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--color-b5) 0%, var(--color-b4) 100%);
    min-height: 100vh;
  }
}

/* Composants réutilisables pour TheShop */
@layer components {
  /* Boutons personnalisés */
  .btn-primary {
    @apply bg-b3 hover:bg-b2 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-b6 hover:bg-b6/90 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }

  .btn-outline {
    @apply border-2 border-b3 text-b3 hover:bg-b3 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300;
  }

  .btn-outline-b6 {
    @apply border-2 border-b6 text-b6 hover:bg-b6 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300;
  }

  /* Cards */
  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1;
  }

  .card-art {
    @apply bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-white/20;
  }

  /* Navigation */
  .nav-link {
    @apply text-b1 hover:text-b3 font-medium transition-colors duration-300 relative;
  }

  .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 50%;
    background: var(--color-b3);
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  .nav-link:hover::after {
    width: 100%;
  }

  /* Layouts responsive */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Formulaires */
  .form-input {
    @apply px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-b3 focus:ring-2 focus:ring-b3/20 transition-all;
  }

  /* Prix */
  .price-tag {
    @apply text-2xl font-bold text-b6 bg-white/90 px-4 py-2 rounded-full shadow-lg;
  }

  /* Styles pour les boutons dans la carte */
  .card-art .btn-primary,
  .card-art .btn-secondary,
  .card-art .btn-outline {
    @apply text-sm py-2 px-4;
  }

  /* Styles pour la prose (description détaillée) */
  .prose h3 {
    @apply text-xl font-bold text-b1 mt-8 mb-4;
  }

  .prose h4 {
    @apply text-lg font-semibold text-b2 mt-6 mb-3;
  }

  .prose ul {
    @apply list-disc pl-6 space-y-2 text-gray-700;
  }

  .prose li {
    @apply leading-relaxed;
  }

  .prose p {
    @apply mb-4 text-gray-700 leading-relaxed;
  }
}

/* Animations personnalisées */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--color-b3);
  }
  50% {
    box-shadow: 0 0 20px var(--color-b3), 0 0 30px var(--color-b3);
  }
}

/* Animations flottantes pour les formes décoratives */
@keyframes float-slow {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg) scale(1); 
  }
  33% { 
    transform: translateY(-20px) rotate(2deg) scale(1.05); 
  }
  66% { 
    transform: translateY(-10px) rotate(-1deg) scale(0.98); 
  }
}

@keyframes float-delayed {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg) scale(1); 
  }
  25% { 
    transform: translateY(-15px) rotate(-2deg) scale(1.02); 
  }
  50% { 
    transform: translateY(-25px) rotate(1deg) scale(0.96); 
  }
  75% { 
    transform: translateY(-8px) rotate(-1deg) scale(1.03); 
  }
}

/* Utilitaires personnalisés */
@layer utilities {
  /* Texte avec gradient */
  .text-gradient {
    background: linear-gradient(135deg, var(--color-b2), var(--color-b3));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Animation personnalisée */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
}

/* Classes utilitaires personnalisées */
.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 12s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 15s ease-in-out infinite;
  animation-delay: -3s;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--color-b3), var(--color-b4), var(--color-b6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-b1);
}

::-webkit-scrollbar-thumb {
  background: var(--color-b3);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-b4);
}

/* Selection styling */
::selection {
  background-color: var(--color-b3);
  color: white;
}

/* Focus states */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-b3 focus:ring-offset-2 focus:ring-offset-b1;
}

/* Boutique preparation styles */
.shop-badge {
  position: relative;
  overflow: hidden;
}

.shop-badge::before {
  content: "BOUTIQUE - BIENTÔT";
  position: absolute;
  width: 220px;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(135deg, var(--color-b6), #FF7B7B);
  color: white;
  padding: 0.25rem 2.2rem;
  font-size: 0.75rem;
  font-weight: 600;
  transform: rotate(45deg) translate(35%, -55%);
  transform-origin: center;
  white-space: nowrap;
}


.mobile-menu-line {
  @apply block w-full h-0.5 bg-b1 transition-all duration-300 absolute;
}

.nav-link {
  @apply text-b1 hover:text-b3 font-medium transition-colors duration-300 relative;
}

/* Styles pour les filtres */
.filter-btn {
  @apply px-4 py-2 rounded-full font-medium transition-all duration-300 border border-transparent;
}

.select-style {
  @apply px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-b3/20 focus:border-b3;
}

/* Styles pour les filtres */
.filter-btn {
  @apply px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 border border-transparent;
}

.filter-btn:hover {
  @apply border-b3/20 transform scale-105;
}

.select-style {
  @apply px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-b3/20 focus:border-b3 transition-all;
  min-width: 140px;
}

/* Boutons de filtres compacts */
.filter-btn-compact {
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background: white;
  color: #64748b;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-btn-compact:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.filter-btn-compact.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Select compact */
.select-style-compact {
  appearance: none;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 32px 8px 12px;
  font-size: 14px;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.select-style-compact:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.select-style-compact:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Cartes d'art améliorées avec fond artistique */
.card-art-enhanced {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.92));
  backdrop-filter: blur(12px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(59, 130, 246, 0.04);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.card-art-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), rgba(20, 184, 166, 0.3), transparent);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.card-art-enhanced:hover::before {
  transform: scaleX(1);
}

.card-art-enhanced:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12), 0 8px 20px rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.99), rgba(248, 250, 252, 0.95));
}

/* ========================================
   PROTECTION DU CONTENU
   ======================================== */

/* Désactiver la sélection de texte uniquement sur le contenu principal */
main,
.content-protected,
[data-protected="true"] {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* Permettre la sélection dans le header et footer pour les infos de contact */
header,
footer,
nav,
.contact-info,
.footer-links,
.selectable,
.email-selectable,
.phone-selectable,
input,
textarea,
[contenteditable="true"],
[data-selectable="true"] {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

/* Classes utilitaires pour la protection */
.protected {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

.unprotected {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

/* Désactiver le glisser-déposer des images */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: none;
}

/* Permettre les interactions sur les images cliquables */
img[role="button"],
img.clickable,
button img,
a img {
  pointer-events: auto;
}

/* Désactiver la sélection avec le curseur uniquement dans le contenu protégé */
main ::selection,
[data-protected="true"] ::selection,
.protected ::selection {
  background: transparent;
}

main ::-moz-selection,
[data-protected="true"] ::-moz-selection,
.protected ::-moz-selection {
  background: transparent;
}

main ::-webkit-selection,
[data-protected="true"] ::-webkit-selection,
.protected ::-webkit-selection {
  background: transparent;
}

/* Permettre la sélection visible dans le header et footer */
header ::selection,
footer ::selection,
nav ::selection,
.contact-info ::selection,
.footer-links ::selection,
.selectable ::selection,
[data-selectable="true"] ::selection {
  background: rgba(133, 189, 191, 0.3) !important; /* Couleur b3 avec transparence */
  color: inherit !important;
}

header ::-moz-selection,
footer ::-moz-selection,
nav ::-moz-selection,
.contact-info ::-moz-selection,
.footer-links ::-moz-selection,
.selectable ::-moz-selection,
[data-selectable="true"] ::-moz-selection {
  background: rgba(133, 189, 191, 0.3) !important;
  color: inherit !important;
}

header ::-webkit-selection,
footer ::-webkit-selection,
nav ::-webkit-selection,
.contact-info ::-webkit-selection,
.footer-links ::-webkit-selection,
.selectable ::-webkit-selection,
[data-selectable="true"] ::-webkit-selection {
  background: rgba(133, 189, 191, 0.3) !important;
  color: inherit !important;
}

/* Protection contre l'impression */
@media print {
  * {
    display: none !important;
  }

  body::before {
    content: "Ce contenu est protégé par des droits d'auteur - Impression non autorisée";
    display: block !important;
    text-align: center;
    font-size: 24px;
    color: red;
    margin-top: 50px;
  }
}

/* Message de protection dans la console */
body::before {
  content: "";
  display: none;
}

/* ========================================
   RESPONSIVE MOBILE OPTIMIZATIONS 350px+
   ======================================== */

/* Breakpoint xs pour très petits écrans (430px) */
@media (min-width: 430px) {
  .xs\:block { display: block; }
  .xs\:hidden { display: none; }
  .xs\:inline { display: inline; }
  .xs\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .xs\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .xs\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .xs\:px-4 { padding-left: 1rem; padding-right: 1rem; }
  .xs\:py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
  .xs\:mb-6 { margin-bottom: 1.5rem; }
  .xs\:mb-8 { margin-bottom: 2rem; }
  .xs\:gap-8 { gap: 2rem; }
  .xs\:h-16 { height: 4rem; }
  .xs\:p-2 { padding: 0.5rem; }
  .xs\:p-3 { padding: 0.75rem; }
}

/* Optimisations pour écrans 350px à 640px */
@media (max-width: 640px) {
  /* Réduction des espacements sur mobile */
  .mobile-compact {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
  }

  /* Textes optimisés pour mobile */
  .mobile-text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
  }

  /* Boutons pleine largeur sur mobile */
  .mobile-full-width {
    width: 100% !important;
  }

  /* Réduction des marges sur mobile */
  .mobile-reduced-margin {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  /* Optimisation des sections */
  section {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  /* Optimisation des conteneurs */
  .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
}

/* Optimisations pour très petits écrans (350px à 429px) */
@media (max-width: 429px) {
  /* Padding minimal pour éviter les débordements */
  .container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  /* Textes encore plus petits */
  h1 {
    font-size: 1.5rem !important;
    line-height: 2rem !important;
  }
  h2 {
    font-size: 1.25rem !important;
    line-height: 1.75rem !important;
  }
  h3 {
    font-size: 1.125rem !important;
    line-height: 1.5rem !important;
  }

  /* Boutons compacts */
  .btn, button, a[class*="btn"] {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }

  /* Réduction des gaps */
  .gap-8 { gap: 1rem !important; }
  .gap-6 { gap: 0.75rem !important; }
  .gap-4 { gap: 0.5rem !important; }

  /* Optimisations spécifiques pour très petits écrans */
  .min-h-screen {
    min-height: calc(100vh - 4rem) !important;
  }

  /* Éviter les débordements horizontaux */
  body, html {
    overflow-x: hidden !important;
  }

  /* Améliorer la lisibilité des textes */
  p, span, div {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* Optimisations de performance mobile */
  * {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  /* Améliorer le scroll sur mobile */
  html {
    scroll-behavior: smooth !important;
    -webkit-overflow-scrolling: touch !important;
  }
}

/* Optimisations pour les écrans tactiles */
@media (hover: none) and (pointer: coarse) {
  /* Augmenter la taille des zones tactiles */
  button, a, [role="button"] {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  /* Désactiver les effets hover sur tactile */
  .hover\:scale-105:hover,
  .hover\:shadow-lg:hover,
  .hover\:shadow-xl:hover {
    transform: none !important;
    box-shadow: inherit !important;
  }
}