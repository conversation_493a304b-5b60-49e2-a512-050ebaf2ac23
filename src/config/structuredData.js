/**
 * Configuration des données structurées (Schema.org) pour le SEO
 * Ces données aident les moteurs de recherche à mieux comprendre le contenu du site
 */

export const websiteSchema = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "The Dev Impact",
  "url": "https://thedevimpact.com/",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://thedevimpact.com/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  }
};

export const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "The Dev Impact",
  "url": "https://thedevimpact.com/",
  "logo": "https://thedevimpact.com/logo.png",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+33-6-12-34-56-78",
    "contactType": "customer service",
    "availableLanguage": ["French"]
  },
  "sameAs": [
    "https://www.facebook.com/thedevimpact/",
    "https://twitter.com/thedevimpact/",
    "https://www.instagram.com/thedevimpact/",
    "https://www.linkedin.com/company/thedevimpact/"
  ]
};

export const localBusinessSchema = {
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "name": "The Dev Impact",
  "image": "https://thedevimpact.com/logo.png",
  "telephone": "+33-6-12-34-56-78",
  "email": "<EMAIL>",
  "url": "https://thedevimpact.com/",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "123 Rue de l'Innovation",
    "addressLocality": "Paris",
    "postalCode": "75000",
    "addressCountry": "FR"
  },
  "openingHoursSpecification": [
    {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      "opens": "09:00",
      "closes": "18:00"
    }
  ],
  "priceRange": "€€"
};

export const serviceSchema = {
  "@context": "https://schema.org",
  "@type": "Service",
  "serviceType": "Création de sites web",
  "provider": {
    "@type": "Organization",
    "name": "The Dev Impact"
  },
  "areaServed": {
    "@type": "Country",
    "name": "France"
  },
  "offers": {
    "@type": "Offer",
    "price": "1000.00",
    "priceCurrency": "EUR",
    "priceValidUntil": "2024-12-31"
  }
};

export const faqSchema = {
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Qu'est-ce qu'un site vitrine ?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Un site vitrine est une présence web professionnelle qui met en valeur votre entreprise, vos services et votre identité de marque. C'est comme une vitrine digitale disponible 24/7, permettant à vos clients potentiels de découvrir votre activité."
      }
    },
    {
      "@type": "Question",
      "name": "Pourquoi ai-je besoin d'un site vitrine ?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Un site vitrine est essentiel car il renforce votre crédibilité professionnelle, améliore votre visibilité en ligne, et permet d'atteindre de nouveaux clients. C'est souvent le premier point de contact avec vos prospects, disponible en permanence pour présenter vos services."
      }
    },
    {
      "@type": "Question",
      "name": "Quels sont les délais de création d'un site ?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Le délai moyen est de 2 à 3 semaines, selon la complexité du projet. Nous suivons un processus en 4 étapes : consultation initiale (2-3 jours), design et validation (1 semaine), développement (1 semaine), et finalisation avec vos retours (3-4 jours)."
      }
    }
  ]
};

// Fonction pour générer le script JSON-LD
export const generateStructuredDataScript = (schema) => {
  return `<script type="application/ld+json">${JSON.stringify(schema)}</script>`;
};

// Fonction pour injecter les données structurées dans le head
export const injectStructuredData = (schema) => {
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.innerHTML = JSON.stringify(schema);
  document.head.appendChild(script);
};

export default {
  websiteSchema,
  organizationSchema,
  localBusinessSchema,
  serviceSchema,
  faqSchema,
  generateStructuredDataScript,
  injectStructuredData
};
