import { createWebHistory, createRouter } from 'vue-router'

import HomeView from './components/views/HomeView.vue'
import FormulePage from './components/views/FormulePage.vue'
import OptionsPage from './components/views/OptionsPage.vue'
import PackEntreprisePage from './components/views/PackEntreprisePage.vue'
import AccompagnementPage from './components/views/AccompagnementPage.vue'
import BoutiquePage from './components/views/BoutiquePage.vue'
import ProductDetailView from './components/views/ProductDetailView.vue'
import SuccessPage from './components/views/SuccessPage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    { 
      path: '/', 
      component: HomeView,
      meta: {
        title: 'Accueil',
        description: 'TheDevImpact - Création de sites web uniques et sur-mesure. Découvrez nos formules et notre boutique de sites exclusifs.'
      }
    },
    {
      path: '/formules',
      component: FormulePage,
      meta: {
        title: 'Création Sur-Mesure',
        description: 'Découvrez nos formules de création de sites web sur-mesure. Du site vitrine au e-commerce, nous créons votre présence digitale unique.'
      }
    },
    {
      path: '/options',
      component: OptionsPage,
      meta: {
        title: 'Options & Modules',
        description: 'Découvrez nos options et modules complémentaires : modifications ponctuelles, visuels personnalisés, maintenance, nom de domaine et plus encore.'
      }
    },
    {
      path: '/pack-creation',
      component: PackEntreprisePage,
      meta: {
        title: 'Pack Création d\'Entreprise',
        description: 'Lancement clé en main : logo, site one-page, réseaux sociaux, cartes de visite, adresse mail pro, page Google Business etc - offre modulable.'
      }
    },
    {
      path: '/accompagnement',
      component: AccompagnementPage,
      meta: {
        title: 'Accompagnement Horaire',
        description: 'Aide personnalisée à l\'heure : je vous montre, je fais avec vous (site, réseaux, Canva, newsletter, etc.)'
      }
    },
    {
      path: '/community-management',
      redirect: '/#contact',
      meta: {
        title: 'Community Management',
        description: 'Stratégie, optimisation ou gestion de vos réseaux sociaux (Instagram, Facebook)'
      }
    },
    { 
      path: '/boutique', 
      component: BoutiquePage,
      meta: {
        title: 'The Shop',
        description: 'Explorez notre collection exclusive de sites web uniques. Chaque création est une œuvre d\'art numérique prête à sublimer votre présence en ligne.'
      }
    },
    { 
      path: '/product/:id', 
      component: ProductDetailView,
      meta: {
        title: 'Détail Produit',
        description: 'Découvrez tous les détails de ce site web unique et exclusif de notre collection.'
      }
    },
    { 
      path: '/success', 
      component: SuccessPage,
      meta: {
        title: 'Paiement Réussi',
        description: 'Votre paiement a été traité avec succès. Merci pour votre achat !'
      }
    },
  ],
  scrollBehavior(to, _from, savedPosition) {
    // Si on a une position sauvegardée (retour arrière), on l'utilise
    if (savedPosition) {
      return savedPosition
    }

    // Si l'URL contient un hash, on défile vers l'élément avec un offset pour la navbar
    if (to.hash) {
      return new Promise((resolve) => {
        // Petit délai pour s'assurer que le DOM est prêt
        setTimeout(() => {
          const element = document.querySelector(to.hash)
          if (element) {
            const navbarHeight = 80 // Hauteur approximative de la navbar
            resolve({
              el: to.hash,
              top: -navbarHeight,
              behavior: 'smooth'
            })
          } else {
            // Si l'élément n'est pas trouvé, on va en haut de la page
            resolve({ top: 0, behavior: 'smooth' })
          }
        }, 100)
      })
    }

    // Par défaut, on va en haut de la page
    return { top: 0, behavior: 'smooth' }
  }
})

// Gestion des meta tags pour le SEO
router.beforeEach((to, from, next) => {
  // Mettre à jour la meta description
  if (to.meta.description) {
    let metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.name = 'description'
      document.head.appendChild(metaDescription)
    }
    metaDescription.content = to.meta.description
  }
  
  next()
})

// Correction pour l'actualisation de la page avec un hash
if (window.location.hash) {
  // On attend que l'application soit montée
  window.addEventListener('load', () => {
    // On laisse le router gérer le défilement
    // Pas besoin de code supplémentaire ici
  })
}

export default router
