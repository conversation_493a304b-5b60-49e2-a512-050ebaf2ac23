<script setup>
import WhatsAppButton from './components/TDI-modules/WhatsAppButton.vue';
import ToastNotification from './components/TDI-modules/ToastNotification.vue';
// FloatingButton supprimé
import MetaTags from './components/SEO/MetaTags.vue';
// GoogleAnalytics est configuré via Google Tag Manager
import { useRoute } from 'vue-router';
import { computed } from 'vue';
import { getSeoConfig } from './config/seoConfig';

const route = useRoute();

// Configuration SEO dynamique basée sur la route actuelle
const seoConfig = computed(() => {
  return getSeoConfig(route.path);
});
</script>

<template>
  <main>
    <!-- Composant SEO pour gérer dynamiquement les balises meta -->
    <MetaTags
      :title="seoConfig.title"
      :description="seoConfig.description"
      :keywords="seoConfig.keywords"
      :ogImage="seoConfig.ogImage"
      :twitterImage="seoConfig.twitterImage"
    />

    <!-- Google Analytics est configuré via Google Tag Manager -->

    <RouterView />
    <WhatsAppButton id="btn-whatsapp"/>
    
    <!-- Notifications Toast -->
    <ToastNotification />
  </main>
</template>
