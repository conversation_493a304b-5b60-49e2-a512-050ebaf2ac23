import { onMounted, onUnmounted } from 'vue'

/**
 * Composable pour protéger le contenu du site contre le vol
 * - Désactive le clic droit
 * - Désactive les raccourcis clavier de développement
 * - Désactive la sélection de texte
 * - Désactive le glisser-déposer d'images
 */
export function useContentProtection() {
  // Fonction pour désactiver le clic droit
  const disableRightClick = (e) => {
    e.preventDefault()
    return false
  }

  // Fonction pour désactiver les raccourcis clavier
  const disableKeyboardShortcuts = (e) => {
    // Permettre les raccourcis dans le header et footer
    const isInAllowedElement = e.target.closest('header, footer, nav, .contact-info, .footer-links, [data-selectable="true"]')
    if (isInAllowedElement) {
      return true
    }

    // Désactiver F12 (DevTools)
    if (e.key === 'F12') {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+Shift+I (DevTools)
    if (e.ctrlKey && e.shiftKey && e.key === 'I') {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+Shift+J (Console)
    if (e.ctrlKey && e.shiftKey && e.key === 'J') {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+U (Voir le code source)
    if (e.ctrlKey && e.key === 'u') {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+S (Sauvegarder)
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+A (Tout sélectionner) uniquement dans le contenu protégé
    if (e.ctrlKey && e.key === 'a' && e.target.closest('main, [data-protected="true"]')) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+P (Imprimer)
    if (e.ctrlKey && e.key === 'p') {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+C (Copier) uniquement dans le contenu protégé
    if (e.ctrlKey && e.key === 'c' && e.target.closest('main, [data-protected="true"]')) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+V (Coller) uniquement dans le contenu protégé
    if (e.ctrlKey && e.key === 'v' && e.target.closest('main, [data-protected="true"]')) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+X (Couper) uniquement dans le contenu protégé
    if (e.ctrlKey && e.key === 'x' && e.target.closest('main, [data-protected="true"]')) {
      e.preventDefault()
      return false
    }
  }

  // Fonction pour désactiver la sélection de texte (uniquement sur le contenu protégé)
  const disableTextSelection = (e) => {
    // Permettre la sélection dans le header, footer et éléments de contact
    const allowedElements = ['HEADER', 'FOOTER', 'NAV', 'INPUT', 'TEXTAREA']
    const isInAllowedElement = e.target.closest('header, footer, nav, .contact-info, .footer-links, [data-selectable="true"]')

    if (allowedElements.includes(e.target.tagName) || isInAllowedElement) {
      return true
    }

    e.preventDefault()
    return false
  }

  // Fonction pour désactiver le glisser-déposer
  const disableDragDrop = (e) => {
    e.preventDefault()
    return false
  }

  // Fonction d'initialisation
  const enableProtection = () => {
    // Désactiver le clic droit uniquement sur le contenu principal
    const mainContent = document.querySelector('main')
    if (mainContent) {
      mainContent.addEventListener('contextmenu', disableRightClick)
    }

    // Désactiver les raccourcis clavier globalement
    document.addEventListener('keydown', disableKeyboardShortcuts)

    // Désactiver la sélection de texte de manière ciblée
    document.addEventListener('selectstart', disableTextSelection)
    document.addEventListener('dragstart', disableDragDrop)

    // Ajouter la classe de protection au contenu principal
    if (mainContent) {
      mainContent.setAttribute('data-protected', 'true')
    }

    // Désactiver le glisser-déposer des images dans le contenu principal
    const protectedImages = document.querySelectorAll('main img, [data-protected="true"] img')
    protectedImages.forEach(img => {
      img.draggable = false
      img.addEventListener('dragstart', disableDragDrop)
    })

    // Message d'avertissement pour les tentatives de vol
    document.addEventListener('keydown', (e) => {
      if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
        console.clear()
        console.log('%c⚠️ ATTENTION ⚠️', 'color: red; font-size: 20px; font-weight: bold;')
        console.log('%cCe site est protégé par des droits d\'auteur.', 'color: red; font-size: 14px;')
        console.log('%cToute tentative de copie ou de vol de contenu est interdite.', 'color: red; font-size: 14px;')
        console.log('%c© 2025 The Dev Impact - Tous droits réservés', 'color: red; font-size: 12px;')
      }
    })
  }

  // Fonction de nettoyage
  const disableProtection = () => {
    const mainContent = document.querySelector('main')
    if (mainContent) {
      mainContent.removeEventListener('contextmenu', disableRightClick)
      mainContent.removeAttribute('data-protected')
    }

    document.removeEventListener('keydown', disableKeyboardShortcuts)
    document.removeEventListener('selectstart', disableTextSelection)
    document.removeEventListener('dragstart', disableDragDrop)
  }

  // Activer la protection au montage du composant
  onMounted(() => {
    enableProtection()
  })

  // Désactiver la protection au démontage du composant
  onUnmounted(() => {
    disableProtection()
  })

  return {
    enableProtection,
    disableProtection
  }
}
