import { onMounted, onUnmounted } from 'vue'

/**
 * Composable pour protéger le contenu du site contre le vol
 * - Désactive le clic droit
 * - Désactive les raccourcis clavier de développement
 * - Désactive la sélection de texte
 * - Désactive le glisser-déposer d'images
 */
export function useContentProtection() {
  // Fonction pour désactiver le clic droit
  const disableRightClick = (e) => {
    e.preventDefault()
    return false
  }

  // Fonction pour désactiver les raccourcis clavier
  const disableKeyboardShortcuts = (e) => {
    // Désactiver F12 (DevTools)
    if (e.keyCode === 123) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+Shift+I (DevTools)
    if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+Shift+J (Console)
    if (e.ctrl<PERSON><PERSON> && e.shift<PERSON><PERSON> && e.keyCode === 74) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+U (Voir le code source)
    if (e.ctrlKey && e.keyCode === 85) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+S (Sauvegarder)
    if (e.ctrlKey && e.keyCode === 83) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+A (Tout sélectionner)
    if (e.ctrlKey && e.keyCode === 65) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+P (Imprimer)
    if (e.ctrlKey && e.keyCode === 80) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+C (Copier)
    if (e.ctrlKey && e.keyCode === 67) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+V (Coller)
    if (e.ctrlKey && e.keyCode === 86) {
      e.preventDefault()
      return false
    }

    // Désactiver Ctrl+X (Couper)
    if (e.ctrlKey && e.keyCode === 88) {
      e.preventDefault()
      return false
    }
  }

  // Fonction pour désactiver la sélection de texte
  const disableTextSelection = (e) => {
    e.preventDefault()
    return false
  }

  // Fonction pour désactiver le glisser-déposer
  const disableDragDrop = (e) => {
    e.preventDefault()
    return false
  }

  // Fonction pour désactiver la sélection avec la souris
  const disableSelection = () => {
    return false
  }

  // Fonction d'initialisation
  const enableProtection = () => {
    // Désactiver le clic droit
    document.addEventListener('contextmenu', disableRightClick)
    
    // Désactiver les raccourcis clavier
    document.addEventListener('keydown', disableKeyboardShortcuts)
    
    // Désactiver la sélection de texte
    document.addEventListener('selectstart', disableTextSelection)
    document.addEventListener('dragstart', disableDragDrop)
    
    // Désactiver la sélection avec CSS
    document.body.style.userSelect = 'none'
    document.body.style.webkitUserSelect = 'none'
    document.body.style.mozUserSelect = 'none'
    document.body.style.msUserSelect = 'none'
    
    // Désactiver le glisser-déposer des images
    const images = document.querySelectorAll('img')
    images.forEach(img => {
      img.draggable = false
      img.addEventListener('dragstart', disableDragDrop)
    })

    // Message d'avertissement pour les tentatives de vol
    document.addEventListener('keydown', (e) => {
      if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73)) {
        console.clear()
        console.log('%c⚠️ ATTENTION ⚠️', 'color: red; font-size: 20px; font-weight: bold;')
        console.log('%cCe site est protégé par des droits d\'auteur.', 'color: red; font-size: 14px;')
        console.log('%cToute tentative de copie ou de vol de contenu est interdite.', 'color: red; font-size: 14px;')
        console.log('%c© 2025 The Dev Impact - Tous droits réservés', 'color: red; font-size: 12px;')
      }
    })
  }

  // Fonction de nettoyage
  const disableProtection = () => {
    document.removeEventListener('contextmenu', disableRightClick)
    document.removeEventListener('keydown', disableKeyboardShortcuts)
    document.removeEventListener('selectstart', disableTextSelection)
    document.removeEventListener('dragstart', disableDragDrop)
    
    // Restaurer la sélection
    document.body.style.userSelect = ''
    document.body.style.webkitUserSelect = ''
    document.body.style.mozUserSelect = ''
    document.body.style.msUserSelect = ''
  }

  // Activer la protection au montage du composant
  onMounted(() => {
    enableProtection()
  })

  // Désactiver la protection au démontage du composant
  onUnmounted(() => {
    disableProtection()
  })

  return {
    enableProtection,
    disableProtection
  }
}
