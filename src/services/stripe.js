import { loadStripe } from '@stripe/stripe-js';

// Configuration Stripe
const STRIPE_PUBLIC_KEY = 'pk_test_TYooMQauvdEDq54NiTphI7jx'; // Remplacer par votre clé publique
let stripeInstance = null;

// Initialisation de Stripe
export async function getStripe() {
  if (!stripeInstance) {
    stripeInstance = await loadStripe(STRIPE_PUBLIC_KEY);
  }
  return stripeInstance;
}

// Configuration des produits Stripe (ID de prix pour chaque produit)
export const STRIPE_PRICE_IDS = {
  'restaurant-ore': 'price_restaurant_ore_930' // ID Stripe pour le site Oré - Gastronomie
};

// Fonction pour créer une session de paiement
export async function createCheckoutSession(productId, productData) {
  try {
    const stripe = await getStripe();
    
    // Simulation d'un appel API pour créer la session Stripe
    // En production, ceci serait un appel à votre backend
    const sessionData = {
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: 'eur',
          product_data: {
            name: productData.name,
            description: productData.subtitle,
            images: [productData.images[0]]
          },
          unit_amount: productData.price * 100 // Stripe utilise les centimes
        },
        quantity: 1
      }],
      mode: 'payment',
      success_url: `${window.location.origin}/success?product=${productId}`,
      cancel_url: `${window.location.origin}/product/${productId}`,
      metadata: {
        product_id: productId
      }
    };

    // Redirection vers Stripe Checkout
    // Note: En production, vous devriez créer la session côté serveur
    console.log('Session de paiement créée:', sessionData);
    
    // Pour la démo, on simule une redirection
    return {
      success: true,
      sessionData
    };
    
  } catch (error) {
    console.error('Erreur lors de la création de la session de paiement:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Fonction pour traiter le paiement avec Stripe Elements
export async function processPayment(paymentMethod, amount, productData) {
  try {
    const stripe = await getStripe();
    
    // Simulation du traitement de paiement
    // En production, ceci nécessiterait un backend pour confirmer le paiement
    const paymentIntent = {
      amount: amount * 100,
      currency: 'eur',
      payment_method: paymentMethod,
      confirmation_method: 'manual',
      confirm: true,
      metadata: {
        product_name: productData.name,
        product_id: productData.id
      }
    };

    console.log('Traitement du paiement:', paymentIntent);
    
    // Simulation d'un succès
    return {
      success: true,
      paymentIntent: {
        id: 'pi_demo_' + Date.now(),
        status: 'succeeded'
      }
    };
    
  } catch (error) {
    console.error('Erreur lors du traitement du paiement:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Utilitaires pour le formatage des prix
export function formatPrice(price) {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price);
}

export function calculateDiscount(originalPrice, currentPrice) {
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
} 