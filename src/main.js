import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import Vue3Marquee from 'vue3-marquee'

// Import des styles
import './styles.css'

// Initialisation du dataLayer pour Google Tag Manager
window.dataLayer = window.dataLayer || []

// Configuration du router pour suivre les changements de page
router.beforeEach((to, from, next) => {
  // Mettre à jour le titre de la page
  if (to.meta.title) {
    document.title = `${to.meta.title} | TheDevImpact`
  } else {
    document.title = 'TheDevImpact - Sites Web Uniques'
  }
  next()
})

router.afterEach((to) => {
  // Envoi d'un événement de changement de page au dataLayer pour GTM
  window.dataLayer.push({
    event: 'pageview',
    page: {
      path: to.path,
      title: document.title
    }
  })

  // Scroll vers le haut à chaque changement de page (sauf si hash)
  if (!to.hash) {
    window.scrollTo(0, 0)
  }

  // Google Analytics est configuré via Google Tag Manager
})

// Création de l'application
const app = createApp(App)

// Création du store Pinia
const pinia = createPinia()

// Configuration des plugins
app.use(router)
app.use(pinia)
app.use(Vue3Marquee)

// Configuration globale
app.config.globalProperties.$formatPrice = (price) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}

// Fonction globale pour formater les dates
app.config.globalProperties.$formatDate = (date) => {
  return new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date))
}

// Configuration pour la production
app.config.performance = true

// Gestion globale des erreurs
app.config.errorHandler = (err, vm, info) => {
  console.error('Erreur Vue:', err)
  console.error('Composant:', vm)
  console.error('Info:', info)
  
  // En production, on pourrait envoyer les erreurs à un service de monitoring
  if (import.meta.env.PROD) {
    // Exemple: Sentry, LogRocket, etc.
    window.dataLayer?.push({
      event: 'vue_error',
      error: {
        message: err.message,
        component: vm?.$options.name || 'Unknown',
        info: info
      }
    })
  }
}

// Directive globale pour lazy loading des images
app.directive('lazy', {
  mounted(el, binding) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          el.src = binding.value
          el.classList.remove('lazy')
          observer.unobserve(el)
        }
      })
    })
    observer.observe(el)
  }
})

// Montage de l'application
app.mount('#app')

