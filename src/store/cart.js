import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useCartStore = defineStore('cart', () => {
  // État
  const items = ref([])
  const isOpen = ref(false)

  // Getters
  const itemsCount = computed(() => {
    return items.value.reduce((total, item) => total + item.quantity, 0)
  })

  const subtotal = computed(() => {
    return items.value.reduce((total, item) => total + (item.price * item.quantity), 0)
  })

  const tax = computed(() => {
    return subtotal.value * 0.20 // TVA 20%
  })

  const total = computed(() => {
    return subtotal.value + tax.value
  })

  const isEmpty = computed(() => {
    return items.value.length === 0
  })

  // Vérifier si un produit est dans le panier
  const isInCart = computed(() => {
    return (productId) => {
      return items.value.some(item => item.id === productId)
    }
  })

  // Obtenir la quantité d'un produit spécifique
  const getItemQuantity = computed(() => {
    return (productId) => {
      const item = items.value.find(item => item.id === productId)
      return item ? item.quantity : 0
    }
  })

  // Actions
  function addItem(product, quantity = 1) {
    try {
      // Validation du produit
      if (!product || !product.id || !product.name || !product.price) {
        throw new Error('Produit invalide')
      }

      // Validation de la quantité
      if (quantity <= 0) {
        throw new Error('La quantité doit être supérieure à 0')
      }

      const existingItem = items.value.find(item => item.id === product.id)
      
      if (existingItem) {
        existingItem.quantity += quantity
      } else {
        items.value.push({
          id: product.id,
          name: product.name,
          price: product.price,
          image: product.images?.[0] || '/placeholder-image.jpg',
          category: product.category || 'Non catégorisé',
          quantity: quantity
        })
      }
      
      // Ouvrir le panier automatiquement
      isOpen.value = true
      
      // Déclencher une notification
      window.dispatchEvent(new CustomEvent('show-toast', {
        detail: {
          type: 'success',
          title: 'Produit ajouté au panier !',
          message: `${product.name} a été ajouté à votre panier`
        }
      }))
      
      // Sauvegarder dans localStorage
      saveToLocalStorage()
      
      return true
    } catch (error) {
      console.error('Erreur lors de l\'ajout au panier:', error)
      window.dispatchEvent(new CustomEvent('show-toast', {
        detail: {
          type: 'error',
          title: 'Erreur',
          message: error.message || 'Impossible d\'ajouter le produit au panier'
        }
      }))
      return false
    }
  }

  function addAdditionalOption(option, quantity = 1) {
    try {
      // Validation de l'option
      if (!option || !option.id || !option.name) {
        throw new Error('Option invalide')
      }

      // Pour les options "sur devis", on ne peut pas les ajouter directement
      if (option.priceValue === 0) {
        window.dispatchEvent(new CustomEvent('show-toast', {
          detail: {
            type: 'info',
            title: 'Devis nécessaire',
            message: `${option.name} nécessite un devis personnalisé. Contactez-nous pour plus d'informations.`
          }
        }))
        return false
      }

      // Validation de la quantité
      if (quantity <= 0) {
        throw new Error('La quantité doit être supérieure à 0')
      }

      const existingItem = items.value.find(item => item.id === option.id)
      
      if (existingItem) {
        existingItem.quantity += quantity
      } else {
        items.value.push({
          id: option.id,
          name: option.name,
          price: option.priceValue,
          image: null, // Image générée avec CSS pour les options
          category: option.category || 'Option',
          quantity: quantity,
          isAdditionalOption: true,
          additionalDelay: option.additionalDelay,
          description: option.description
        })
      }
      
      // Ouvrir le panier automatiquement
      isOpen.value = true
      
      // Déclencher une notification
      window.dispatchEvent(new CustomEvent('show-toast', {
        detail: {
          type: 'success',
          title: 'Option ajoutée au panier !',
          message: `${option.name} a été ajouté à votre panier`
        }
      }))
      
      // Sauvegarder dans localStorage
      saveToLocalStorage()
      
      return true
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'option:', error)
      window.dispatchEvent(new CustomEvent('show-toast', {
        detail: {
          type: 'error',
          title: 'Erreur',
          message: error.message || 'Impossible d\'ajouter l\'option au panier'
        }
      }))
      return false
    }
  }

  function removeItem(productId) {
    try {
      const index = items.value.findIndex(item => item.id === productId)
      if (index > -1) {
        items.value.splice(index, 1)
        saveToLocalStorage()
        return true
      }
      return false
    } catch (error) {
      console.error('Erreur lors de la suppression:', error)
      return false
    }
  }

  function updateQuantity(productId, quantity) {
    try {
      const item = items.value.find(item => item.id === productId)
      if (item) {
        if (quantity <= 0) {
          removeItem(productId)
        } else {
          item.quantity = quantity
          saveToLocalStorage()
        }
        return true
      }
      return false
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la quantité:', error)
      return false
    }
  }

  function clearCart() {
    try {
      items.value = []
      saveToLocalStorage()
      return true
    } catch (error) {
      console.error('Erreur lors du vidage du panier:', error)
      return false
    }
  }

  function toggleCart() {
    isOpen.value = !isOpen.value
  }

  function closeCart() {
    isOpen.value = false
  }

  function openCart() {
    isOpen.value = true
  }

  // Persistance localStorage avec gestion d'erreurs
  function saveToLocalStorage() {
    try {
      localStorage.setItem('thedevimpact-cart', JSON.stringify(items.value))
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du panier:', error)
    }
  }

  function loadFromLocalStorage() {
    try {
      const saved = localStorage.getItem('thedevimpact-cart')
      if (saved) {
        const parsedItems = JSON.parse(saved)
        // Validation des données chargées
        if (Array.isArray(parsedItems)) {
          items.value = parsedItems.filter(item => 
            item && 
            item.id && 
            item.name && 
            typeof item.price === 'number' && 
            typeof item.quantity === 'number' && 
            item.quantity > 0
          )
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement du panier:', error)
      // En cas d'erreur, on repart avec un panier vide
      items.value = []
    }
  }

  // Obtenir les données du panier pour Stripe
  function getCheckoutData() {
    return {
      items: items.value.map(item => ({
        id: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        image: item.image,
        category: item.category
      })),
      subtotal: subtotal.value,
      tax: tax.value,
      total: total.value,
      itemsCount: itemsCount.value
    }
  }

  // Initialiser depuis localStorage
  loadFromLocalStorage()

  return {
    // État
    items,
    isOpen,
    // Getters
    itemsCount,
    subtotal,
    tax,
    total,
    isEmpty,
    isInCart,
    getItemQuantity,
    // Actions
    addItem,
    addAdditionalOption,
    removeItem,
    updateQuantity,
    clearCart,
    toggleCart,
    closeCart,
    openCart,
    loadFromLocalStorage,
    getCheckoutData
  }
}) 