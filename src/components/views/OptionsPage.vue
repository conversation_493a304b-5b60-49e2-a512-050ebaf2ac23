<script setup>
import { ref } from 'vue'
import Navbar from "../TDI-modules/Navbar.vue"
import Footer from "../TDI-modules/Footer.vue"
import ContactModal from "../TDI-modules/ContactModal.vue"
import { 
  WrenchScrewdriverIcon, 
  PaintBrushIcon, 
  DocumentPlusIcon, 
  GlobeAltIcon, 
  EnvelopeIcon,
  CogIcon
} from "@heroicons/vue/24/outline"

const isContactModalOpen = ref(false)

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true
}

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false
}

// Options spécifiques extraites du composant Options.vue
const optionsData = [
  {
    title: "Modifications ponctuelles",
    description: "Besoin de modifications sur un site que nous avons déjà réalisé ? Nous vous proposons un service de modifications ponctuelles, rapide et sur mesure.",
    price: "Sur devis",
    icon: WrenchScrewdriverIcon,
    detail: "Idéal pour mettre à jour votre contenu, ajuster une section, ou intégrer un nouvel élément.",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Modification de textes et images",
      "Ajustement de sections existantes",
      "Intégration de nouveaux éléments",
      "Optimisation mineure"
    ]
  },
  {
    title: "Visuels personnalisés",
    description: "Logo, bannière, illustration, visuel digital… Création sur-mesure adaptée à votre identité et à vos supports.",
    price: "+120€ / visuel",
    icon: PaintBrushIcon,
    detail: "Idéal pour renforcer votre image de marque avec des éléments graphiques cohérents et professionnels.",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Logo professionnel",
      "Bannières et headers",
      "Illustrations personnalisées",
      "Éléments graphiques cohérents"
    ]
  },
  {
    title: "Page supplémentaire",
    description: "Besoin d'ajouter une page à votre site (ex : blog, galerie, mentions légales…) ?",
    price: "Sur devis",
    icon: DocumentPlusIcon,
    detail: "Chaque page comprend son design personnalisé et une optimisation de base pour le SEO.",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Design personnalisé",
      "Optimisation SEO de base",
      "Intégration responsive",
      "Navigation mise à jour"
    ]
  },
  {
    title: "Maintenance",
    description: "Assurez la stabilité de votre site en continu.",
    price: "+180€",
    icon: CogIcon,
    detail: "Service de maintenance pour garantir le bon fonctionnement de votre site web.",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Corrections de bugs",
      "Ajustements mineurs (textes, images)",
      "Mises à jour de sécurité",
      "Optimisation SEO continue"
    ]
  },
  {
    title: "Nom de domaine personnalisé",
    description: "www.nom-entreprise.com, c'est plus professionnel et rassurant pour vos clients.",
    price: "+150€",
    icon: GlobeAltIcon,
    detail: "Nous nous occupons de la configuration. Le nom de domaine est à acheter séparément (comptez environ 12 €/an).",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Configuration complète",
      "Redirection automatique",
      "Certificat SSL inclus",
      "Support technique"
    ]
  },
  {
    title: "Adresse e-mail professionnelle",
    description: "<EMAIL> pour une communication professionnelle.",
    price: "+80€",
    icon: EnvelopeIcon,
    detail: "Configuration d'une adresse e-mail professionnelle avec votre nom de domaine.",
    delay: "Délai supplémentaire de 2 jours",
    features: [
      "Configuration e-mail",
      "Interface webmail",
      "Synchronisation mobile",
      "Support technique"
    ]
  }
]
</script>

<template>
  <div class="bg-black min-h-screen">
    <Navbar />

    <!-- Hero Section -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Background gradient -->
      <div class="absolute inset-0 bg-gradient-to-br from-b1 via-b2 to-b3"></div>
      
      <!-- Animated shapes -->
      <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-b3/20 to-b6/20 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-b4/20 to-b5/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <!-- Content -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/20 to-b6/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8">
          <span class="w-2 h-2 rounded-full bg-b3 mr-2 animate-pulse"></span>
          Options & Modules
          <span class="w-2 h-2 rounded-full bg-b6 ml-2 animate-pulse"></span>
        </div>

        <!-- Title -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
          Services
          <span class="block bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">
            complémentaires
          </span>
        </h1>

        <!-- Subtitle -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-gray-300 mb-10 leading-relaxed">
          Personnalisez votre projet avec nos options et modules pour répondre 
          <span class="text-b4 font-semibold">parfaitement à vos besoins</span>
        </p>

        <!-- CTA -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a 
            href="#options" 
            class="inline-flex items-center px-8 py-4 rounded-xl bg-gradient-to-r from-b3 to-b6 text-white font-semibold text-lg hover:shadow-2xl hover:shadow-b3/25 transform hover:-translate-y-1 transition-all duration-300"
          >
            Découvrir les options
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
          </a>
          <button 
            @click="openContactModal"
            class="inline-flex items-center px-8 py-4 rounded-xl border-2 border-white/20 text-white font-semibold text-lg hover:bg-white/10 hover:border-white/40 transition-all duration-300"
          >
            Demander un devis
          </button>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div id="options" class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>

    <!-- Options Section -->
    <section class="relative py-20 bg-gradient-to-b from-black to-b1">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">
            <span class="bg-gradient-to-r from-b3 via-b4 to-b6 text-transparent bg-clip-text">
              Nos options disponibles
            </span>
          </h2>
          <p class="text-white/70 text-lg max-w-3xl mx-auto leading-relaxed">
            Chaque option peut être ajoutée à votre projet pour l'adapter parfaitement à vos besoins spécifiques.
          </p>
        </div>

        <!-- Options Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div
            v-for="(option, index) in optionsData"
            :key="index"
            class="group relative bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden transition-all duration-500 hover:bg-white/10 hover:border-white/20 hover:scale-[1.02]"
          >
            <!-- Top accent line -->
            <div class="h-1 w-full bg-gradient-to-r from-b3 via-b4 to-b6"></div>

            <!-- Content -->
            <div class="p-6">
              <!-- Header with icon and title -->
              <div class="flex items-start mb-4">
                <div class="flex-shrink-0 w-12 h-12 rounded-xl bg-gradient-to-br from-white/20 to-white/10 flex items-center justify-center mr-4 border border-white/20">
                  <component :is="option.icon" class="h-6 w-6 text-white" />
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="text-lg font-bold text-white mb-2 leading-tight">
                    {{ option.title }}
                  </h3>
                  <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-b3/20 to-b6/20 text-white border border-white/20">
                    {{ option.price }}
                  </div>
                </div>
              </div>

              <!-- Description -->
              <p class="text-white/80 text-sm leading-relaxed mb-4">
                {{ option.description }}
              </p>

              <!-- Features list -->
              <div class="mb-4 space-y-2">
                <div
                  v-for="(feature, fIndex) in option.features"
                  :key="fIndex"
                  class="flex items-center text-sm"
                >
                  <div class="flex-shrink-0 w-5 h-5 rounded-full bg-b3 flex items-center justify-center mr-3">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <span class="text-white/70">{{ feature }}</span>
                </div>
              </div>

              <!-- Detail section -->
              <div class="mb-4 p-3 rounded-xl bg-white/5 border border-white/10">
                <p class="text-white/60 text-sm leading-relaxed italic">
                  {{ option.detail }}
                </p>
              </div>

              <!-- Delay info -->
              <div class="mb-4 flex items-center text-white/50 text-xs">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{{ option.delay }}</span>
              </div>

              <!-- CTA Button -->
              <button 
                @click="openContactModal"
                class="w-full mt-4 px-4 py-3 bg-gradient-to-r from-b3 to-b6 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-b3/25 transform hover:-translate-y-0.5 transition-all duration-300"
              >
                Demander cette option
              </button>
            </div>
          </div>
        </div>

        <!-- Bottom CTA -->
        <div class="text-center mt-16">
          <div class="bg-gradient-to-r from-b3/10 to-b6/10 backdrop-blur-sm rounded-2xl border border-white/10 p-8">
            <h3 class="text-2xl font-bold text-white mb-4">
              Besoin d'une option personnalisée ?
            </h3>
            <p class="text-white/70 mb-6 max-w-2xl mx-auto">
              Nous pouvons créer des solutions sur-mesure pour répondre à vos besoins spécifiques. 
              Contactez-nous pour discuter de votre projet.
            </p>
            <button 
              @click="openContactModal"
              class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-b3 to-b6 text-white font-semibold rounded-xl hover:shadow-2xl hover:shadow-b3/25 transform hover:-translate-y-1 transition-all duration-300"
            >
              Nous contacter
            </button>
          </div>
        </div>
      </div>
    </section>

    <Footer />
    
    <!-- Contact Modal -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<style scoped>
/* Animations personnalisées */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style>
