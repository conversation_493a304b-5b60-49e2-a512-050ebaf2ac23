<script setup>
import { ref, onMounted } from 'vue'
import Navbar from "../TDI-modules/Navbar.vue"
import Footer from "../TDI-modules/Footer.vue"
import ContactModal from "../TDI-modules/ContactModal.vue"
// Les icônes sont maintenant utilisées via FontAwesome dans le template

const isContactModalOpen = ref(false)
const heroVisible = ref(false)
const textVisible = ref(false)

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true
}

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false
}

// Animations au montage
onMounted(() => {
  setTimeout(() => {
    heroVisible.value = true
  }, 300)

  setTimeout(() => {
    textVisible.value = true
  }, 600)
})

// Les options sont maintenant codées directement dans le template pour un meilleur contrôle du design
</script>

<template>
  <div class="bg-black min-h-screen">
    <Navbar />

    <!-- Hero Section -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Image de fond -->
      <div class="absolute inset-0">
        <img
          src="../../assets/branding.webp"
          alt="Options et modules personnalisés"
          class="w-full h-full object-cover"
        />
        <!-- Overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-b from-blue-600/60 via-black/75 to-black"></div>
      </div>

      <!-- Contenu -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8 opacity-0" :class="{ 'animate-fade-in': heroVisible }">
          <span class="w-2 h-2 rounded-full bg-blue-400 mr-2 animate-pulse"></span>
          Options & Modules
          <span class="w-2 h-2 rounded-full bg-cyan-400 ml-2 animate-pulse"></span>
        </div>

        <!-- Titre principal -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Personnalisez
          <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">
            votre projet
          </span>
        </h1>

        <!-- Sous-titre -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-white/80 mb-10 leading-relaxed opacity-0" :class="{ 'animate-fade-in-delayed-2': textVisible }">
          Des options sur-mesure pour adapter votre site web à vos besoins spécifiques
        </p>

        <!-- Boutons CTA -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center opacity-0" :class="{ 'animate-fade-in-delayed-3': textVisible }">
          <a
            href="#options"
            class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-2xl hover:shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1 transition-all duration-300"
          >
            Découvrir les options
            <i class="fas fa-arrow-down ml-2"></i>
          </a>
          <button
            @click="openContactModal"
            class="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-2xl hover:bg-white/10 hover:border-white/50 transition-all duration-300"
          >
            <i class="fas fa-envelope mr-2"></i>
            Demander un devis
          </button>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>

    <!-- Options Section -->
    <section id="options" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
            Options sur-mesure
            <span class="w-2 h-2 rounded-full bg-cyan-500 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Adaptez votre site
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600">
              à vos besoins précis
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Chaque option peut être ajoutée à votre projet pour créer une solution parfaitement adaptée
          </p>
        </div>

        <!-- Grille des options -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <!-- Modifications ponctuelles -->
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-wrench text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Modifications ponctuelles</h3>
            <p class="text-gray-600 mb-4">Besoin de modifications sur un site que nous avons déjà réalisé ? Service rapide et sur mesure.</p>
            <ul class="text-sm text-gray-500 space-y-2 mb-4">
              <li>✓ Modification de textes et images</li>
              <li>✓ Ajustement de sections existantes</li>
              <li>✓ Intégration de nouveaux éléments</li>
              <li>✓ Optimisation mineure</li>
            </ul>
            <div class="bg-blue-50 rounded-xl p-3 mb-4">
              <p class="text-blue-700 text-sm font-medium">Sur devis</p>
              <p class="text-blue-600 text-xs">Délai supplémentaire de 4 jours</p>
            </div>
          </div>

          <!-- Visuels personnalisés -->
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-palette text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Visuels personnalisés</h3>
            <p class="text-gray-600 mb-4">Logo, bannière, illustration... Création sur-mesure adaptée à votre identité visuelle.</p>
            <ul class="text-sm text-gray-500 space-y-2 mb-4">
              <li>✓ Logo professionnel</li>
              <li>✓ Bannières et headers</li>
              <li>✓ Illustrations personnalisées</li>
              <li>✓ Éléments graphiques cohérents</li>
            </ul>
            <div class="bg-purple-50 rounded-xl p-3 mb-4">
              <p class="text-purple-700 text-sm font-medium">+120€ / visuel</p>
              <p class="text-purple-600 text-xs">Délai supplémentaire de 4 jours</p>
            </div>
          </div>

          <!-- Page supplémentaire -->
          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-plus text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Page supplémentaire</h3>
            <p class="text-gray-600 mb-4">Ajout d'une page à votre site (blog, galerie, mentions légales...).</p>
            <ul class="text-sm text-gray-500 space-y-2 mb-4">
              <li>✓ Design personnalisé</li>
              <li>✓ Optimisation SEO de base</li>
              <li>✓ Intégration responsive</li>
              <li>✓ Navigation mise à jour</li>
            </ul>
            <div class="bg-green-50 rounded-xl p-3 mb-4">
              <p class="text-green-700 text-sm font-medium">Sur devis</p>
              <p class="text-green-600 text-xs">Délai supplémentaire de 4 jours</p>
            </div>
          </div>

          <!-- Maintenance -->
          <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-8 border border-orange-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-cog text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Maintenance</h3>
            <p class="text-gray-600 mb-4">Assurez la stabilité et les performances de votre site en continu.</p>
            <ul class="text-sm text-gray-500 space-y-2 mb-4">
              <li>✓ Corrections de bugs</li>
              <li>✓ Ajustements mineurs (textes, images)</li>
              <li>✓ Mises à jour de sécurité</li>
              <li>✓ Optimisation SEO continue</li>
            </ul>
            <div class="bg-orange-50 rounded-xl p-3 mb-4">
              <p class="text-orange-700 text-sm font-medium">+180€</p>
              <p class="text-orange-600 text-xs">Délai supplémentaire de 4 jours</p>
            </div>
          </div>

          <!-- Nom de domaine personnalisé -->
          <div class="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-2xl p-8 border border-cyan-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-globe text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Nom de domaine personnalisé</h3>
            <p class="text-gray-600 mb-4">www.nom-entreprise.com, c'est plus professionnel et rassurant pour vos clients.</p>
            <ul class="text-sm text-gray-500 space-y-2 mb-4">
              <li>✓ Configuration complète</li>
              <li>✓ Redirection automatique</li>
              <li>✓ Certificat SSL inclus</li>
              <li>✓ Support technique</li>
            </ul>
            <div class="bg-cyan-50 rounded-xl p-3 mb-4">
              <p class="text-cyan-700 text-sm font-medium">+150€</p>
              <p class="text-cyan-600 text-xs">Délai supplémentaire de 4 jours</p>
            </div>
          </div>

          <!-- Adresse e-mail professionnelle -->
          <div class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-8 border border-indigo-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-envelope text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Adresse e-mail professionnelle</h3>
            <p class="text-gray-600 mb-4"><EMAIL> pour une communication professionnelle.</p>
            <ul class="text-sm text-gray-500 space-y-2 mb-4">
              <li>✓ Configuration e-mail</li>
              <li>✓ Interface webmail</li>
              <li>✓ Synchronisation mobile</li>
              <li>✓ Support technique</li>
            </ul>
            <div class="bg-indigo-50 rounded-xl p-3 mb-4">
              <p class="text-indigo-700 text-sm font-medium">+80€</p>
              <p class="text-indigo-600 text-xs">Délai supplémentaire de 2 jours</p>
            </div>
          </div>
        </div>

        <!-- Section CTA finale -->
        <div class="text-center">
          <div class="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl border border-blue-100 p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
              Besoin d'une option personnalisée ?
            </h3>
            <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
              Nous pouvons créer des solutions sur-mesure pour répondre à vos besoins spécifiques.
              Contactez-nous pour discuter de votre projet.
            </p>
            <button
              @click="openContactModal"
              class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-xl hover:shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1 transition-all duration-300"
            >
              <i class="fas fa-envelope mr-2"></i>
              Nous contacter
            </button>
          </div>
        </div>
      </div>
    </section>

    <Footer />
    
    <!-- Contact Modal -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<style scoped>
/* Animations personnalisées */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.8s ease-out 0.2s forwards;
}

.animate-fade-in-delayed-2 {
  animation: fadeIn 0.8s ease-out 0.4s forwards;
}

.animate-fade-in-delayed-3 {
  animation: fadeIn 0.8s ease-out 0.6s forwards;
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style>
