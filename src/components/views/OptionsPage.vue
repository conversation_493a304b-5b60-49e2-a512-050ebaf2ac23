<script setup>
import { ref, onMounted } from 'vue'
import Navbar from "../TDI-modules/Navbar.vue"
import Footer from "../TDI-modules/Footer.vue"
import ContactModal from "../TDI-modules/ContactModal.vue"
import CarouselTop from "../TDI-modules/CarouselTop.vue"
import {
  WrenchScrewdriverIcon,
  PaintBrushIcon,
  DocumentPlusIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  CogIcon
} from "@heroicons/vue/24/outline"

const isContactModalOpen = ref(false)
const heroVisible = ref(false)
const textVisible = ref(false)

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true
}

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false
}

// Animations au montage
onMounted(() => {
  setTimeout(() => {
    heroVisible.value = true
  }, 300)

  setTimeout(() => {
    textVisible.value = true
  }, 600)
})

// Options spécifiques extraites du composant Options.vue
const optionsData = [
  {
    title: "Modifications ponctuelles",
    description: "Besoin de modifications sur un site que nous avons déjà réalisé ? Nous vous proposons un service de modifications ponctuelles, rapide et sur mesure.",
    price: "Sur devis",
    icon: WrenchScrewdriverIcon,
    detail: "Idéal pour mettre à jour votre contenu, ajuster une section, ou intégrer un nouvel élément.",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Modification de textes et images",
      "Ajustement de sections existantes",
      "Intégration de nouveaux éléments",
      "Optimisation mineure"
    ]
  },
  {
    title: "Visuels personnalisés",
    description: "Logo, bannière, illustration, visuel digital… Création sur-mesure adaptée à votre identité et à vos supports.",
    price: "+120€ / visuel",
    icon: PaintBrushIcon,
    detail: "Idéal pour renforcer votre image de marque avec des éléments graphiques cohérents et professionnels.",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Logo professionnel",
      "Bannières et headers",
      "Illustrations personnalisées",
      "Éléments graphiques cohérents"
    ]
  },
  {
    title: "Page supplémentaire",
    description: "Besoin d'ajouter une page à votre site (ex : blog, galerie, mentions légales…) ?",
    price: "Sur devis",
    icon: DocumentPlusIcon,
    detail: "Chaque page comprend son design personnalisé et une optimisation de base pour le SEO.",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Design personnalisé",
      "Optimisation SEO de base",
      "Intégration responsive",
      "Navigation mise à jour"
    ]
  },
  {
    title: "Maintenance",
    description: "Assurez la stabilité de votre site en continu.",
    price: "+180€",
    icon: CogIcon,
    detail: "Service de maintenance pour garantir le bon fonctionnement de votre site web.",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Corrections de bugs",
      "Ajustements mineurs (textes, images)",
      "Mises à jour de sécurité",
      "Optimisation SEO continue"
    ]
  },
  {
    title: "Nom de domaine personnalisé",
    description: "www.nom-entreprise.com, c'est plus professionnel et rassurant pour vos clients.",
    price: "+150€",
    icon: GlobeAltIcon,
    detail: "Nous nous occupons de la configuration. Le nom de domaine est à acheter séparément (comptez environ 12 €/an).",
    delay: "Délai supplémentaire de 4 jours",
    features: [
      "Configuration complète",
      "Redirection automatique",
      "Certificat SSL inclus",
      "Support technique"
    ]
  },
  {
    title: "Adresse e-mail professionnelle",
    description: "<EMAIL> pour une communication professionnelle.",
    price: "+80€",
    icon: EnvelopeIcon,
    detail: "Configuration d'une adresse e-mail professionnelle avec votre nom de domaine.",
    delay: "Délai supplémentaire de 2 jours",
    features: [
      "Configuration e-mail",
      "Interface webmail",
      "Synchronisation mobile",
      "Support technique"
    ]
  }
]
</script>

<template>
  <div class="bg-black min-h-screen">
    <CarouselTop />
    <Navbar />

    <!-- Hero Section -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Image de fond -->
      <div class="absolute inset-0">
        <img
          src="../../assets/branding.webp"
          alt="Options et modules personnalisés"
          class="w-full h-full object-cover"
        />
        <!-- Overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-b from-blue-600/60 via-black/75 to-black"></div>
      </div>

      <!-- Contenu -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8 opacity-0" :class="{ 'animate-fade-in': heroVisible }">
          <span class="w-2 h-2 rounded-full bg-blue-400 mr-2 animate-pulse"></span>
          Options & Modules
          <span class="w-2 h-2 rounded-full bg-cyan-400 ml-2 animate-pulse"></span>
        </div>

        <!-- Titre principal -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Personnalisez
          <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">
            votre projet
          </span>
        </h1>

        <!-- Sous-titre -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-white/80 mb-10 leading-relaxed opacity-0" :class="{ 'animate-fade-in-delayed-2': textVisible }">
          Des options sur-mesure pour adapter votre site web à vos besoins spécifiques
        </p>

        <!-- Boutons CTA -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center opacity-0" :class="{ 'animate-fade-in-delayed-3': textVisible }">
          <a
            href="#options"
            class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-2xl hover:shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1 transition-all duration-300"
          >
            Découvrir les options
            <i class="fas fa-arrow-down ml-2"></i>
          </a>
          <button
            @click="openContactModal"
            class="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-2xl hover:bg-white/10 hover:border-white/50 transition-all duration-300"
          >
            <i class="fas fa-envelope mr-2"></i>
            Demander un devis
          </button>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>

    <!-- Options Section -->
    <section id="options" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
            Options sur-mesure
            <span class="w-2 h-2 rounded-full bg-cyan-500 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Adaptez votre site
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600">
              à vos besoins précis
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Chaque option peut être ajoutée à votre projet pour créer une solution parfaitement adaptée
          </p>
        </div>

        <!-- Grille des options -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <div
            v-for="(option, index) in optionsData"
            :key="index"
            class="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl p-8 border border-blue-100 hover:shadow-lg transition-all duration-300"
          >
            <!-- Icône -->
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6">
              <component :is="option.icon" class="h-8 w-8 text-white" />
            </div>

            <!-- Titre et prix -->
            <div class="mb-4">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ option.title }}</h3>
              <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700 border border-blue-200">
                {{ option.price }}
              </div>
            </div>

            <!-- Description -->
            <p class="text-gray-600 mb-4">{{ option.description }}</p>

            <!-- Features list -->
            <ul class="text-sm text-gray-500 space-y-2 mb-4">
              <li
                v-for="(feature, fIndex) in option.features"
                :key="fIndex"
                class="flex items-start"
              >
                <i class="fas fa-check text-blue-500 mr-2 mt-1 text-xs"></i>
                <span>{{ feature }}</span>
              </li>
            </ul>

            <!-- Detail section -->
            <div class="mb-4 p-3 rounded-xl bg-blue-50 border border-blue-100">
              <p class="text-gray-600 text-sm leading-relaxed italic">
                {{ option.detail }}
              </p>
            </div>

            <!-- Delay info -->
            <div class="mb-4 flex items-center text-gray-500 text-xs">
              <i class="fas fa-clock mr-2"></i>
              <span>{{ option.delay }}</span>
            </div>

            <!-- CTA Button -->
            <button
              @click="openContactModal"
              class="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transform hover:-translate-y-0.5 transition-all duration-300"
            >
              Demander cette option
            </button>
          </div>
        </div>

        <!-- Section CTA finale -->
        <div class="text-center">
          <div class="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl border border-blue-100 p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
              Besoin d'une option personnalisée ?
            </h3>
            <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
              Nous pouvons créer des solutions sur-mesure pour répondre à vos besoins spécifiques.
              Contactez-nous pour discuter de votre projet.
            </p>
            <button
              @click="openContactModal"
              class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-xl hover:shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1 transition-all duration-300"
            >
              <i class="fas fa-envelope mr-2"></i>
              Nous contacter
            </button>
          </div>
        </div>
      </div>
    </section>

    <Footer />
    
    <!-- Contact Modal -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<style scoped>
/* Animations personnalisées */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.8s ease-out 0.2s forwards;
}

.animate-fade-in-delayed-2 {
  animation: fadeIn 0.8s ease-out 0.4s forwards;
}

.animate-fade-in-delayed-3 {
  animation: fadeIn 0.8s ease-out 0.6s forwards;
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style>
