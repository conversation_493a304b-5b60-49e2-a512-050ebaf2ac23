<script setup>
import CarouselTop from "../TDI-modules/CarouselTop.vue";
import Navbar from "../TDI-modules/Navbar.vue";
import HeroHome from "../TDI-modules/HeroHome.vue";
import CarouselTitle from "../TDI-modules/CarouselTitle.vue";
import Pricing from "../TDI-modules/Pricing.vue";
import ClientTestimonials from "../TDI-modules/ClientTestimonials.vue";
import Faq from "../TDI-modules/Faq.vue";
import Footer from "../TDI-modules/Footer.vue";
import CitationsCarousel from "../TDI-modules/CitationsCarousel.vue";
import AlternatingFeatures from "../TDI-modules/AlternatingFeatures.vue";
import Options from "../TDI-modules/Options.vue";
import DeuxChoix from "../TDI-modules/DeuxChoix.vue";
</script>

<template>
  <div class="bg-black w-full min-h-screen overflow-x-hidden">
    <CarouselTop />
    <Navbar />

    <HeroHome id="accueil" />
    <DeuxChoix />

    <ClientTestimonials id="temoignages" />

    <AlternatingFeatures id="a-propos" />
    <div
      class="relative overflow-hidden"
      style="
        background-image: url(https://img.daisyui.com/images/stock/photo-1507358522600-9f71e620c44e.webp);
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      "
    >
      <!-- Background Overlay -->
      <div class="absolute inset-0 bg-gradient-to-b from-black/70 to-b1/95">
        <!-- Subtle Pattern -->
        <div
          class="absolute inset-0 opacity-5"
          style="
            background-size: 20px 20px;
            background-image: radial-gradient(
              circle,
              rgba(255, 255, 255, 0.3) 1px,
              transparent 1px
            );
          "
        ></div>
      </div>

      <div></div>
      <!-- Content -->
    </div>
    <div id="faq">
      <Faq />
    </div>
    <CitationsCarousel />
    <div id="contact">
      <Footer />
    </div>
  </div>
</template>
