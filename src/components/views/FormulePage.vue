<script setup>
import Navbar from "../TDI-modules/Navbar.vue";
import Footer from "../TDI-modules/Footer.vue";
import { ref, onMounted } from "vue";
import { PaintBrushIcon, CodeBracketIcon, RocketLaunchIcon } from "@heroicons/vue/24/outline";
import ContactModal from "../TDI-modules/ContactModal.vue";

// Animation de la section hero
const heroVisible = ref(false);
const textVisible = ref(false);
const ctaVisible = ref(false);
const isContactModalOpen = ref(false);

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true;
};

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false;
};

onMounted(() => {
  // Animation séquentielle des éléments comme dans HeroHome
  setTimeout(() => { heroVisible.value = true; }, 50);
  setTimeout(() => { textVisible.value = true; }, 180);
  setTimeout(() => { ctaVisible.value = true; }, 300);
});
</script>

<template>
  <div class="bg-black min-h-screen">
    <Navbar />

    <!-- Section Hero -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Image de fond -->
      <div class="absolute inset-0">
                 <img 
           src="../../assets/branding.webp" 
           alt="Workspace moderne et lumineux"
           class="w-full h-full object-cover"
         />
                 <!-- Overlay gradient pour image claire -->
         <div class="absolute inset-0 bg-gradient-to-b from-b3/60 via-black/75 to-black"></div>
        
      </div>

      <!-- Contenu -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/20 to-b6/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8 opacity-0" :class="{ 'animate-fade-in': heroVisible }">
          <span class="w-2 h-2 rounded-full bg-b3 mr-2 animate-pulse"></span>
          Création sur-mesure
          <span class="w-2 h-2 rounded-full bg-b6 ml-2 animate-pulse"></span>
        </div>

        <!-- Titre principal -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Votre site web
          <span class="block bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">
            sur-mesure
          </span>
          <span class="block text-white">commence ici</span>
        </h1>

        <!-- Sous-titre -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-gray-300 mb-10 leading-relaxed opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Créons ensemble un site web qui vous ressemble, performant et unique, 
          <span class="text-b4 font-semibold">conçu pour faire grandir votre business</span>
        </p>

        <!-- Features rapides avec design amélioré -->
        <div class="flex flex-wrap justify-center gap-4 mb-12 opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <PaintBrushIcon class="w-5 h-5 text-b3" />
            <span class="text-sm font-medium text-white">Design unique</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <CodeBracketIcon class="w-5 h-5 text-b4" />
            <span class="text-sm font-medium text-white">Code sur-mesure</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <RocketLaunchIcon class="w-5 h-5 text-b6" />
            <span class="text-sm font-medium text-white">Performance optimale</span>
          </div>
        </div>

        <!-- CTA améliorés -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center opacity-0" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
          <a
            href="#services"
            class="inline-flex items-center px-8 py-4 rounded-2xl bg-gradient-to-r from-b3 to-b6 text-white font-semibold text-lg hover:shadow-2xl hover:shadow-b3/25 transform hover:-translate-y-1 transition-all duration-300"
          >
            <i class="fas fa-rocket mr-2"></i>
            Découvrir nos services
            <i class="fas fa-arrow-down ml-2"></i>
          </a>
          <button
            @click="openContactModal"
            class="inline-flex items-center px-8 py-4 rounded-2xl border-2 border-white/30 text-white font-semibold text-lg hover:bg-white/10 hover:border-white/50 backdrop-blur-sm transition-all duration-300"
          >
            <i class="fas fa-envelope mr-2"></i>
            Parlons de votre projet
          </button>
        </div>
      </div>

      <!-- Scroll indicator amélioré -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce opacity-0" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center backdrop-blur-sm">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
        <p class="text-white/60 text-xs mt-2 text-center">Découvrir</p>
      </div>
    </section>





    <!-- Section Processus et Avantages -->
    <section class="w-full bg-gradient-to-b from-white to-gray-100 py-10 md:py-24 overflow-hidden relative">
      <!-- Éléments décoratifs d'arrière-plan -->
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div class="absolute top-10 left-10 w-64 h-64 bg-b4/10 rounded-full filter blur-3xl"></div>
        <div class="absolute bottom-10 right-10 w-80 h-80 bg-b3/10 rounded-full filter blur-3xl"></div>
        <div class="absolute top-1/3 right-1/4 w-40 h-40 bg-b6/5 rounded-full filter blur-2xl"></div>

        <!-- Grille décorative -->
        <div
          class="absolute inset-0 opacity-[0.04]"
          style="
            background-image: linear-gradient(#57737a 1px, transparent 1px),
              linear-gradient(to right, #57737a 1px, transparent 1px);
            background-size: 40px 40px;
          "
        ></div>
      </div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- En-tête de section -->
        <header class="text-center mb-16 md:mb-24">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
            Notre approche
            <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            <i class="fas fa-lightbulb text-b3 mr-3"></i>Pourquoi choisir
            <span class="relative inline-block">
              <span class="relative z-10 bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">notre création sur mesure</span>
              <span class="absolute bottom-2 left-0 w-full h-3 bg-b3/20 -rotate-1 z-0"></span>
            </span>
            <span> ?</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Parce que votre activité mérite mieux qu'un site générique.<br>
            Nous créons des sites vitrines uniques, pensés pour refléter votre identité, atteindre vos objectifs, et vous démarquer dès la première visite.
          </p>
        </header>

        <!-- Blocs alternés -->
        <div class="space-y-16 md:space-y-32">
          <!-- Bloc 1: Processus clair et humain -->
          <article class="flex flex-col md:flex-row items-center gap-6 md:gap-8 lg:gap-16 relative">
            <!-- Élément décoratif -->
            <div class="absolute -left-4 top-1/4 w-8 h-24 bg-gradient-to-b from-b3/30 to-b4/30 rounded-r-lg hidden md:block opacity-50"></div>

            <!-- Contenu texte -->
            <div class="w-full md:w-1/2 space-y-6">
              <div class="inline-flex items-center px-3 py-1 rounded-full bg-b3/10 text-b3 text-sm font-medium">
                <i class="fas fa-compass mr-2"></i>Processus
              </div>
              <h3 class="text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
                Un processus clair,
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-b3 to-b6">
                  itératif et humain
                </span>
              </h3>
              <p class="text-lg text-gray-600 leading-relaxed">
                Nous ne travaillons pas avec des maquettes figées.<br>
                Notre méthode repose sur l'écoute, l'adaptation et l'efficacité :
              </p>

              <!-- Liste des étapes -->
              <div class="space-y-4">
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 rounded-full bg-b3/30 flex items-center justify-center text-white font-bold text-sm mt-1">1</div>
                  <div>
                    <h4 class="font-semibold text-gray-900">Analyse de vos besoins</h4>
                    <p class="text-gray-600 text-sm">Compréhension de votre activité, de vos objectifs et de vos préférences.</p>
                  </div>
                </div>
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 rounded-full bg-b3/40 flex items-center justify-center text-white font-bold text-sm mt-1">2</div>
                  <div>
                    <h4 class="font-semibold text-gray-900">Première version du site</h4>
                    <p class="text-gray-600 text-sm">Une base concrète pour visualiser le rendu et affiner ensemble.</p>
                  </div>
                </div>
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 rounded-full bg-b3/50 flex items-center justify-center text-white font-bold text-sm mt-1">3</div>
                  <div>
                    <h4 class="font-semibold text-gray-900">Itérations sur le design</h4>
                    <p class="text-gray-600 text-sm">Ajustements selon vos retours jusqu'à validation complète.</p>
                  </div>
                </div>
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 rounded-full bg-b3/60 flex items-center justify-center text-white font-bold text-sm mt-1">4</div>
                  <div>
                    <h4 class="font-semibold text-gray-900">Optimisation technique</h4>
                    <p class="text-gray-600 text-sm">Travail du SEO, responsive design, refonte du code et nettoyage final.</p>
                  </div>
                </div>
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 rounded-full bg-b3/70 flex items-center justify-center text-white font-bold text-sm mt-1">5</div>
                  <div>
                    <h4 class="font-semibold text-gray-900">Mise en ligne</h4>
                    <p class="text-gray-600 text-sm">Site hébergé sur Netlify, lien de consultation fourni et accès complet au code source.</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Conteneur image -->
            <div class="w-full md:w-1/2 relative group">
              <div class="absolute -top-6 -right-6 w-24 h-24 bg-b3/10 rounded-full blur-xl z-0"></div>
              <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-b4/10 rounded-full blur-lg z-0"></div>

              <div class="relative z-10 overflow-hidden rounded-2xl shadow-2xl transform transition-all duration-500 group-hover:scale-[1.02]">
                <img
                  src="../../assets/UI-UX.png"
                  alt="Processus de création sur-mesure"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                />
                <div class="absolute inset-0 bg-gradient-to-br from-b3/10 to-transparent mix-blend-overlay"></div>
              </div>
            </div>
          </article>

          <!-- Bloc 2: Design fidèle à votre image -->
          <article class="flex flex-col md:flex-row-reverse items-center gap-6 md:gap-8 lg:gap-16 relative">
            <!-- Élément décoratif -->
            <div class="absolute -right-4 top-1/4 w-8 h-24 bg-gradient-to-b from-b6/30 to-b3/30 rounded-l-lg hidden md:block opacity-50"></div>

            <!-- Contenu texte -->
            <div class="w-full md:w-1/2 space-y-6">
              <div class="inline-flex items-center px-3 py-1 rounded-full bg-b6/10 text-b6 text-sm font-medium">
                <i class="fas fa-palette mr-2"></i>Design
              </div>
              <h3 class="text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
                Un design fidèle
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-b4 to-b6">
                  à votre image
                </span>
              </h3>
              <p class="text-lg text-gray-600 leading-relaxed">
                Chaque site est pensé sur mesure, sans template préfabriqué.<br>
                Notre objectif : traduire votre univers, capter l'attention et inspirer confiance.
              </p>

              <!-- Avantages design -->
              <div class="grid grid-cols-1 gap-4">
                <div class="flex items-center gap-3 p-3 rounded-lg bg-white/50 border border-gray-100">
                  <div class="w-2 h-2 rounded-full bg-b4"></div>
                  <span class="text-gray-700 font-medium">Identité visuelle cohérente et professionnelle</span>
                </div>
                <div class="flex items-center gap-3 p-3 rounded-lg bg-white/50 border border-gray-100">
                  <div class="w-2 h-2 rounded-full bg-b5"></div>
                  <span class="text-gray-700 font-medium">Expérience utilisateur fluide</span>
                </div>
                <div class="flex items-center gap-3 p-3 rounded-lg bg-white/50 border border-gray-100">
                  <div class="w-2 h-2 rounded-full bg-b6"></div>
                  <span class="text-gray-700 font-medium">Site responsive (mobile, tablette, desktop)</span>
                </div>
              </div>
            </div>

            <!-- Conteneur image -->
            <div class="w-full md:w-1/2 relative group">
              <div class="absolute inset-0 bg-gradient-to-br from-b6/20 to-b3/20 rounded-2xl transform -rotate-3 scale-[0.97] transition-all duration-500 group-hover:rotate-0 group-hover:scale-100"></div>

              <div class="relative overflow-hidden rounded-2xl shadow-2xl transform transition-all duration-500 group-hover:rotate-2">
                <img
                  src="../../assets/branding.webp"
                  alt="Design personnalisé et professionnel"
                  class="w-full h-full object-cover transition-all duration-700 group-hover:scale-105 rounded-2xl"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-60 rounded-2xl"></div>
              </div>
            </div>
          </article>

          <!-- Bloc 3: Base technique solide -->
          <article class="flex flex-col md:flex-row items-center gap-6 md:gap-8 lg:gap-16 relative">
            <!-- Élément décoratif -->
            <div class="absolute -left-4 top-1/4 w-8 h-24 bg-gradient-to-b from-b5/30 to-b6/30 rounded-r-lg hidden md:block opacity-50"></div>

            <!-- Contenu texte -->
            <div class="w-full md:w-1/2 space-y-6">
              <div class="inline-flex items-center px-3 py-1 rounded-full bg-b3/10 text-b3 text-sm font-medium">
                <i class="fas fa-laptop-code mr-2"></i>Technique
              </div>
              <h3 class="text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
                Une base technique
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-b4 to-b6">
                  solide et durable
                </span>
              </h3>
              <p class="text-lg text-gray-600 leading-relaxed">
                Nos sites sont statiques, légers et ultra rapides.<br>
                Ils reposent sur un code propre et moderne, facile à maintenir et à faire évoluer.
              </p>

              <!-- Technologies -->
              <div class="grid grid-cols-1 gap-4">
                <div class="flex items-center gap-3 p-3 rounded-lg bg-white/50 border border-gray-100">
                  <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-green-400 to-green-500 flex items-center justify-center">
                    <i class="fab fa-vuejs text-white text-sm"></i>
                  </div>
                  <span class="text-gray-700 font-medium">Développement avec Vue.js, Vite, Tailwind CSS</span>
                </div>
                <div class="flex items-center gap-3 p-3 rounded-lg bg-white/50 border border-gray-100">
                  <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-orange-400 to-orange-500 flex items-center justify-center">
                    <i class="fas fa-bolt text-white text-sm"></i>
                  </div>
                  <span class="text-gray-700 font-medium">Performances optimisées</span>
                </div>
                <div class="flex items-center gap-3 p-3 rounded-lg bg-white/50 border border-gray-100">
                  <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-cyan-400 to-cyan-500 flex items-center justify-center">
                    <i class="fab fa-css3-alt text-white text-sm"></i>
                  </div>
                  <span class="text-gray-700 font-medium">Aucune dépendance à une base de données ou back-end</span>
                </div>
              </div>
            </div>

            <!-- Conteneur image -->
            <div class="w-full md:w-1/2 relative group">
              <div class="absolute -top-6 -right-6 w-24 h-24 bg-b5/10 rounded-full blur-xl z-0"></div>
              <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-b6/10 rounded-full blur-lg z-0"></div>

              <div class="relative z-10 overflow-hidden rounded-2xl shadow-2xl transform transition-all duration-500 group-hover:scale-[1.02]">
                <div class="bg-gradient-to-br from-gray-900 to-gray-800 p-8 h-64 flex items-center justify-center">
                  <div class="text-center">
                    <div class="grid grid-cols-3 gap-4 mb-4">
                      <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-lg flex items-center justify-center">
                        <i class="fab fa-vuejs text-white text-xl"></i>
                      </div>
                      <div class="w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bolt text-white text-xl"></i>
                      </div>
                      <div class="w-12 h-12 bg-gradient-to-r from-cyan-400 to-cyan-500 rounded-lg flex items-center justify-center">
                        <i class="fab fa-css3-alt text-white text-xl"></i>
                      </div>
                    </div>
                    <p class="text-white font-semibold">Technologies modernes</p>
                    <p class="text-gray-300 text-sm">Vue.js • Vite • Tailwind</p>
                  </div>
                </div>
              </div>
            </div>
          </article>

          <!-- Bloc 4: Accompagnement sur mesure -->
          <article class="flex flex-col md:flex-row-reverse items-center gap-6 md:gap-8 lg:gap-16 relative">
            <!-- Élément décoratif -->
            <div class="absolute -right-4 top-1/4 w-8 h-24 bg-gradient-to-b from-b3/30 to-b6/30 rounded-l-lg hidden md:block opacity-50"></div>

            <!-- Contenu texte -->
            <div class="w-full md:w-1/2 space-y-6">
              <div class="inline-flex items-center px-3 py-1 rounded-full bg-b6/10 text-b6 text-sm font-medium">
                <i class="fas fa-handshake mr-2"></i>Accompagnement
              </div>
              <h3 class="text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
                Un accompagnement
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-b6 to-b3">
                  sur mesure, sans jargon
                </span>
              </h3>
              <p class="text-lg text-gray-600 leading-relaxed">
                Vous n'êtes pas seul(e).<br>
                Nous vous guidons tout au long du projet, avec des explications claires, des échanges réguliers et un vrai souci du détail.
              </p>

              <!-- Avantages accompagnement -->
              <div class="grid grid-cols-1 gap-4">
                <div class="flex items-center gap-3 p-3 rounded-lg bg-white/50 border border-gray-100">
                  <div class="w-2 h-2 rounded-full bg-b6"></div>
                  <span class="text-gray-700 font-medium">Interlocuteur unique & dédié</span>
                </div>
                <div class="flex items-center gap-3 p-3 rounded-lg bg-white/50 border border-gray-100">
                  <div class="w-2 h-2 rounded-full bg-b3"></div>
                  <span class="text-gray-700 font-medium">Prise en main facilitée</span>
                </div>
                <div class="flex items-center gap-3 p-3 rounded-lg bg-white/50 border border-gray-100">
                  <div class="w-2 h-2 rounded-full bg-b4"></div>
                  <span class="text-gray-700 font-medium">Suivi et support même après la mise en ligne</span>
                </div>
              </div>
            </div>

            <!-- Conteneur image -->
            <div class="w-full md:w-1/2 relative group">
              <div class="absolute inset-0 bg-gradient-to-br from-b3/20 to-b6/20 rounded-2xl transform -rotate-3 scale-[0.97] transition-all duration-500 group-hover:rotate-0 group-hover:scale-100"></div>

              <div class="relative overflow-hidden rounded-2xl shadow-2xl transform transition-all duration-500 group-hover:rotate-2">
                <img
                  src="../../assets/chef.jpg"
                  alt="Accompagnement personnalisé et humain"
                  class="w-full h-full object-cover transition-all duration-700 group-hover:scale-105 rounded-2xl"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-60 rounded-2xl"></div>
              </div>
            </div>
          </article>
        </div>

        <!-- Section finale avec processus détaillé -->
        <div class="mt-24">
          <!-- En-tête centré -->
          <div class="text-center mb-16">
            <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-sm font-medium mb-8">
              <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
              <i class="fas fa-magic mr-2"></i>Votre site, créé main dans la main
              <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
            </div>
            <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Un processus clair,
              <span class="text-transparent bg-clip-text bg-gradient-to-r from-b3 to-b6">
                humain et efficace
              </span>
            </h3>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
              De la première rencontre à la mise en ligne, découvrez notre méthode éprouvée
            </p>
          </div>

          <!-- Processus en timeline -->
          <div class="relative">
            <!-- Ligne de connexion -->
            <div class="hidden lg:block absolute top-20 left-1/2 transform -translate-x-1/2 w-3/4 h-0.5 bg-gradient-to-r from-b3 to-b6 opacity-30"></div>

            <!-- Grille des étapes -->
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
              <!-- Étape 1 - b3 -->
              <div class="relative group">
                <!-- Numéro d'étape -->
                <div class="absolute -top-4 -left-4 w-8 h-8 bg-b3 rounded-full flex items-center justify-center text-white font-bold text-sm z-10 shadow-lg">
                  1
                </div>

                <!-- Card -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2 relative overflow-hidden">
                  <!-- Accent coloré -->
                  <div class="absolute top-0 left-0 w-full h-1 bg-b3"></div>

                  <!-- Icône -->
                  <div class="w-14 h-14 bg-b3 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <i class="fas fa-compass text-white text-lg"></i>
                  </div>

                  <!-- Contenu -->
                  <h4 class="text-lg font-bold text-gray-900 mb-3 text-center">On apprend à vous connaître</h4>
                  <p class="text-gray-600 text-sm leading-relaxed mb-4 text-center">
                    Échange approfondi sur votre activité, vos objectifs et votre vision
                  </p>

                  <!-- Résultat -->
                  <div class="bg-b3/10 rounded-lg p-3 border border-b3/20">
                    <div class="flex items-start gap-2">
                      <i class="fas fa-check text-b3 text-xs mt-1 flex-shrink-0"></i>
                      <span class="text-gray-700 text-xs font-medium leading-tight">Un site pensé pour vos vrais besoins</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Étape 2 - b4 -->
              <div class="relative group">
                <!-- Numéro d'étape -->
                <div class="absolute -top-4 -left-4 w-8 h-8 bg-b3/50 rounded-full flex items-center justify-center text-white font-bold text-sm z-10 shadow-lg">
                  2
                </div>

                <!-- Card -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2 relative overflow-hidden">
                  <!-- Accent coloré -->
                  <div class="absolute top-0 left-0 w-full h-1 bg-b3/50"></div>

                  <!-- Icône -->
                  <div class="w-14 h-14 bg-b3/50 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <i class="fas fa-tools text-white text-lg"></i>
                  </div>

                  <!-- Contenu -->
                  <h4 class="text-lg font-bold text-gray-900 mb-3 text-center">On crée une première version</h4>
                  <p class="text-gray-600 text-sm leading-relaxed mb-4 text-center">
                    Développement de votre site en temps réel, sans maquette figée
                  </p>

                  <!-- Résultat -->
                  <div class="bg-b4/10 rounded-lg p-3 border border-b4/20">
                    <div class="flex items-start gap-2">
                      <i class="fas fa-check text-b4 text-xs mt-1 flex-shrink-0"></i>
                      <span class="text-gray-700 text-xs font-medium leading-tight">Visualisation immédiate et ajustements en temps réel</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Étape 3 - b5 -->
              <div class="relative group">
                <!-- Numéro d'étape -->
                <div class="absolute -top-4 -left-4 w-8 h-8 bg-b4 rounded-full flex items-center justify-center text-white font-bold text-sm z-10 shadow-lg">
                  3
                </div>

                <!-- Card -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2 relative overflow-hidden">
                  <!-- Accent coloré -->
                  <div class="absolute top-0 left-0 w-full h-1 bg-b4"></div>

                  <!-- Icône -->
                  <div class="w-14 h-14 bg-b4 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <i class="fas fa-magic text-white text-lg"></i>
                  </div>

                  <!-- Contenu -->
                  <h4 class="text-lg font-bold text-gray-900 mb-3 text-center">On affine jusqu'à la perfection</h4>
                  <p class="text-gray-600 text-sm leading-relaxed mb-4 text-center">
                    Optimisation complète : design, responsive, SEO et performances
                  </p>

                  <!-- Résultat -->
                  <div class="bg-b5/10 rounded-lg p-3 border border-b5/20">
                    <div class="flex items-start gap-2">
                      <i class="fas fa-check text-b5 text-xs mt-1 flex-shrink-0"></i>
                      <span class="text-gray-700 text-xs font-medium leading-tight">Site professionnel sur tous les écrans</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Étape 4 - b6 -->
              <div class="relative group">
                <!-- Numéro d'étape -->
                <div class="absolute -top-4 -left-4 w-8 h-8 bg-b6 rounded-full flex items-center justify-center text-white font-bold text-sm z-10 shadow-lg">
                  4
                </div>

                <!-- Card -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2 relative overflow-hidden">
                  <!-- Accent coloré -->
                  <div class="absolute top-0 left-0 w-full h-1 bg-b6"></div>

                  <!-- Icône -->
                  <div class="w-14 h-14 bg-b6 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <i class="fas fa-rocket text-white text-lg"></i>
                  </div>

                  <!-- Contenu -->
                  <h4 class="text-lg font-bold text-gray-900 mb-3 text-center">On met en ligne, vous êtes prêt</h4>
                  <p class="text-gray-600 text-sm leading-relaxed mb-4 text-center">
                    Mise en ligne complète avec tous les accès et le support
                  </p>

                  <!-- Résultat -->
                  <div class="bg-b6/10 rounded-lg p-3 border border-b6/20">
                    <div class="flex items-start gap-2">
                      <i class="fas fa-check text-b6 text-xs mt-1 flex-shrink-0"></i>
                      <span class="text-gray-700 text-xs font-medium leading-tight">Site opérationnel sans prise de tête</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- CTA final -->
          <div class="text-center mt-16">
            <div class="bg-gradient-to-r from-b3/5 to-b6/5 rounded-2xl p-8 border border-b3/10">
              <h4 class="text-2xl font-bold text-gray-900 mb-4">
                Prêt à démarrer votre projet ?
              </h4>
              <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
                Chaque étape est pensée pour vous offrir transparence, qualité et résultats.
                Parlons de votre projet dès maintenant.
              </p>
              <button
                @click="openContactModal"
                class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-b3 to-b6 text-white font-semibold rounded-xl hover:shadow-2xl hover:shadow-b3/25 transform hover:-translate-y-1 transition-all duration-300"
              >
                <i class="fas fa-envelope mr-2"></i>
                Commencer mon projet
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Pricing -->
    <section class="py-20 bg-gradient-to-b from-gray-50 to-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
            Nos formules
            <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Choisissez la formule
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-b3 to-b6">
              qui vous correspond
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Trois formules adaptées à vos besoins et votre budget, avec un accompagnement personnalisé
          </p>
        </div>

        <!-- Grille des formules -->
        <div class="grid lg:grid-cols-3 gap-6 lg:gap-8">

          <!-- Formule Starter -->
          <div class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <!-- En-tête -->
            <div class="p-6 border-b border-gray-100">
              <div class="text-center">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Starter</h3>
                <div class="mb-3">
                  <span class="text-sm text-gray-500 line-through mr-2">1500€</span>
                  <span class="text-3xl font-bold text-gray-900">1350€</span>
                </div>
                <div class="inline-block bg-red-50 text-red-600 px-3 py-1 rounded-full text-xs font-medium">
                  OFFRE LIMITÉE - Valable sur les 3 prochaines commandes
                </div>
              </div>
            </div>

            <!-- Contenu -->
            <div class="p-6">
              <ul class="space-y-3 mb-6">
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm"><strong>1 page</strong> (type One-page)</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm"><strong>5 sections max</strong></span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Menu ancré (défilement sur la même page)</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Responsive : Mobile, tablette, ordinateur</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Liens Réseaux sociaux</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Design Personnalisé</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Hébergement : inclus sur Netlify (https://nom-entreprise.netlify.app) Domaine personnalisé en option</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">SEO : Balises optimisées</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Maintenance : 1 mois inclus (fix bugs, mises à jour, optimisations)</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Modifications incluses : 3 changements mineurs pendant 1 mois</span>
                </li>
              </ul>

              <button
                @click="openContactModal"
                class="w-full bg-gray-900 text-white font-medium py-3 px-4 rounded-md hover:bg-gray-800 transition-colors duration-200 cursor-pointer"
              >
                Choisir Starter
              </button>
            </div>
          </div>

          <!-- Formule Premium -->
          <div class="bg-white rounded-lg border-2 border-blue-200 shadow-sm hover:shadow-md transition-shadow duration-200 relative">
            <!-- Badge populaire -->
            <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                POPULAIRE
              </span>
            </div>

            <!-- En-tête -->
            <div class="p-6 border-b border-gray-100 pt-8">
              <div class="text-center">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Premium</h3>
                <div class="mb-3">
                  <span class="text-sm text-gray-500 line-through mr-2">2500€</span>
                  <span class="text-3xl font-bold text-gray-900">2250€</span>
                </div>
                <div class="inline-block bg-red-50 text-red-600 px-3 py-1 rounded-full text-xs font-medium mb-3">
                  OFFRE LIMITÉE - Valable sur les 2 prochaines commandes
                </div>
                <div class="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm">
                  <div class="font-medium">Un site complet pour présenter votre activité en détail</div>
                  <div class="text-xs text-blue-600 mt-1">L'essentiel pour une présence en ligne efficace</div>
                </div>
              </div>
            </div>

            <!-- Contenu -->
            <div class="p-6">
              <ul class="space-y-3 mb-6">
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm"><strong>Jusqu'à 3 pages</strong> (Accueil, Services, À propos, etc.)</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm"><strong>3 sections max / page</strong></span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Menu organisé en pages distinctes</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Responsive : Mobile, tablette, ordinateur</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Liens Réseaux sociaux</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Design Personnalisé</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Hébergement inclus + nom de domaine personnalisé (www.nom-entreprise.com)</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">SEO : Optimisation du site de façon globale + titres, descriptions, structure</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Maintenance : 2 mois inclus (fix bugs, mises à jour, optimisations)</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Modifications incluses : 6 changements mineurs pendant 2 mois</span>
                </li>
              </ul>

              <button
                @click="openContactModal"
                class="w-full bg-blue-600 text-white font-medium py-3 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200 cursor-pointer"
              >
                Choisir Premium
              </button>
            </div>
          </div>

          <!-- Formule IMPACT PRO+ -->
          <div class="bg-gray-50 rounded-lg border border-gray-300 shadow-sm hover:shadow-md transition-shadow duration-200 relative">
            <!-- Badge premium -->
            <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span class="bg-gray-800 text-white px-3 py-1 rounded-full text-xs font-medium">
                PREMIUM
              </span>
            </div>

            <!-- En-tête -->
            <div class="p-6 border-b border-gray-200 pt-8">
              <div class="text-center">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">IMPACT PRO+</h3>
                <div class="mb-3">
                  <span class="text-3xl font-bold text-gray-900">Sur devis</span>
                </div>
                <div class="bg-gray-100 text-gray-700 px-3 py-2 rounded-md text-sm">
                  <div class="font-medium">Une solution 100% personnalisée, pensée pour convaincre</div>
                </div>
              </div>
            </div>

            <!-- Contenu -->
            <div class="p-6">
              <ul class="space-y-3 mb-6">
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm"><strong>Jusqu'à 6 pages personnalisées</strong></span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm"><strong>3 sections max / page</strong></span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Navigation complète et structurée</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Responsive : Mobile, tablette, ordinateur</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Liens Réseaux sociaux</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Design Personnalisé</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Hébergement inclus + nom de domaine personnalisé (www.nom-entreprise.com)</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">SEO : Optimisation poussée pour un référencement optimal</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm">Maintenance : 3 mois inclus (fix bugs, mises à jour, optimisations)</span>
                </li>
                <li class="flex items-start gap-3">
                  <i class="fas fa-check text-green-500 mt-1 text-sm flex-shrink-0"></i>
                  <span class="text-gray-700 text-sm"><strong>Modifications illimitées pendant 3 mois</strong></span>
                </li>
              </ul>

              <button
                @click="openContactModal"
                class="w-full bg-gray-800 text-white font-medium py-3 px-4 rounded-md hover:bg-gray-900 transition-colors duration-200 cursor-pointer"
              >
                Demander un devis
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Options -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
            Options complémentaires
            <span class="w-2 h-2 rounded-full bg-cyan-500 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Personnalisez
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600">
              votre projet
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Ajoutez des options pour adapter parfaitement votre site à vos besoins spécifiques
          </p>
        </div>

        <!-- Grille des options -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Option 1 -->
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-wrench text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Modifications ponctuelles</h3>
                <p class="text-blue-700 text-sm font-medium">Sur devis</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Modifications rapides sur votre site existant</p>
          </div>

          <!-- Option 2 -->
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-palette text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Visuels personnalisés</h3>
                <p class="text-purple-700 text-sm font-medium">+120€/visuel</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Logo, bannières, illustrations sur-mesure</p>
          </div>

          <!-- Option 3 -->
          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-plus text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Page supplémentaire</h3>
                <p class="text-green-700 text-sm font-medium">Sur devis</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Ajout de pages (blog, galerie, etc.)</p>
          </div>

          <!-- Option 4 -->
          <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 border border-orange-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-cog text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Maintenance</h3>
                <p class="text-orange-700 text-sm font-medium">+180€</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Maintenance et mises à jour continues</p>
          </div>

          <!-- Option 5 -->
          <div class="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-xl p-6 border border-cyan-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-globe text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Nom de domaine</h3>
                <p class="text-cyan-700 text-sm font-medium">+150€</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Configuration domaine personnalisé</p>
          </div>

          <!-- Option 6 -->
          <div class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-envelope text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">E-mail professionnel</h3>
                <p class="text-indigo-700 text-sm font-medium">+80€</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Adresse e-mail avec votre domaine</p>
          </div>
        </div>

        <!-- CTA pour voir toutes les options -->
        <div class="text-center mt-12">
          <a
            href="/options"
            class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transform hover:-translate-y-0.5 transition-all duration-300"
          >
            <i class="fas fa-arrow-right mr-2"></i>
            Voir toutes les options
          </a>
        </div>
      </div>
    </section>
    <Footer />
    
    <!-- Modale de contact -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<style scoped>
/* Animations personnalisées */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.6s ease-out 0.3s forwards;
}

.animate-fade-in-delayed-more {
  animation: fadeIn 0.6s ease-out 0.6s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style>
