<script setup>
import CarouselTop from "../TDI-modules/CarouselTop.vue";
import Navbar from "../TDI-modules/Navbar.vue";
import USPOne from "../TDI-modules/USPOne.vue";
import Pricing from "../TDI-modules/Pricing.vue";
import Footer from "../TDI-modules/Footer.vue";
import CarouselTitle from "../TDI-modules/CarouselTitle.vue";
import Options from "../TDI-modules/Options.vue";
import { ref, onMounted } from "vue";
import { PaintBrushIcon, CodeBracketIcon, RocketLaunchIcon } from "@heroicons/vue/24/outline";
import brandingImage from "../../assets/branding.webp";
import ContactModal from "../TDI-modules/ContactModal.vue";

// Animation de la section hero
const heroVisible = ref(false);
const textVisible = ref(false);
const ctaVisible = ref(false);
const isContactModalOpen = ref(false);

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true;
};

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false;
};

onMounted(() => {
  // Animation séquentielle des éléments comme dans HeroHome
  setTimeout(() => { heroVisible.value = true; }, 50);
  setTimeout(() => { textVisible.value = true; }, 180);
  setTimeout(() => { ctaVisible.value = true; }, 300);
});
</script>

<template>
  <div class="bg-black min-h-screen">
    <CarouselTop />
    <Navbar />

    <!-- Section Hero -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Image de fond -->
      <div class="absolute inset-0">
                 <img 
           src="../../assets/branding.webp" 
           alt="Workspace moderne et lumineux"
           class="w-full h-full object-cover"
         />
                 <!-- Overlay gradient pour image claire -->
         <div class="absolute inset-0 bg-gradient-to-b from-b3/60 via-black/75 to-black"></div>
        
      </div>

      <!-- Contenu -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/20 to-b6/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8 opacity-0" :class="{ 'animate-fade-in': heroVisible }">
          <span class="w-2 h-2 rounded-full bg-b3 mr-2 animate-pulse"></span>
          Création sur-mesure
          <span class="w-2 h-2 rounded-full bg-b6 ml-2 animate-pulse"></span>
        </div>

        <!-- Titre principal -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Votre site web
          <span class="block bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">
            sur-mesure
          </span>
          <span class="block text-white">commence ici</span>
        </h1>

        <!-- Sous-titre -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-gray-300 mb-10 leading-relaxed opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Créons ensemble un site web qui vous ressemble, performant et unique, 
          <span class="text-b4 font-semibold">conçu pour faire grandir votre business</span>
        </p>

        <!-- Features rapides avec design amélioré -->
        <div class="flex flex-wrap justify-center gap-4 mb-12 opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <PaintBrushIcon class="w-5 h-5 text-b3" />
            <span class="text-sm font-medium text-white">Design unique</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <CodeBracketIcon class="w-5 h-5 text-b4" />
            <span class="text-sm font-medium text-white">Code sur-mesure</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <RocketLaunchIcon class="w-5 h-5 text-b6" />
            <span class="text-sm font-medium text-white">Performance optimale</span>
          </div>
        </div>

        <!-- CTA améliorés -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center opacity-0" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
          <a
            href="#services"
            class="inline-flex items-center px-8 py-4 rounded-2xl bg-gradient-to-r from-b3 to-b6 text-white font-semibold text-lg hover:shadow-2xl hover:shadow-b3/25 transform hover:-translate-y-1 transition-all duration-300"
          >
            <i class="fas fa-rocket mr-2"></i>
            Découvrir nos services
            <i class="fas fa-arrow-down ml-2"></i>
          </a>
          <button
            @click="openContactModal"
            class="inline-flex items-center px-8 py-4 rounded-2xl border-2 border-white/30 text-white font-semibold text-lg hover:bg-white/10 hover:border-white/50 backdrop-blur-sm transition-all duration-300"
          >
            <i class="fas fa-envelope mr-2"></i>
            Parlons de votre projet
          </button>
        </div>
      </div>

      <!-- Scroll indicator amélioré -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce opacity-0" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center backdrop-blur-sm">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
        <p class="text-white/60 text-xs mt-2 text-center">Découvrir</p>
      </div>
    </section>

    <!-- Section Services -->
    <section id="services" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
            Notre approche
            <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Pourquoi choisir
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-b3 to-b6">
              notre création sur-mesure ?
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Chaque projet est unique, c'est pourquoi nous adaptons notre approche à vos besoins spécifiques
          </p>
        </div>

        <!-- Grille des avantages -->
        <div class="grid lg:grid-cols-3 gap-8 mb-16">
          <!-- Design Unique -->
          <div class="bg-gradient-to-br from-b3/5 to-b4/5 rounded-2xl p-8 border border-b3/10 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-b3 to-b4 rounded-2xl flex items-center justify-center mb-6">
              <PaintBrushIcon class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Design Unique</h3>
            <p class="text-gray-600 mb-6">Création d'un design 100% personnalisé qui reflète votre identité et se démarque de la concurrence</p>
            <ul class="text-sm text-gray-500 space-y-3">
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-2 mt-1 text-xs"></i>
                <span>Maquettes personnalisées</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-2 mt-1 text-xs"></i>
                <span>Charte graphique cohérente</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-2 mt-1 text-xs"></i>
                <span>Expérience utilisateur optimisée</span>
              </li>
            </ul>
          </div>

          <!-- Code Sur-Mesure -->
          <div class="bg-gradient-to-br from-b4/5 to-b5/5 rounded-2xl p-8 border border-b4/10 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-b4 to-b5 rounded-2xl flex items-center justify-center mb-6">
              <CodeBracketIcon class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Code Sur-Mesure</h3>
            <p class="text-gray-600 mb-6">Développement avec les dernières technologies pour des performances optimales et une évolutivité maximale</p>
            <ul class="text-sm text-gray-500 space-y-3">
              <li class="flex items-start">
                <i class="fas fa-check text-b4 mr-2 mt-1 text-xs"></i>
                <span>Technologies modernes (Vue.js, React...)</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b4 mr-2 mt-1 text-xs"></i>
                <span>Code propre et documenté</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b4 mr-2 mt-1 text-xs"></i>
                <span>Optimisation des performances</span>
              </li>
            </ul>
          </div>

          <!-- Accompagnement -->
          <div class="bg-gradient-to-br from-b5/5 to-b6/5 rounded-2xl p-8 border border-b5/10 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-b5 to-b6 rounded-2xl flex items-center justify-center mb-6">
              <RocketLaunchIcon class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Accompagnement Complet</h3>
            <p class="text-gray-600 mb-6">Suivi personnalisé de A à Z, de la conception à la mise en ligne, avec formation et support inclus</p>
            <ul class="text-sm text-gray-500 space-y-3">
              <li class="flex items-start">
                <i class="fas fa-check text-b5 mr-2 mt-1 text-xs"></i>
                <span>Suivi projet dédié</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b5 mr-2 mt-1 text-xs"></i>
                <span>Formation à la gestion</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b5 mr-2 mt-1 text-xs"></i>
                <span>Support post-lancement</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Section processus -->
        <div class="text-center">
          <h3 class="text-2xl font-bold text-gray-900 mb-8">Notre processus en 4 étapes</h3>
          <div class="grid md:grid-cols-4 gap-8">
            <div class="text-center">
              <div class="w-12 h-12 bg-gradient-to-r from-b3 to-b4 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-white font-bold">1</span>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">Analyse</h4>
              <p class="text-sm text-gray-600">Étude de vos besoins et objectifs</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-gradient-to-r from-b4 to-b5 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-white font-bold">2</span>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">Conception</h4>
              <p class="text-sm text-gray-600">Maquettes et validation du design</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-gradient-to-r from-b5 to-b6 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-white font-bold">3</span>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">Développement</h4>
              <p class="text-sm text-gray-600">Intégration et programmation</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-gradient-to-r from-b6 to-b3 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-white font-bold">4</span>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">Lancement</h4>
              <p class="text-sm text-gray-600">Mise en ligne et formation</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section USPOne -->
    <USPOne  />

    <!-- Section pricing -->
    <div
      class="relative overflow-hidden"
      :style="{
        backgroundImage: `url(${brandingImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }"
    >
      <!-- Background Overlay -->
      <div class="absolute inset-0 bg-gradient-to-b from-black/70 to-b1/95">
        <!-- Subtle Pattern -->
        <div
          class="absolute inset-0 opacity-5"
          style="
            background-size: 20px 20px;
            background-image: radial-gradient(
              circle,
              rgba(255, 255, 255, 0.3) 1px,
              transparent 1px
            );
          "
        ></div>
      </div>

      <!-- Content -->
      <div class="relative z-10">
        <Pricing />
        <Options />
      </div>
    </div>
    <Footer />
    
    <!-- Modale de contact -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<style scoped>
/* Animations personnalisées */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.6s ease-out 0.3s forwards;
}

.animate-fade-in-delayed-more {
  animation: fadeIn 0.6s ease-out 0.6s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style>
