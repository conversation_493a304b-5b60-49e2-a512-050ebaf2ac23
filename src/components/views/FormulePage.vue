<script setup>
import CarouselTop from "../TDI-modules/CarouselTop.vue";
import Navbar from "../TDI-modules/Navbar.vue";
import Footer from "../TDI-modules/Footer.vue";
import { ref, onMounted } from "vue";
import { PaintBrushIcon, CodeBracketIcon, RocketLaunchIcon } from "@heroicons/vue/24/outline";
import ContactModal from "../TDI-modules/ContactModal.vue";

// Animation de la section hero
const heroVisible = ref(false);
const textVisible = ref(false);
const ctaVisible = ref(false);
const isContactModalOpen = ref(false);

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true;
};

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false;
};

onMounted(() => {
  // Animation séquentielle des éléments comme dans HeroHome
  setTimeout(() => { heroVisible.value = true; }, 50);
  setTimeout(() => { textVisible.value = true; }, 180);
  setTimeout(() => { ctaVisible.value = true; }, 300);
});
</script>

<template>
  <div class="bg-black min-h-screen">
    <CarouselTop />
    <Navbar />

    <!-- Section Hero -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Image de fond -->
      <div class="absolute inset-0">
                 <img 
           src="../../assets/branding.webp" 
           alt="Workspace moderne et lumineux"
           class="w-full h-full object-cover"
         />
                 <!-- Overlay gradient pour image claire -->
         <div class="absolute inset-0 bg-gradient-to-b from-b3/60 via-black/75 to-black"></div>
        
      </div>

      <!-- Contenu -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/20 to-b6/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8 opacity-0" :class="{ 'animate-fade-in': heroVisible }">
          <span class="w-2 h-2 rounded-full bg-b3 mr-2 animate-pulse"></span>
          Création sur-mesure
          <span class="w-2 h-2 rounded-full bg-b6 ml-2 animate-pulse"></span>
        </div>

        <!-- Titre principal -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Votre site web
          <span class="block bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">
            sur-mesure
          </span>
          <span class="block text-white">commence ici</span>
        </h1>

        <!-- Sous-titre -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-gray-300 mb-10 leading-relaxed opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Créons ensemble un site web qui vous ressemble, performant et unique, 
          <span class="text-b4 font-semibold">conçu pour faire grandir votre business</span>
        </p>

        <!-- Features rapides avec design amélioré -->
        <div class="flex flex-wrap justify-center gap-4 mb-12 opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <PaintBrushIcon class="w-5 h-5 text-b3" />
            <span class="text-sm font-medium text-white">Design unique</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <CodeBracketIcon class="w-5 h-5 text-b4" />
            <span class="text-sm font-medium text-white">Code sur-mesure</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <RocketLaunchIcon class="w-5 h-5 text-b6" />
            <span class="text-sm font-medium text-white">Performance optimale</span>
          </div>
        </div>

        <!-- CTA améliorés -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center opacity-0" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
          <a
            href="#services"
            class="inline-flex items-center px-8 py-4 rounded-2xl bg-gradient-to-r from-b3 to-b6 text-white font-semibold text-lg hover:shadow-2xl hover:shadow-b3/25 transform hover:-translate-y-1 transition-all duration-300"
          >
            <i class="fas fa-rocket mr-2"></i>
            Découvrir nos services
            <i class="fas fa-arrow-down ml-2"></i>
          </a>
          <button
            @click="openContactModal"
            class="inline-flex items-center px-8 py-4 rounded-2xl border-2 border-white/30 text-white font-semibold text-lg hover:bg-white/10 hover:border-white/50 backdrop-blur-sm transition-all duration-300"
          >
            <i class="fas fa-envelope mr-2"></i>
            Parlons de votre projet
          </button>
        </div>
      </div>

      <!-- Scroll indicator amélioré -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce opacity-0" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center backdrop-blur-sm">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
        <p class="text-white/60 text-xs mt-2 text-center">Découvrir</p>
      </div>
    </section>

    <!-- Section Services -->
    <section id="services" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
            Notre approche
            <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
            <span>Pourquoi choisir </span>
            <span class="relative inline-block">
              <span class="relative z-10 bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">notre création sur-mesure</span>
              <span class="absolute bottom-2 left-0 w-full h-3 bg-b3/20 -rotate-1 z-0"></span>
            </span>
            <span> ?</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Chaque projet est unique, c'est pourquoi nous adaptons notre approche à vos besoins spécifiques
          </p>
        </div>

        <!-- Grille des avantages -->
        <div class="grid lg:grid-cols-3 gap-8 mb-16">
          <!-- Design Unique -->
          <div class="bg-gradient-to-br from-b3/5 to-b4/5 rounded-2xl p-8 border border-b3/10 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-b3 to-b4 rounded-2xl flex items-center justify-center mb-6">
              <PaintBrushIcon class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Design Unique</h3>
            <p class="text-gray-600 mb-6">Création d'un design 100% personnalisé qui reflète votre identité et se démarque de la concurrence</p>
            <ul class="text-sm text-gray-500 space-y-3">
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-2 mt-1 text-xs"></i>
                <span>Maquettes personnalisées</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-2 mt-1 text-xs"></i>
                <span>Charte graphique cohérente</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-2 mt-1 text-xs"></i>
                <span>Expérience utilisateur optimisée</span>
              </li>
            </ul>
          </div>

          <!-- Code Sur-Mesure -->
          <div class="bg-gradient-to-br from-b4/5 to-b5/5 rounded-2xl p-8 border border-b4/10 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-b4 to-b5 rounded-2xl flex items-center justify-center mb-6">
              <CodeBracketIcon class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Code Sur-Mesure</h3>
            <p class="text-gray-600 mb-6">Développement avec les dernières technologies pour des performances optimales et une évolutivité maximale</p>
            <ul class="text-sm text-gray-500 space-y-3">
              <li class="flex items-start">
                <i class="fas fa-check text-b4 mr-2 mt-1 text-xs"></i>
                <span>Technologies modernes (Vue.js, React...)</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b4 mr-2 mt-1 text-xs"></i>
                <span>Code propre et documenté</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b4 mr-2 mt-1 text-xs"></i>
                <span>Optimisation des performances</span>
              </li>
            </ul>
          </div>

          <!-- Accompagnement -->
          <div class="bg-gradient-to-br from-b5/5 to-b6/5 rounded-2xl p-8 border border-b5/10 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-b5 to-b6 rounded-2xl flex items-center justify-center mb-6">
              <RocketLaunchIcon class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Accompagnement Complet</h3>
            <p class="text-gray-600 mb-6">Suivi personnalisé de A à Z, de la conception à la mise en ligne, avec formation et support inclus</p>
            <ul class="text-sm text-gray-500 space-y-3">
              <li class="flex items-start">
                <i class="fas fa-check text-b5 mr-2 mt-1 text-xs"></i>
                <span>Suivi projet dédié</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b5 mr-2 mt-1 text-xs"></i>
                <span>Formation à la gestion</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b5 mr-2 mt-1 text-xs"></i>
                <span>Support post-lancement</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Section processus -->
        <div class="text-center">
          <h3 class="text-2xl font-bold text-gray-900 mb-8">Notre processus en 4 étapes</h3>
          <div class="grid md:grid-cols-4 gap-8">
            <div class="text-center">
              <div class="w-12 h-12 bg-gradient-to-r from-b3 to-b4 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-white font-bold">1</span>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">Analyse</h4>
              <p class="text-sm text-gray-600">Étude de vos besoins et objectifs</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-gradient-to-r from-b4 to-b5 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-white font-bold">2</span>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">Conception</h4>
              <p class="text-sm text-gray-600">Maquettes et validation du design</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-gradient-to-r from-b5 to-b6 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-white font-bold">3</span>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">Développement</h4>
              <p class="text-sm text-gray-600">Intégration et programmation</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-gradient-to-r from-b6 to-b3 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-white font-bold">4</span>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">Lancement</h4>
              <p class="text-sm text-gray-600">Mise en ligne et formation</p>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- Section Pricing -->
    <section class="py-20 bg-gradient-to-b from-gray-50 to-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
            Nos formules
            <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Choisissez la formule
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-b3 to-b6">
              qui vous correspond
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Trois formules adaptées à vos besoins et votre budget, avec un accompagnement personnalisé
          </p>
        </div>

        <!-- Grille des formules -->
        <div class="grid lg:grid-cols-3 gap-8 mb-16">
          <!-- Formule Essentiel -->
          <div class="bg-white rounded-2xl p-8 border border-gray-200 hover:shadow-lg transition-all duration-300 relative">
            <div class="text-center mb-6">
              <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-seedling text-white text-2xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 mb-2">Essentiel</h3>
              <p class="text-gray-600 mb-4">Parfait pour débuter</p>
              <div class="text-4xl font-bold text-gray-900 mb-2">850€</div>
              <p class="text-sm text-gray-500">Site vitrine professionnel</p>
            </div>

            <ul class="space-y-3 mb-8">
              <li class="flex items-start">
                <i class="fas fa-check text-green-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Site vitrine jusqu'à 5 pages</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-green-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Design responsive et moderne</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-green-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Optimisation SEO de base</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-green-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Formulaire de contact</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-green-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Formation à la gestion</span>
              </li>
            </ul>

            <button
              @click="openContactModal"
              class="w-full px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-green-500/25 transform hover:-translate-y-0.5 transition-all duration-300"
            >
              Choisir Essentiel
            </button>

            <div class="mt-4 text-center">
              <p class="text-xs text-gray-500">Délai : 2-3 semaines</p>
            </div>
          </div>

          <!-- Formule Business (Populaire) -->
          <div class="bg-white rounded-2xl p-8 border-2 border-b3 hover:shadow-xl transition-all duration-300 relative transform scale-105">
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="bg-gradient-to-r from-b3 to-b6 text-white px-4 py-1 rounded-full text-sm font-medium">
                Populaire
              </span>
            </div>

            <div class="text-center mb-6">
              <div class="w-16 h-16 bg-gradient-to-r from-b3 to-b4 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-rocket text-white text-2xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 mb-2">Business</h3>
              <p class="text-gray-600 mb-4">Le plus complet</p>
              <div class="text-4xl font-bold text-gray-900 mb-2">1450€</div>
              <p class="text-sm text-gray-500">Site professionnel avancé</p>
            </div>

            <ul class="space-y-3 mb-8">
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Tout de la formule Essentiel</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Jusqu'à 10 pages</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Blog intégré</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Galerie photos/portfolio</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Intégrations réseaux sociaux</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-b3 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Analytics et suivi</span>
              </li>
            </ul>

            <button
              @click="openContactModal"
              class="w-full px-6 py-3 bg-gradient-to-r from-b3 to-b6 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-b3/25 transform hover:-translate-y-0.5 transition-all duration-300"
            >
              Choisir Business
            </button>

            <div class="mt-4 text-center">
              <p class="text-xs text-gray-500">Délai : 3-4 semaines</p>
            </div>
          </div>

          <!-- Formule Premium -->
          <div class="bg-white rounded-2xl p-8 border border-gray-200 hover:shadow-lg transition-all duration-300 relative">
            <div class="text-center mb-6">
              <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-crown text-white text-2xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 mb-2">Premium</h3>
              <p class="text-gray-600 mb-4">Solution complète</p>
              <div class="text-4xl font-bold text-gray-900 mb-2">2200€</div>
              <p class="text-sm text-gray-500">E-commerce ou site complexe</p>
            </div>

            <ul class="space-y-3 mb-8">
              <li class="flex items-start">
                <i class="fas fa-check text-purple-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Tout de la formule Business</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-purple-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">E-commerce complet</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-purple-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Espace membre/client</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-purple-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Fonctionnalités avancées</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-purple-500 mr-3 mt-1 text-sm"></i>
                <span class="text-gray-700">Support prioritaire 3 mois</span>
              </li>
            </ul>

            <button
              @click="openContactModal"
              class="w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-purple-500/25 transform hover:-translate-y-0.5 transition-all duration-300"
            >
              Choisir Premium
            </button>

            <div class="mt-4 text-center">
              <p class="text-xs text-gray-500">Délai : 4-6 semaines</p>
            </div>
          </div>
        </div>

        <!-- Section garantie -->
        <div class="text-center bg-gradient-to-r from-b3/5 to-b6/5 rounded-2xl p-8 border border-b3/10">
          <div class="w-16 h-16 bg-gradient-to-r from-b3 to-b6 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-shield-alt text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">Garantie satisfaction</h3>
          <p class="text-gray-600 max-w-2xl mx-auto">
            Nous nous engageons à votre satisfaction. Si le résultat ne vous convient pas, nous reprenons le travail jusqu'à ce que vous soyez 100% satisfait.
          </p>
        </div>
      </div>
    </section>

    <!-- Section Options -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
            Options complémentaires
            <span class="w-2 h-2 rounded-full bg-cyan-500 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Personnalisez
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600">
              votre projet
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Ajoutez des options pour adapter parfaitement votre site à vos besoins spécifiques
          </p>
        </div>

        <!-- Grille des options -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Option 1 -->
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-wrench text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Modifications ponctuelles</h3>
                <p class="text-blue-700 text-sm font-medium">Sur devis</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Modifications rapides sur votre site existant</p>
          </div>

          <!-- Option 2 -->
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-palette text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Visuels personnalisés</h3>
                <p class="text-purple-700 text-sm font-medium">+120€/visuel</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Logo, bannières, illustrations sur-mesure</p>
          </div>

          <!-- Option 3 -->
          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-plus text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Page supplémentaire</h3>
                <p class="text-green-700 text-sm font-medium">Sur devis</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Ajout de pages (blog, galerie, etc.)</p>
          </div>

          <!-- Option 4 -->
          <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 border border-orange-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-cog text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Maintenance</h3>
                <p class="text-orange-700 text-sm font-medium">+180€</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Maintenance et mises à jour continues</p>
          </div>

          <!-- Option 5 -->
          <div class="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-xl p-6 border border-cyan-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-globe text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">Nom de domaine</h3>
                <p class="text-cyan-700 text-sm font-medium">+150€</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Configuration domaine personnalisé</p>
          </div>

          <!-- Option 6 -->
          <div class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-100 hover:shadow-md transition-all duration-300">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-envelope text-white"></i>
              </div>
              <div>
                <h3 class="font-bold text-gray-900">E-mail professionnel</h3>
                <p class="text-indigo-700 text-sm font-medium">+80€</p>
              </div>
            </div>
            <p class="text-gray-600 text-sm">Adresse e-mail avec votre domaine</p>
          </div>
        </div>

        <!-- CTA pour voir toutes les options -->
        <div class="text-center mt-12">
          <a
            href="/options"
            class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transform hover:-translate-y-0.5 transition-all duration-300"
          >
            <i class="fas fa-arrow-right mr-2"></i>
            Voir toutes les options
          </a>
        </div>
      </div>
    </section>
    <Footer />
    
    <!-- Modale de contact -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<style scoped>
/* Animations personnalisées */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.6s ease-out 0.3s forwards;
}

.animate-fade-in-delayed-more {
  animation: fadeIn 0.6s ease-out 0.6s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style>
