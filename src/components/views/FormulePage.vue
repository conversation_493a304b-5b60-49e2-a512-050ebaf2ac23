<script setup>
import CarouselTop from "../TDI-modules/CarouselTop.vue";
import Navbar from "../TDI-modules/Navbar.vue";
import USPOne from "../TDI-modules/USPOne.vue";
import Pricing from "../TDI-modules/Pricing.vue";
import Footer from "../TDI-modules/Footer.vue";
import CarouselTitle from "../TDI-modules/CarouselTitle.vue";
import Options from "../TDI-modules/Options.vue";
import { ref, onMounted } from "vue";
import { PaintBrushIcon, CodeBracketIcon, RocketLaunchIcon } from "@heroicons/vue/24/outline";
import brandingImage from "../../assets/branding.webp";
import ContactModal from "../TDI-modules/ContactModal.vue";

// Animation de la section hero
const heroVisible = ref(false);
const textVisible = ref(false);
const ctaVisible = ref(false);
const isContactModalOpen = ref(false);

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true;
};

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false;
};

onMounted(() => {
  // Animation séquentielle des éléments comme dans HeroHome
  setTimeout(() => { heroVisible.value = true; }, 50);
  setTimeout(() => { textVisible.value = true; }, 180);
  setTimeout(() => { ctaVisible.value = true; }, 300);
});
</script>

<template>
  <div class="bg-black min-h-screen">
    <CarouselTop />
    <Navbar />

    <!-- Section Hero -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Image de fond -->
      <div class="absolute inset-0">
                 <img 
           src="../../assets/branding.webp" 
           alt="Workspace moderne et lumineux"
           class="w-full h-full object-cover"
         />
                 <!-- Overlay gradient pour image claire -->
         <div class="absolute inset-0 bg-gradient-to-b from-b3/60 via-black/75 to-black"></div>
        
      </div>

      <!-- Contenu -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/20 to-b6/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8 opacity-0" :class="{ 'animate-fade-in': heroVisible }">
          <span class="w-2 h-2 rounded-full bg-b3 mr-2 animate-pulse"></span>
          Création sur-mesure
          <span class="w-2 h-2 rounded-full bg-b6 ml-2 animate-pulse"></span>
        </div>

        <!-- Titre principal -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Votre site web
          <span class="block bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">
            sur-mesure
          </span>
          <span class="block text-white">commence ici</span>
        </h1>

        <!-- Sous-titre -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-gray-300 mb-10 leading-relaxed opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Créons ensemble un site web qui vous ressemble, performant et unique, 
          <span class="text-b4 font-semibold">conçu pour faire grandir votre business</span>
        </p>

        <!-- Features rapides -->
        <div class="flex flex-wrap justify-center gap-6 mb-12 opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          <div class="flex items-center space-x-2 text-white/90">
            <PaintBrushIcon class="w-5 h-5 text-b3" />
            <span class="text-sm font-medium">Design unique</span>
          </div>
          <div class="flex items-center space-x-2 text-white/90">
            <CodeBracketIcon class="w-5 h-5 text-b4" />
            <span class="text-sm font-medium">Code sur-mesure</span>
          </div>
          <div class="flex items-center space-x-2 text-white/90">
            <RocketLaunchIcon class="w-5 h-5 text-b6" />
            <span class="text-sm font-medium">Performance optimale</span>
          </div>
        </div>

        <!-- CTA -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center opacity-0" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
          <a 
            href="#services" 
            class="inline-flex items-center px-8 py-4 rounded-xl bg-gradient-to-r from-b3 to-b6 text-white font-semibold text-lg hover:shadow-2xl hover:shadow-b3/25 transform hover:-translate-y-1 transition-all duration-300"
          >
            Découvrir nos services
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
          </a>
          <button 
            @click="openContactModal"
            class="inline-flex items-center px-8 py-4 rounded-xl border-2 border-white/20 text-white font-semibold text-lg hover:bg-white/10 hover:border-white/40 transition-all duration-300"
          >
            Parlons de votre projet
          </button>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div id="services" class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>

    <!-- Section USPOne -->
    <USPOne  />

    <!-- Section pricing -->
    <div
      class="relative overflow-hidden"
      :style="{
        backgroundImage: `url(${brandingImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }"
    >
      <!-- Background Overlay -->
      <div class="absolute inset-0 bg-gradient-to-b from-black/70 to-b1/95">
        <!-- Subtle Pattern -->
        <div
          class="absolute inset-0 opacity-5"
          style="
            background-size: 20px 20px;
            background-image: radial-gradient(
              circle,
              rgba(255, 255, 255, 0.3) 1px,
              transparent 1px
            );
          "
        ></div>
      </div>

      <!-- Content -->
      <div class="relative z-10">
        <Pricing />
        <Options />
      </div>
    </div>
    <Footer />
    
    <!-- Modale de contact -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<style scoped>
/* Animations personnalisées */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.6s ease-out 0.3s forwards;
}

.animate-fade-in-delayed-more {
  animation: fadeIn 0.6s ease-out 0.6s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style>
