<template>
  <div class="bg-black min-h-screen">
    <CarouselTop />
    <Navbar />

    <!-- Section Hero -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Image de fond -->
      <div class="absolute inset-0">
        <img 
          src="../../assets/branding.webp" 
          alt="Accompagnement professionnel"
          class="w-full h-full object-cover"
        />
        <!-- Overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-b from-orange-600/60 via-black/75 to-black"></div>
      </div>

      <!-- Contenu -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-orange-500/20 to-yellow-500/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8 opacity-0" :class="{ 'animate-fade-in': heroVisible }">
          <span class="w-2 h-2 rounded-full bg-orange-400 mr-2 animate-pulse"></span>
          Accompagnement Sur-Mesure
          <span class="w-2 h-2 rounded-full bg-yellow-400 ml-2 animate-pulse"></span>
        </div>

        <!-- Titre principal -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Un coup de main
          <span class="block bg-gradient-to-r from-orange-400 to-yellow-400 text-transparent bg-clip-text">
            quand vous en avez besoin
          </span>
        </h1>

        <!-- Sous-titre -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-gray-300 mb-10 leading-relaxed opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Formation en direct, aide technique, conseil stratégique... 
          <span class="text-orange-300 font-semibold">Je vous accompagne à l'heure selon vos besoins</span>
        </p>

        <!-- CTA -->
        <div class="flex flex-col sm:flex-row gap-6 justify-center items-center opacity-0" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
          <button @click="openContactModal" class="bg-gradient-to-r from-orange-600 to-yellow-600 hover:from-orange-700 hover:to-yellow-700 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105">
            Réserver une session
          </button>
          <a href="#services" class="border-2 border-white/30 hover:border-white text-white hover:bg-white/10 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300">
            Découvrir les services
          </a>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>

    <!-- Section Services -->
    <section id="services" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-orange-100 to-yellow-100 text-orange-700 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-orange-500 mr-2"></span>
            Accompagnement personnalisé
            <span class="w-2 h-2 rounded-full bg-yellow-500 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Comment puis-je
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-orange-600 to-yellow-600">
              vous aider aujourd'hui ?
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Des sessions en direct pour résoudre vos défis, apprendre de nouvelles compétences ou optimiser votre stratégie digitale
          </p>
        </div>

        <!-- Grille des services -->
        <div class="grid lg:grid-cols-3 gap-8 mb-16">
          <!-- Formation Live -->
          <div class="bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl p-8 border border-orange-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-chalkboard-teacher text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Formation en Direct</h3>
            <p class="text-gray-600 mb-6">Apprenez à utiliser vos outils marketing, votre site web ou vos réseaux sociaux avec un expert à vos côtés</p>
            <ul class="text-sm text-gray-500 space-y-3 mb-6">
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Formation WordPress, Wix, Squarespace</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Google Analytics & Search Console</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Réseaux sociaux (Meta, LinkedIn, Instagram)</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Outils de création (Canva, Figma)</span>
              </li>
            </ul>
            <div class="text-center">
              <div class="text-3xl font-bold text-orange-600 mb-2">80€ <span class="text-lg text-gray-500">/heure</span></div>
              <p class="text-sm text-gray-500">Session d'1h en visio</p>
            </div>
          </div>

          <!-- Aide Technique -->
          <div class="bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl p-8 border border-orange-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-tools text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Aide Technique</h3>
            <p class="text-gray-600 mb-6">Résolvons ensemble vos problèmes techniques, bugs ou difficultés sur vos outils digitaux</p>
            <ul class="text-sm text-gray-500 space-y-3 mb-6">
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Résolution de bugs et dysfonctionnements</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Configuration d'outils et intégrations</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Optimisation de performances</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Mise à jour et maintenance</span>
              </li>
            </ul>
            <div class="text-center">
              <div class="text-3xl font-bold text-blue-600 mb-2">90€ <span class="text-lg text-gray-500">/heure</span></div>
              <p class="text-sm text-gray-500">Intervention technique en direct</p>
            </div>
          </div>

          <!-- Conseil Stratégique -->
          <div class="bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl p-8 border border-orange-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-lightbulb text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Conseil Stratégique</h3>
            <p class="text-gray-600 mb-6">Optimisons votre stratégie digitale, votre communication et votre présence en ligne</p>
            <ul class="text-sm text-gray-500 space-y-3 mb-6">
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Audit de votre présence digitale</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Stratégie de contenu et communication</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Optimisation SEO et référencement</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-check text-orange-500 mr-2 mt-1 text-xs"></i>
                <span>Plan d'action personnalisé</span>
              </li>
            </ul>
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600 mb-2">120€ <span class="text-lg text-gray-500">/heure</span></div>
              <p class="text-sm text-gray-500">Consultation stratégique</p>
            </div>
          </div>
        </div>

        <!-- Forfait mensuel -->
        <div class="bg-gradient-to-r from-orange-600 to-yellow-600 rounded-3xl p-8 lg:p-12 text-white text-center">
          <h3 class="text-3xl lg:text-4xl font-bold mb-4">Forfait Accompagnement Mensuel</h3>
          <p class="text-xl text-orange-100 mb-6">Pour un suivi régulier et une progression continue</p>
          <div class="grid md:grid-cols-3 gap-8 mb-8">
            <div>
              <h4 class="text-2xl font-bold mb-3">4h / mois</h4>
              <div class="text-3xl font-black mb-2">280€</div>
              <p class="text-orange-200 text-sm">soit 70€/h</p>
            </div>
            <div>
              <h4 class="text-2xl font-bold mb-3">8h / mois</h4>
              <div class="text-3xl font-black mb-2">520€</div>
              <p class="text-orange-200 text-sm">soit 65€/h</p>
            </div>
            <div>
              <h4 class="text-2xl font-bold mb-3">12h / mois</h4>
              <div class="text-3xl font-black mb-2">720€</div>
              <p class="text-orange-200 text-sm">soit 60€/h</p>
            </div>
          </div>
          <button @click="openContactModal" class="bg-white text-orange-600 px-10 py-4 rounded-xl font-bold text-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
            Choisir mon forfait
          </button>
          <p class="text-sm text-orange-200 mt-4">Sessions flexibles • Pas d'engagement • Utilisable sur 2 mois</p>
        </div>
      </div>
    </section>

    <!-- Section Comment ça marche -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Comment ça marche ?
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Un processus simple pour obtenir l'aide dont vous avez besoin, quand vous en avez besoin
          </p>
        </div>

        <div class="grid md:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">1</div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Contactez-moi</h3>
            <p class="text-gray-600">Décrivez-moi votre besoin et vos disponibilités</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">2</div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Planification</h3>
            <p class="text-gray-600">Nous fixons ensemble un créneau qui vous convient</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">3</div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Session en direct</h3>
            <p class="text-gray-600">Nous travaillons ensemble en visio pour résoudre votre problématique</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">4</div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Suivi</h3>
            <p class="text-gray-600">Récapitulatif écrit et suivi si nécessaire</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Témoignages -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Ils ont été accompagnés
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Découvrez comment l'accompagnement personnalisé a aidé d'autres entrepreneurs
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div class="bg-gray-50 rounded-2xl p-8 border border-gray-100">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                M
              </div>
              <div>
                <div class="font-bold text-gray-900">Marie L.</div>
                <div class="text-sm text-gray-500">Coach indépendante</div>
              </div>
            </div>
            <p class="text-gray-600 italic">
              "Grâce aux sessions d'accompagnement, j'ai enfin maîtrisé WordPress et je peux maintenant mettre à jour mon site en autonomie. Un gain de temps et d'argent énorme !"
            </p>
          </div>

          <div class="bg-gray-50 rounded-2xl p-8 border border-gray-100">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                J
              </div>
              <div>
                <div class="font-bold text-gray-900">Julien P.</div>
                <div class="text-sm text-gray-500">Restaurateur</div>
              </div>
            </div>
            <p class="text-gray-600 italic">
              "L'audit de ma présence digitale a révélé des problèmes que je n'avais pas vus. Maintenant mon restaurant est beaucoup mieux référencé sur Google !"
            </p>
          </div>

          <div class="bg-gray-50 rounded-2xl p-8 border border-gray-100">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                S
              </div>
              <div>
                <div class="font-bold text-gray-900">Sophie D.</div>
                <div class="text-sm text-gray-500">E-commerce</div>
              </div>
            </div>
            <p class="text-gray-600 italic">
              "Les sessions techniques m'ont permis de résoudre des bugs récurrents sur ma boutique en ligne. Mon taux de conversion a augmenté de 30% !"
            </p>
          </div>
        </div>
      </div>
    </section>

    <Footer />
    
    <!-- Modale de contact -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<script setup>
import CarouselTop from "../TDI-modules/CarouselTop.vue"
import Navbar from "../TDI-modules/Navbar.vue"
import Footer from "../TDI-modules/Footer.vue"
import ContactModal from "../TDI-modules/ContactModal.vue"
import { ref, onMounted } from "vue"

// Animation de la section hero
const heroVisible = ref(false)
const textVisible = ref(false)
const ctaVisible = ref(false)
const isContactModalOpen = ref(false)

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true
}

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false
}

onMounted(() => {
  // Animation séquentielle des éléments
  setTimeout(() => { heroVisible.value = true }, 50)
  setTimeout(() => { textVisible.value = true }, 180)
  setTimeout(() => { ctaVisible.value = true }, 300)
})
</script>

<style scoped>
/* Animations personnalisées */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.6s ease-out 0.3s forwards;
}

.animate-fade-in-delayed-more {
  animation: fadeIn 0.6s ease-out 0.6s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style> 