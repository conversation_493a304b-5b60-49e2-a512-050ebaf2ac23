<template>
  <div class="min-h-screen bg-gradient-to-br from-b1 via-b2 to-b3 flex items-center justify-center p-4">
    <div class="max-w-md w-full bg-white rounded-2xl shadow-2xl p-8 text-center">
      <!-- I<PERSON><PERSON> de succès -->
      <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <i class="fas fa-check text-3xl text-green-600"></i>
      </div>
      
      <!-- Titre -->
      <h1 class="text-2xl font-bold text-gray-900 mb-4">
        Paiement réussi !
      </h1>
      
      <!-- Message -->
      <p class="text-gray-600 mb-6">
        Merci pour votre achat ! Votre commande a été traitée avec succès. 
        Vous recevrez un email de confirmation sous peu.
      </p>
      
      <!-- Informations de commande -->
      <div v-if="sessionId" class="bg-gray-50 rounded-lg p-4 mb-6">
        <p class="text-sm text-gray-500 mb-1">Numéro de transaction</p>
        <p class="font-mono text-sm text-gray-800">{{ sessionId }}</p>
      </div>
      
      <!-- Actions -->
      <div class="space-y-3">
        <button 
          @click="goToShop"
          class="w-full bg-gradient-to-r from-b3 to-b6 text-white font-medium py-3 px-6 rounded-lg hover:shadow-lg transition-all duration-300">
          <i class="fas fa-shopping-bag mr-2"></i>
          Continuer mes achats
        </button>
        
        <button 
          @click="goToHome"
          class="w-full border-2 border-gray-300 text-gray-700 font-medium py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors">
          <i class="fas fa-home mr-2"></i>
          Retour à l'accueil
        </button>
      </div>
      
      <!-- Contact -->
      <div class="mt-8 pt-6 border-t border-gray-200">
        <p class="text-sm text-gray-500 mb-2">
          Une question sur votre commande ?
        </p>
        <a 
          href="mailto:<EMAIL>"
          class="text-b6 hover:text-b3 font-medium text-sm transition-colors">
          <i class="fas fa-envelope mr-1"></i>
          <EMAIL>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useCartStore } from '../../store/cart.js'

const router = useRouter()
const route = useRoute()
const cartStore = useCartStore()

const sessionId = ref('')

onMounted(() => {
  // Récupérer l'ID de session depuis l'URL
  sessionId.value = route.query.session_id || ''
  
  // Vider le panier après un paiement réussi
  cartStore.clearCart()
  
  // Optionnel : tracker l'événement de conversion
  if (window.gtag) {
    window.gtag('event', 'purchase', {
      transaction_id: sessionId.value,
      value: 0, // Vous pourriez stocker le montant dans l'URL
      currency: 'EUR'
    })
  }
})

function goToShop() {
  router.push('/boutique')
}

function goToHome() {
  router.push('/')
}
</script>

<style scoped>
/* Animation d'entrée */
.max-w-md {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 