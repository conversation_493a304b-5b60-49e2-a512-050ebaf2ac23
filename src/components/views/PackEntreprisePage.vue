<template>
  <div class="bg-black min-h-screen">
    <CarouselTop />
    <Navbar />

    <!-- Section Hero -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Image de fond -->
      <div class="absolute inset-0">
        <img 
          src="../../assets/branding.webp" 
          alt="Entreprise moderne et dynamique"
          class="w-full h-full object-cover"
        />
        <!-- Overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-b from-purple-600/60 via-black/75 to-black"></div>
      </div>

      <!-- Contenu -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8 opacity-0" :class="{ 'animate-fade-in': heroVisible }">
          <span class="w-2 h-2 rounded-full bg-purple-400 mr-2 animate-pulse"></span>
          Pack Création d'Entreprise
          <span class="w-2 h-2 rounded-full bg-pink-400 ml-2 animate-pulse"></span>
        </div>

        <!-- Titre principal -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Lancez votre activité
          <span class="block bg-gradient-to-r from-purple-400 to-pink-400 text-transparent bg-clip-text">
            avec tout l'essentiel
          </span>
        </h1>

        <!-- Sous-titre -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-gray-300 mb-10 leading-relaxed opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Logo professionnel, site web sur-mesure, réseaux sociaux, carte de visite, 
          <span class="text-purple-300 font-semibold">tout ce qu'il faut pour démarrer fort</span>
        </p>

        <!-- CTA -->
        <div class="flex flex-col sm:flex-row gap-6 justify-center items-center opacity-0" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
          <button @click="openContactModal" class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105">
            Découvrir le Pack Complet
          </button>
          <a href="#details" class="border-2 border-white/30 hover:border-white text-white hover:bg-white/10 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300">
            Voir les détails
          </a>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>

    <!-- Section Ce qui est inclus -->
    <section id="details" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-purple-500 mr-2"></span>
            Offre tout-en-un
            <span class="w-2 h-2 rounded-full bg-pink-500 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Tout ce dont vous avez besoin
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">
              pour briller dès le premier jour
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Un pack complet pensé pour les entrepreneurs qui veulent partir du bon pied
          </p>
        </div>

        <!-- Grille des inclus -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <!-- Logo professionnel -->
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-palette text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Logo Professionnel</h3>
            <p class="text-gray-600 mb-4">Création d'un logo unique et moderne qui représente parfaitement votre activité</p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li>✓ 3 propositions créatives</li>
              <li>✓ Fichiers vectoriels HD</li>
              <li>✓ Déclinaisons couleur/N&B</li>
              <li>✓ Formats web et print</li>
            </ul>
          </div>

          <!-- Site web sur-mesure -->
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-globe text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Site Web Sur-Mesure</h3>
            <p class="text-gray-600 mb-4">Site vitrine professionnel responsive avec domaine personnalisé inclus</p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li>✓ Design unique et moderne</li>
              <li>✓ Jusqu'à 5 pages</li>
              <li>✓ Optimisé mobile/tablet</li>
              <li>✓ Domaine .com inclus 1 an</li>
            </ul>
          </div>

          <!-- Carte de visite -->
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-id-card text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Carte de Visite</h3>
            <p class="text-gray-600 mb-4">Design professionnel cohérent avec votre identité visuelle</p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li>✓ Design personnalisé</li>
              <li>✓ Fichiers prêts à imprimer</li>
              <li>✓ Recto-verso</li>
              <li>✓ Formats standards</li>
            </ul>
          </div>

          <!-- Réseaux sociaux -->
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-share-alt text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Kit Réseaux Sociaux</h3>
            <p class="text-gray-600 mb-4">Templates et visuels prêts à utiliser pour vos réseaux sociaux</p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li>✓ Photo de profil adaptée</li>
              <li>✓ Bannière Facebook/LinkedIn</li>
              <li>✓ 5 templates Instagram</li>
              <li>✓ Stories templates</li>
            </ul>
          </div>

          <!-- Google Business -->
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-map-marker-alt text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Google Business</h3>
            <p class="text-gray-600 mb-4">Configuration de votre fiche Google My Business pour être trouvé localement</p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li>✓ Création de la fiche</li>
              <li>✓ Optimisation SEO local</li>
              <li>✓ Photos professionnelles</li>
              <li>✓ Formation à la gestion</li>
            </ul>
          </div>

          <!-- Email professionnel -->
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100 hover:shadow-lg transition-all duration-300">
            <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6">
              <i class="fas fa-envelope text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Email Professionnel</h3>
            <p class="text-gray-600 mb-4">Configuration d'une adresse email professionnelle à votre nom de domaine</p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li>✓ <EMAIL></li>
              <li>✓ Configuration complète</li>
              <li>✓ Synchronisation mobile</li>
              <li>✓ Formation à l'utilisation</li>
            </ul>
          </div>
        </div>

        <!-- Prix et CTA -->
        <div class="text-center bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl p-8 lg:p-12 text-white">
          <h3 class="text-3xl lg:text-4xl font-bold mb-4">Pack Création d'Entreprise Complet</h3>
          <div class="flex items-center justify-center gap-4 mb-6">
            <span class="text-2xl text-purple-200 line-through">2 500€</span>
            <span class="text-5xl lg:text-6xl font-black">1 890€</span>
            <span class="text-purple-200">TTC</span>
          </div>
          <p class="text-xl text-purple-100 mb-8">Économisez plus de 600€ avec ce pack tout-en-un</p>
          <button @click="openContactModal" class="bg-white text-purple-600 px-10 py-4 rounded-xl font-bold text-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
            Démarrer mon projet maintenant
          </button>
          <p class="text-sm text-purple-200 mt-4">Livraison en 15 jours ouvrés • Paiement en 2 fois sans frais</p>
        </div>
      </div>
    </section>

    <!-- Section processus -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Comment ça marche ?
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Un processus simple et efficace pour lancer votre entreprise en beauté
          </p>
        </div>

        <div class="grid md:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">1</div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Briefing créatif</h3>
            <p class="text-gray-600">Nous définissons ensemble votre identité, vos besoins et vos objectifs</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">2</div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Création & Design</h3>
            <p class="text-gray-600">Nous créons votre logo, site et tous vos supports visuels</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">3</div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Validation & Ajustements</h3>
            <p class="text-gray-600">Vous validez les créations et nous effectuons les ajustements nécessaires</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">4</div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Livraison & Formation</h3>
            <p class="text-gray-600">Nous livrons tout et vous formons à l'utilisation de vos outils</p>
          </div>
        </div>
      </div>
    </section>

    <Footer />
    
    <!-- Modale de contact -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<script setup>
import CarouselTop from "../TDI-modules/CarouselTop.vue"
import Navbar from "../TDI-modules/Navbar.vue"
import Footer from "../TDI-modules/Footer.vue"
import ContactModal from "../TDI-modules/ContactModal.vue"
import { ref, onMounted } from "vue"

// Animation de la section hero
const heroVisible = ref(false)
const textVisible = ref(false)
const ctaVisible = ref(false)
const isContactModalOpen = ref(false)

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true
}

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false
}

onMounted(() => {
  // Animation séquentielle des éléments
  setTimeout(() => { heroVisible.value = true }, 50)
  setTimeout(() => { textVisible.value = true }, 180)
  setTimeout(() => { ctaVisible.value = true }, 300)
})
</script>

<style scoped>
/* Animations personnalisées */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.6s ease-out 0.3s forwards;
}

.animate-fade-in-delayed-more {
  animation: fadeIn 0.6s ease-out 0.6s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style> 