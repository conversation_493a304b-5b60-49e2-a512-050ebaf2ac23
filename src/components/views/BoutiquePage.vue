<script setup>
import { ref, computed, onMounted } from 'vue'
import { getFeaturedProducts } from '../../data/products.js'
import TheShopFooter from '../TDI-modules/TheShopFooter.vue'
import ProductCard from '../TDI-modules/ProductCard.vue'
import ProductsView from '../TDI-modules/ProductsView.vue'

// Data réactive
const featuredProducts = computed(() => getFeaturedProducts())

// Features de la marque
const features = ref([
  {
    title: 'Design Unique',
    description: 'Chaque site est une création originale, pensée pour se démarquer et marquer les esprits.',
    icon: 'fas fa-palette'
  },
  {
    title: 'Code Premium',
    description: 'Technologies modernes, performances optimales et code structuré pour une expérience parfaite.',
    icon: 'fas fa-code'
  },
  {
    title: 'Support Dédié',
    description: 'Accompagnement personnalisé et support technique pour vous assurer une mise en ligne réussie.',
    icon: 'fas fa-heart'
  }
])

// Étapes du processus créatif
const processSteps = ref([
  {
    title: 'Inspiration',
    description: 'Recherche créative et veille technologique pour des concepts innovants.'
  },
  {
    title: 'Conception',
    description: 'Design système et prototypage pour une expérience utilisateur optimale.'
  },
  {
    title: 'Développement',
    description: 'Code artisanal avec les meilleures pratiques et technologies modernes.'
  },
  {
    title: 'Livraison',
    description: 'Package complet avec documentation et support pour votre succès.'
  }
])

// Méthodes
function scrollToAbout() {
  document.getElementById('about').scrollIntoView({ behavior: 'smooth' })
}

// Lifecycle
onMounted(() => {
  // Animation des formes flottantes
  animateFloatingShapes()
})

function animateFloatingShapes() {
  const shapes = document.querySelectorAll('.floating-shape')
  
  shapes.forEach((shape, index) => {
    const duration = 20 + index * 5
    const delay = index * 2
    
    shape.style.animation = `float ${duration}s ease-in-out ${delay}s infinite`
  })
}
</script>

<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-br from-b5 via-b4 to-b3 opacity-90"></div>
      
      <!-- Animations de fond -->
      <div class="absolute inset-0">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
      </div>
      
      <div class="container-custom relative z-10 text-center">
        <div class="animate-fade-in-up">
          <h1 class="text-6xl md:text-8xl font-bold text-gradient mb-6">
            La Collection
          </h1>
          <p class="text-2xl md:text-3xl text-b1 mb-8 font-light">
            TheDevImpact | Des sites web comme des œuvres d'art
          </p>
          <p class="text-lg md:text-xl text-b2 mb-12 max-w-3xl mx-auto leading-relaxed">
            Découvrez notre collection exclusive de sites web uniques, conçus avec passion et expertise. 
            Chaque création est une œuvre d'art numérique prête à sublimer votre présence en ligne.
          </p>
          
          <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <a href="#shop" class="btn-outline-b6 text-lg px-8 py-4">
              Explorer la Collection
            </a>
            <!-- <button @click="scrollToAbout" class="btn-outline text-lg px-8 py-4">
              Découvrir notre Vision
            </button> -->
          </div>
        </div>
      </div>
      
      <!-- Scroll indicator -->
      <div   class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-b3 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-b3 rounded-full mt-2"></div>
        </div>
      </div>
    </section>

 

    <!-- Products Section -->
    <ProductsView />



    

    <!-- Footer -->
    <TheShopFooter />
  </div>
</template>

<style scoped>
/* Formes flottantes animées */
.floating-shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  background: linear-gradient(45deg, var(--color-b3), var(--color-b6));
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 10%;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 15%;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 60%;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-30px) rotate(120deg);
  }
  66% {
    transform: translateY(20px) rotate(240deg);
  }
}

/* Animations personnalisées */
.animate-fade-in-up {
  animation: fadeInUp 1s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .floating-shape {
    display: none;
  }
}
</style> 