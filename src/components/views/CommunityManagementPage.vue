<script setup>
import { ref, onMounted } from 'vue'
import Navbar from "../TDI-modules/Navbar.vue"
import Footer from "../TDI-modules/Footer.vue"
import ContactModal from "../TDI-modules/ContactModal.vue"
import CarouselTop from "../TDI-modules/CarouselTop.vue"

const isContactModalOpen = ref(false)
const heroVisible = ref(false)
const textVisible = ref(false)

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true
}

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false
}

// Animations au montage
onMounted(() => {
  setTimeout(() => {
    heroVisible.value = true
  }, 300)
  
  setTimeout(() => {
    textVisible.value = true
  }, 600)
})

// Services de community management
const services = [
  {
    title: "Stratégie de contenu",
    description: "Développement d'une stratégie de contenu adaptée à votre audience et vos objectifs business",
    icon: "fas fa-lightbulb",
    color: "from-purple-500 to-purple-600",
    bgColor: "from-purple-50 to-pink-50",
    borderColor: "border-purple-100",
    features: [
      "Audit de votre présence actuelle",
      "Définition de votre ligne éditoriale",
      "Calendrier de publication personnalisé",
      "Identification de votre audience cible"
    ],
    price: "À partir de 300€"
  },
  {
    title: "Création de contenu",
    description: "Production de contenus visuels et textuels engageants pour vos réseaux sociaux",
    icon: "fas fa-camera",
    color: "from-blue-500 to-blue-600",
    bgColor: "from-blue-50 to-cyan-50",
    borderColor: "border-blue-100",
    features: [
      "Posts Instagram et Facebook",
      "Stories créatives et interactives",
      "Visuels sur-mesure avec Canva Pro",
      "Rédaction de captions engageantes"
    ],
    price: "À partir de 150€/semaine"
  },
  {
    title: "Gestion quotidienne",
    description: "Prise en charge complète de vos réseaux sociaux au quotidien",
    icon: "fas fa-calendar-alt",
    color: "from-green-500 to-green-600",
    bgColor: "from-green-50 to-emerald-50",
    borderColor: "border-green-100",
    features: [
      "Publication programmée",
      "Réponse aux commentaires et messages",
      "Veille concurrentielle",
      "Reporting mensuel détaillé"
    ],
    price: "À partir de 400€/mois"
  },
  {
    title: "Optimisation & croissance",
    description: "Optimisation de vos performances et développement de votre communauté",
    icon: "fas fa-chart-line",
    color: "from-orange-500 to-red-500",
    bgColor: "from-orange-50 to-red-50",
    borderColor: "border-orange-100",
    features: [
      "Analyse des performances",
      "Optimisation des hashtags",
      "Stratégies de croissance organique",
      "Conseils pour améliorer l'engagement"
    ],
    price: "À partir de 200€/mois"
  }
]

// Plateformes supportées
const platforms = [
  { name: "Instagram", icon: "fab fa-instagram", color: "text-pink-500" },
  { name: "Facebook", icon: "fab fa-facebook", color: "text-blue-600" },
  { name: "LinkedIn", icon: "fab fa-linkedin", color: "text-blue-700" },
  { name: "TikTok", icon: "fab fa-tiktok", color: "text-gray-800" }
]
</script>

<template>
  <div class="bg-black min-h-screen">
    <CarouselTop />
    <Navbar />

    <!-- Section Hero -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Image de fond -->
      <div class="absolute inset-0">
        <img 
          src="../../assets/branding.webp" 
          alt="Community management professionnel"
          class="w-full h-full object-cover"
        />
        <!-- Overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-b from-pink-600/60 via-black/75 to-black"></div>
      </div>

      <!-- Contenu -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-pink-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 text-white text-sm font-medium mb-8 opacity-0" :class="{ 'animate-fade-in': heroVisible }">
          <span class="w-2 h-2 rounded-full bg-pink-400 mr-2 animate-pulse"></span>
          Community Management
          <span class="w-2 h-2 rounded-full bg-purple-400 ml-2 animate-pulse"></span>
        </div>

        <!-- Titre principal -->
        <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight opacity-0" :class="{ 'animate-fade-in-delayed': textVisible }">
          Développez votre
          <span class="block text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-purple-400">
            communauté
          </span>
        </h1>

        <!-- Sous-titre -->
        <p class="max-w-3xl mx-auto text-xl sm:text-2xl text-white/80 mb-10 leading-relaxed opacity-0" :class="{ 'animate-fade-in-delayed-2': textVisible }">
          Stratégie, création de contenu et gestion de vos réseaux sociaux pour faire grandir votre audience
        </p>

        <!-- Boutons CTA -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center opacity-0" :class="{ 'animate-fade-in-delayed-3': textVisible }">
          <a 
            href="#services" 
            class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-pink-500 to-purple-500 text-white font-semibold rounded-2xl hover:shadow-2xl hover:shadow-pink-500/25 transform hover:-translate-y-1 transition-all duration-300"
          >
            Découvrir nos services
            <i class="fas fa-arrow-down ml-2"></i>
          </a>
          <button 
            @click="openContactModal"
            class="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-2xl hover:bg-white/10 hover:border-white/50 transition-all duration-300"
          >
            <i class="fas fa-envelope mr-2"></i>
            Demander un devis
          </button>
        </div>

        <!-- Plateformes supportées -->
        <div class="mt-12 opacity-0" :class="{ 'animate-fade-in-delayed-4': textVisible }">
          <p class="text-white/60 text-sm mb-4">Plateformes que nous gérons :</p>
          <div class="flex justify-center space-x-6">
            <div 
              v-for="platform in platforms" 
              :key="platform.name"
              class="flex flex-col items-center"
            >
              <div class="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center mb-2 border border-white/20">
                <i :class="[platform.icon, 'text-2xl text-white']"></i>
              </div>
              <span class="text-white/70 text-xs">{{ platform.name }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>

    <!-- Section Services -->
    <section id="services" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-pink-100 to-purple-100 text-pink-700 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-pink-500 mr-2"></span>
            Nos services
            <span class="w-2 h-2 rounded-full bg-purple-500 ml-2"></span>
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Une approche complète
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600">
              de votre présence sociale
            </span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            De la stratégie à la gestion quotidienne, nous vous accompagnons pour développer votre communauté
          </p>
        </div>

        <!-- Grille des services -->
        <div class="grid lg:grid-cols-2 gap-8 mb-16">
          <div
            v-for="(service, index) in services"
            :key="index"
            :class="`bg-gradient-to-br ${service.bgColor} rounded-2xl p-8 border ${service.borderColor} hover:shadow-lg transition-all duration-300`"
          >
            <!-- Icône -->
            <div :class="`w-16 h-16 bg-gradient-to-r ${service.color} rounded-2xl flex items-center justify-center mb-6`">
              <i :class="[service.icon, 'text-white text-2xl']"></i>
            </div>

            <!-- Titre et prix -->
            <div class="mb-4">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ service.title }}</h3>
              <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/50 text-gray-700 border border-gray-200">
                {{ service.price }}
              </div>
            </div>

            <!-- Description -->
            <p class="text-gray-600 mb-6">{{ service.description }}</p>

            <!-- Features -->
            <ul class="text-sm text-gray-500 space-y-3 mb-6">
              <li 
                v-for="(feature, fIndex) in service.features"
                :key="fIndex"
                class="flex items-start"
              >
                <i class="fas fa-check text-pink-500 mr-2 mt-1 text-xs"></i>
                <span>{{ feature }}</span>
              </li>
            </ul>

            <!-- CTA -->
            <button 
              @click="openContactModal"
              class="w-full px-4 py-3 bg-gradient-to-r from-pink-500 to-purple-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-pink-500/25 transform hover:-translate-y-0.5 transition-all duration-300"
            >
              Demander ce service
            </button>
          </div>
        </div>

        <!-- Section CTA finale -->
        <div class="text-center">
          <div class="bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl border border-pink-100 p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
              Prêt à développer votre communauté ?
            </h3>
            <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
              Discutons de votre projet et créons ensemble une stratégie social media qui vous ressemble.
            </p>
            <button 
              @click="openContactModal"
              class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-pink-500 to-purple-500 text-white font-semibold rounded-xl hover:shadow-2xl hover:shadow-pink-500/25 transform hover:-translate-y-1 transition-all duration-300"
            >
              <i class="fas fa-envelope mr-2"></i>
              Commencer maintenant
            </button>
          </div>
        </div>
      </div>
    </section>

    <Footer />
    
    <!-- Contact Modal -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
  </div>
</template>

<style scoped>
/* Animations personnalisées */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.8s ease-out 0.2s forwards;
}

.animate-fade-in-delayed-2 {
  animation: fadeIn 0.8s ease-out 0.4s forwards;
}

.animate-fade-in-delayed-3 {
  animation: fadeIn 0.8s ease-out 0.6s forwards;
}

.animate-fade-in-delayed-4 {
  animation: fadeIn 0.8s ease-out 0.8s forwards;
}

/* Scroll smooth */
html {
  scroll-behavior: smooth;
}
</style>
