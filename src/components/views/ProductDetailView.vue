<template>
  <div class="min-h-screen bg-white" v-if="product">
    <!-- Header Navigation -->
    <header class="bg-white shadow-sm border-b border-slate-200/50 sticky top-0 z-50">
      <div class="container mx-auto px-6 lg:px-8">
        <div class="flex items-center justify-between h-20">
          <!-- Logo -->
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-b3 to-b6 rounded-xl flex items-center justify-center mr-3">
              <span class="text-white font-bold text-lg">TD</span>
            </div>
            <span class="text-2xl font-black text-slate-900">TheDevImpact</span>
          </div>
          
          <!-- Navigation -->
          <div class="hidden md:flex items-center space-x-8">
            <a href="/" class="text-slate-700 hover:text-b3 font-semibold transition-colors">Accueil</a>
            <a href="/formules" class="text-slate-700 hover:text-b3 font-semibold transition-colors">Création sur-mesure</a>
            <a href="/boutique" class="text-b6 hover:text-b6 font-semibold transition-colors">La Collection</a>
            <button @click="scrollToBottom" class="text-slate-700 hover:text-b3 font-semibold transition-colors">Contact</button>
            
            <!-- Cart Icon -->
            <div class="relative">
              <button @click="toggleCart" class="p-2 text-slate-600 hover:text-b3 transition-colors">
                <i class="fas fa-shopping-cart text-lg"></i>
                <span v-if="cartItemsCount > 0" class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-b3 to-b6 rounded-full flex items-center justify-center text-white text-xs font-bold">{{ cartItemsCount }}</span>
              </button>
            </div>
          </div>

          <!-- Menu Mobile Button -->
          <button class="md:hidden p-2 text-slate-600">
            <i class="fas fa-bars text-lg"></i>
          </button>
        </div>
      </div>
    </header>

    <!-- Détails du produit -->
    <section class="py-8">
      <div class="container mx-auto px-6 lg:px-8">
        <div class="grid lg:grid-cols-2 gap-12">
          <!-- Galerie d'images -->
          <div class="space-y-4">
            <!-- Image principale -->
            <div class="mb-4">
              <img :src="selectedImage" 
                   :alt="product.name"
                   class="w-full h-96 lg:h-[500px] object-cover rounded-lg shadow-lg" />
            </div>
            
            <!-- Boutons d'actions -->
            <div class="flex gap-3 mb-4">
              <button @click="openLightbox" class="flex-1 bg-white border border-gray-300 hover:bg-gray-50 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center">
                <i class="fas fa-expand-arrows-alt mr-2"></i>
                Agrandir
              </button>
              <button @click="requestDemo" class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center">
                <i class="fas fa-vial mr-2"></i>
                Accéder à la démo
              </button>
            </div>
            
            <!-- Miniatures -->
            <div class="grid grid-cols-4 gap-3">
              <button v-for="(image, index) in product.images" 
                      :key="index"
                      @click="selectedImageIndex = index"
                      :class="{ 'ring-2 ring-b3': selectedImageIndex === index }"
                      class="aspect-square rounded-lg overflow-hidden hover:ring-2 hover:ring-b3/50 transition-all">
                <img :src="image" 
                     :alt="`${product.name} - Image ${index + 1}`"
                     class="w-full h-full object-cover" />
              </button>
            </div>
          </div>
          
          <!-- Informations produit -->
          <div class="lg:pl-8">
            <!-- Bouton retour en haut -->
            <div class="mb-4">
              <button @click="goBack" class="inline-flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg transition-colors text-sm">
                <i class="fas fa-arrow-left mr-2"></i>
                Retour
              </button>
            </div>
            
            <!-- En-tête -->
            <div class="mb-6">
              <div class="flex items-center gap-3 mb-4">
                <span class="bg-b3 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {{ product.category }}
                </span>
              </div>
              
              <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-3">{{ product.name }}</h1>
              <p class="text-lg text-gray-600 mb-6">{{ product.subtitle }}</p>
            </div>
            
            <!-- Prix -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
              <div v-if="product.promotion" class="flex items-center gap-2 mb-3">
                <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                  {{ product.promotion.label }}
                </span>
                <span class="text-sm text-red-600 font-medium">
                  Jusqu'au {{ formatDate(product.promotion.endDate) }}
                </span>
              </div>
              
              <div class="flex items-center gap-4 mb-4">
                <span class="text-3xl font-bold text-gray-900">{{ $formatPrice(product.price) }}</span>
                <span v-if="product.originalPrice && product.originalPrice > product.price" 
                      class="text-xl text-gray-400 line-through">
                  {{ $formatPrice(product.originalPrice) }}
                </span>
              </div>
              
              <div class="space-y-4 text-sm">
                <div class="bg-white rounded-lg p-4 border border-gray-200">
                  <h4 class="font-semibold text-gray-900 mb-3">Ce qui est inclus</h4>
                  
                  <div class="space-y-3">
                    <div class="flex items-start">
                      <i class="fas fa-file-code text-blue-500 mr-3 mt-1"></i>
                      <div>
                        <span class="text-gray-700 font-medium">Fichiers sources complets</span>
                        <p class="text-xs text-gray-500 mt-1">HTML, CSS, JavaScript - tous les fichiers pour personnaliser votre site</p>
                      </div>
                    </div>
                    
                    <div class="flex items-start">
                      <i class="fas fa-mobile-alt text-purple-500 mr-3 mt-1"></i>
                      <div>
                        <span class="text-gray-700 font-medium">Design responsive</span>
                        <p class="text-xs text-gray-500 mt-1">Optimisé pour mobile, tablette et desktop</p>
                      </div>
                    </div>
                    
                    <div class="flex items-start">
                      <i class="fas fa-cloud text-blue-500 mr-3 mt-1"></i>
                      <div>
                        <span class="text-gray-700 font-medium">Hébergement & nom de domaine</span>
                        <p class="text-xs text-gray-500 mt-1">Hébergement inclus sur Netlify (https://nom-entreprise.netlify.app)<br>Domaine personnalisé en option (environ 12-14 euros)</p>
                      </div>
                    </div>
                    
                    <div class="flex items-start">
                      <i class="fas fa-search text-green-500 mr-3 mt-1"></i>
                      <div>
                        <span class="text-gray-700 font-medium">Référencement SEO</span>
                        <p class="text-xs text-gray-500 mt-1">Balises de base (titres, descriptions, structure claire)</p>
                      </div>
                    </div>
                    
                    <div class="flex items-start">
                      <i class="fas fa-tools text-orange-500 mr-3 mt-1"></i>
                      <div>
                        <span class="text-gray-700 font-medium">Maintenance</span>
                        <p class="text-xs text-gray-500 mt-1">1 mois inclus (fix bugs, mises à jour, optimisations)</p>
                      </div>
                    </div>
                    
                    <div class="flex items-start">
                      <i class="fas fa-edit text-purple-500 mr-3 mt-1"></i>
                      <div>
                        <span class="text-gray-700 font-medium">Modifications incluses</span>
                        <p class="text-xs text-gray-500 mt-1">3 changements mineurs pendant 1 mois</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Mention des options additionnelles -->
                <div class="mt-4 p-3 bg-gray-50 rounded-lg border-l-4 border-purple-500">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <i class="fas fa-plus-circle text-purple-600 mr-2"></i>
                      <span class="text-sm text-gray-700">
                        <strong>8 options additionnelles</strong> disponibles pour personnaliser davantage votre site
                      </span>
                    </div>
                    <button @click="scrollToOptions" 
                            class="bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-1 px-3 rounded-md transition-colors">
                      Voir les options
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Description -->
            <div class="mb-8">
              <p class="text-gray-700 leading-relaxed">{{ product.description }}</p>
            </div>
            
            <!-- Technologies -->
            <div class="mb-8">
              <h3 class="text-lg font-bold text-slate-900 mb-4 flex items-center">
                <i class="fas fa-code text-b3 mr-3"></i>
                Technologies Utilisées
              </h3>
              <div class="grid grid-cols-2 gap-3">
                <span v-for="tech in product.technologies" 
                      :key="tech"
                      class="inline-flex items-center px-4 py-3 bg-white border border-gray-200 hover:border-b3 text-slate-700 rounded-xl font-medium transition-all duration-300 hover:shadow-md">
                  <i class="fas fa-check-circle text-green-500 mr-3"></i>
                  {{ tech }}
                </span>
              </div>
            </div>

            <!-- Actions principales -->
            <div class="mb-8 space-y-3">
              <button @click="addToCart" 
                      :disabled="isAddingToCart"
                      class="add-to-cart-btn w-full bg-b3 hover:bg-b4 text-white font-semibold py-3 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      :class="{ 'bg-green-600': isInCart }">
                <i v-if="!isAddingToCart" :class="isInCart ? 'fas fa-check' : 'fas fa-shopping-cart'" class="mr-2"></i>
                <div v-if="isAddingToCart" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {{ isAddingToCart ? 'Ajout en cours...' : isInCart ? 'Déjà dans le panier' : 'Ajouter au panier' }}
              </button>
              
              <button @click="buyNow" 
                      :disabled="isBuyingNow"
                      class="w-full bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 font-semibold py-3 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
                <i v-if="!isBuyingNow" class="fas fa-credit-card mr-2"></i>
                <div v-if="isBuyingNow" class="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-700 mr-2"></div>
                {{ isBuyingNow ? 'Redirection...' : 'Acheter maintenant' }}
              </button>
            </div>
            

          </div>
        </div>
      </div>
    </section>

    <!-- Description détaillée -->
    <section class="py-8 bg-gray-50">
      <div class="container mx-auto px-6 lg:px-8">
        <div class="max-w-4xl mx-auto">
          <h2 class="text-2xl font-bold text-gray-900 mb-6">Description du produit</h2>
          <div class="bg-white rounded-lg p-8 border border-gray-200">
            <!-- Description principale -->
            <div class="mb-8">
              <p class="text-lg text-gray-700 leading-relaxed mb-6">
                Faites rayonner votre restaurant avec un site vitrine élégant, moderne et immersif. Ce modèle a été conçu pour impressionner vos visiteurs dès les premières secondes, tout en présentant votre établissement sous son meilleur jour.
              </p>
            </div>
            
            <!-- Idéal pour -->
            <div class="mb-8">
              <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-users text-blue-500 mr-3"></i>
                Idéal pour
              </h3>
              <div class="grid md:grid-cols-2 gap-4">
                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                  <i class="fas fa-utensils text-blue-500 mr-3"></i>
                  <span class="text-gray-700">Restaurants gastronomiques</span>
                </div>
                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                  <i class="fas fa-pizza-slice text-blue-500 mr-3"></i>
                  <span class="text-gray-700">Bistros, brasseries, pizzerias</span>
                </div>
                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                  <i class="fas fa-truck text-blue-500 mr-3"></i>
                  <span class="text-gray-700">Foodtrucks et food courts</span>
                </div>
                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                  <i class="fas fa-birthday-cake text-blue-500 mr-3"></i>
                  <span class="text-gray-700">Pâtisseries et boulangeries</span>
                </div>
                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                  <i class="fas fa-wine-glass text-blue-500 mr-3"></i>
                  <span class="text-gray-700">Bars et cavistes</span>
                </div>
                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                  <i class="fas fa-cocktail text-blue-500 mr-3"></i>
                  <span class="text-gray-700">Bars à cocktails</span>
                </div>
              </div>
            </div>
            
            <!-- Processus -->
            <div class="mt-8 p-6 bg-gray-50 rounded-lg">
              <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-cog text-orange-500 mr-3"></i>
                Comment ça marche
              </h3>
              <div class="grid md:grid-cols-3 gap-6">
                <div class="text-center">
                  <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">1</div>
                  <h4 class="font-semibold text-gray-900 mb-2">Vous achetez</h4>
                  <p class="text-sm text-gray-600">Commandez ce modèle en un clic</p>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">2</div>
                  <h4 class="font-semibold text-gray-900 mb-2">Vous fournissez</h4>
                  <p class="text-sm text-gray-600">Envoyez vos contenus (textes, photos, logo)</p>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">3</div>
                  <h4 class="font-semibold text-gray-900 mb-2">C'est prêt !</h4>
                  <p class="text-sm text-gray-600">Site personnalisé livré sous 5-10 jours</p>
                </div>
              </div>
            </div>
            
            <!-- Note sur personnalisation -->
            <div class="mt-8 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
              <p class="text-gray-700 flex items-center">
                <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                <span><strong>Besoin d'options spécifiques ?</strong> Menu interactif, réservation en ligne, design personnalisé... Contactez-nous pour un devis sur mesure.</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Options additionnelles -->
    <ProductOptions />

    <!-- Produits similaires -->
    <section v-if="similarProducts.length > 0" class="py-8 bg-white">
      <div class="container mx-auto px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Produits similaires</h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ProductCard 
            v-for="similarProduct in similarProducts" 
            :key="similarProduct.id" 
            :product="similarProduct"
          />
        </div>
      </div>
    </section>

    <!-- Lightbox pour images -->
    <div v-if="showLightbox" 
         class="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
         @click="closeLightbox">
      <div class="relative max-w-4xl max-h-full">
        <img :src="selectedImage" 
             :alt="product.name"
             class="w-full h-full object-contain" />
        <button @click="closeLightbox" 
                class="absolute top-4 right-4 w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </div>
  
  <!-- Page 404 si produit non trouvé -->
  <div v-else class="min-h-screen pt-20 flex items-center justify-center">
    <div class="text-center">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">Produit Non Trouvé</h1>
      <p class="text-xl text-gray-600 mb-8">Ce produit n'existe pas ou a été retiré de notre collection.</p>
      <router-link to="/boutique" class="bg-b3 text-white px-6 py-3 rounded-lg font-semibold hover:bg-b4 transition-colors">
        Retour à la Collection
      </router-link>
    </div>
  </div>
  
    <!-- Footer -->
    <TheShopFooter />
  
    <!-- Cart Sidebar -->
    <CartSidebar :isOpen="cartStore.isOpen" @close="cartStore.closeCart" />
    
    <!-- Contact Modal for Quote -->
    <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
    
    <!-- Modale de demande de démo -->
    <DemoModal 
      :isOpen="showDemoModal" 
      :product="product" 
      @close="closeDemoModal" 
    />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getProductById, products } from '../../data/products.js'
import { calculateDiscount as calcDiscount } from '../../services/stripe.js'
import { useCartStore } from '../../store/cart.js'
import ProductCard from '../TDI-modules/ProductCard.vue'
import CartSidebar from '../TDI-modules/CartSidebar.vue'
import TheShopFooter from '../TDI-modules/TheShopFooter.vue'
import ContactModal from '../TDI-modules/ContactModal.vue'
import DemoModal from '../TDI-modules/DemoModal.vue'
import ProductOptions from '../TDI-modules/ProductOptions.vue'

// Composables
const route = useRoute()
const router = useRouter()
const cartStore = useCartStore()

// État réactif
const selectedImageIndex = ref(0)
const showLightbox = ref(false)
const isInWishlist = ref(false)
const isAddingToCart = ref(false)
const isBuyingNow = ref(false)
const isContactModalOpen = ref(false)
const showDemoModal = ref(false)

// Computed
const product = computed(() => getProductById(route.params.id))

const selectedImage = computed(() => {
  return product.value?.images[selectedImageIndex.value] || ''
})

const similarProducts = computed(() => {
  if (!product.value) return []
  
  return products
    .filter(p => p.id !== product.value.id && p.category === product.value.category)
    .slice(0, 3)
})

const isInCart = computed(() => {
  return product.value ? cartStore.isInCart(product.value.id) : false
})

const cartItemsCount = computed(() => cartStore.itemsCount)

// Méthodes
function calculateDiscount() {
  if (!product.value?.originalPrice || product.value.originalPrice <= product.value.price) {
    return 0
  }
  return calcDiscount(product.value.originalPrice, product.value.price)
}

function openLightbox() {
  showLightbox.value = true
}

function closeLightbox() {
  showLightbox.value = false
}

function addToCart() {
  if (!product.value || isAddingToCart.value) return
  
  isAddingToCart.value = true
  
  try {
    const success = cartStore.addItem(product.value, 1)
    
    if (success) {
      // Feedback visuel supplémentaire
      const button = document.querySelector('.add-to-cart-btn')
      if (button) {
        button.classList.add('animate-pulse')
        setTimeout(() => {
          button.classList.remove('animate-pulse')
        }, 1000)
      }
    }
  } catch (error) {
    console.error('Erreur lors de l\'ajout au panier:', error)
  } finally {
    isAddingToCart.value = false
  }
}

async function buyNow() {
  if (!product.value || isBuyingNow.value) return
  
  isBuyingNow.value = true
  
  try {
    // Ajouter au panier d'abord
    const success = cartStore.addItem(product.value, 1)
    
    if (success) {
      // Préparer les données pour Stripe
      const lineItems = [{
        price_data: {
          currency: 'eur',
          product_data: {
            name: product.value.name,
            description: product.value.subtitle,
            images: [product.value.images[0]]
          },
          unit_amount: Math.round(product.value.price * 100) // Stripe utilise les centimes
        },
        quantity: 1
      }]

      // Appeler la fonction Netlify pour créer la session Stripe
      const response = await fetch('/.netlify/functions/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          line_items: lineItems,
          success_url: `${window.location.origin}/success`,
          cancel_url: `${window.location.origin}/product/${product.value.id}`
        })
      })

      const { url, error } = await response.json()

      if (error) {
        throw new Error(error)
      }

      // Rediriger vers Stripe Checkout
      window.location.href = url
    }
  } catch (error) {
    console.error('Erreur lors de l\'achat direct:', error)
    window.dispatchEvent(new CustomEvent('show-toast', {
      detail: {
        type: 'error',
        title: 'Erreur',
        message: 'Une erreur est survenue lors du paiement. Veuillez réessayer.'
      }
    }))
  } finally {
    isBuyingNow.value = false
  }
}

function toggleWishlist() {
  isInWishlist.value = !isInWishlist.value
  
  // Sauvegarder dans localStorage
  try {
    const wishlist = JSON.parse(localStorage.getItem('thedevimpact-wishlist') || '[]')
    
    if (isInWishlist.value) {
      if (!wishlist.includes(product.value.id)) {
        wishlist.push(product.value.id)
        window.dispatchEvent(new CustomEvent('show-toast', {
          detail: {
            type: 'success',
            title: 'Ajouté aux favoris',
            message: `${product.value.name} a été ajouté à vos favoris`
          }
        }))
      }
    } else {
      const index = wishlist.indexOf(product.value.id)
      if (index > -1) {
        wishlist.splice(index, 1)
        window.dispatchEvent(new CustomEvent('show-toast', {
          detail: {
            type: 'info',
            title: 'Retiré des favoris',
            message: `${product.value.name} a été retiré de vos favoris`
          }
        }))
      }
    }
    
    localStorage.setItem('thedevimpact-wishlist', JSON.stringify(wishlist))
  } catch (error) {
    console.error('Erreur lors de la gestion des favoris:', error)
  }
}

function goBack() {
  // Retour à la page précédente ou à la boutique si pas d'historique
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/boutique')
  }
}

function toggleCart() {
  cartStore.toggleCart()
}

function scrollToBottom() {
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: 'smooth'
  })
}

function scrollToOptions() {
  // Chercher la section des options additionnelles de manière compatible
  const headings = document.querySelectorAll('h2')
  let optionsSection = null
  
  for (const heading of headings) {
    if (heading.textContent && heading.textContent.includes('Options additionnelles')) {
      optionsSection = heading.closest('section')
      break
    }
  }
  
  if (optionsSection) {
    optionsSection.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  } else {
    // Fallback : scroller vers les 2/3 de la page
    const targetPosition = document.body.scrollHeight * 0.65
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    })
  }
}

function requestDemo() {
  showDemoModal.value = true
}

function requestQuote() {
  isContactModalOpen.value = true
}

function closeContactModal() {
  isContactModalOpen.value = false
}

function closeDemoModal() {
  showDemoModal.value = false
}

function formatDate(dateString) {
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

// Lifecycle
onMounted(() => {
  // Vérifier si le produit existe
  if (!product.value) {
    console.warn('Produit non trouvé:', route.params.id)
  }
  
  // Mettre à jour le titre de la page
  if (product.value) {
    document.title = `${product.value.name} - TheDevImpact`
  }
  
  // Charger l'état des favoris
  try {
    const wishlist = JSON.parse(localStorage.getItem('thedevimpact-wishlist') || '[]')
    isInWishlist.value = wishlist.includes(route.params.id)
  } catch (error) {
    console.error('Erreur lors du chargement des favoris:', error)
  }
})
</script>

<style scoped>
/* Animation pour les images */
.aspect-square {
  aspect-ratio: 1;
}
</style> 