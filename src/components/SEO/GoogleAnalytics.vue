<script setup>
import { onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';

// ID de mesure Google Analytics depuis les variables d'environnement
const measurementId = import.meta.env.VITE_GA_MEASUREMENT_ID;

// Initialisation de Google Analytics
const initGoogleAnalytics = () => {
  if (!measurementId) return;
  
  // Création des balises script pour Google Analytics
  const gtagScript = document.createElement('script');
  gtagScript.async = true;
  gtagScript.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
  document.head.appendChild(gtagScript);
  
  // Configuration de Google Analytics
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    window.dataLayer.push(arguments);
  }
  gtag('js', new Date());
  gtag('config', measurementId, { 'anonymize_ip': true });
  
  // Définition de la fonction gtag globalement
  window.gtag = gtag;
};

// Envoi d'un événement de changement de page
const trackPageView = (path) => {
  if (!measurementId || !window.gtag) return;
  
  window.gtag('config', measurementId, {
    'page_path': path,
    'anonymize_ip': true
  });
};

// Initialisation au montage du composant
onMounted(() => {
  // Vérification que nous ne sommes pas en mode développement
  if (import.meta.env.MODE !== 'development') {
    initGoogleAnalytics();
    trackPageView(window.location.pathname);
  }
});

// Suivi des changements de route
const route = useRoute();
watch(() => route.path, (newPath) => {
  if (import.meta.env.MODE !== 'development') {
    trackPageView(newPath);
  }
});
</script>

<template>
  <!-- Composant sans rendu visuel -->
</template>
