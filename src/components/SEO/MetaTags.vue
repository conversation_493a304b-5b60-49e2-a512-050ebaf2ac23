<script setup>
import { onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { injectStructuredData, websiteSchema, organizationSchema } from '../../config/structuredData';

const props = defineProps({
  title: {
    type: String,
    default: 'The Dev Impact | Création de Sites Web Sur Mesure et Performants'
  },
  description: {
    type: String,
    default: 'Agence spécialisée dans la création de sites web sur mesure, performants et optimisés pour convertir vos visiteurs en clients.'
  },
  keywords: {
    type: String,
    default: 'création site web, développement web, site sur mesure, site vitrine, site responsive, agence web, performance web'
  },
  ogImage: {
    type: String,
    default: 'https://thedevimpact.com/og-image.jpg'
  },
  twitterImage: {
    type: String,
    default: 'https://thedevimpact.com/twitter-image.jpg'
  },
  canonicalUrl: {
    type: String,
    default: ''
  }
});

const route = useRoute();

// Fonction pour mettre à jour les balises meta
const updateMetaTags = () => {
  // Titre de la page
  document.title = props.title;

  // Meta description
  const metaDescription = document.querySelector('meta[name="description"]');
  if (metaDescription) {
    metaDescription.setAttribute('content', props.description);
  } else {
    const meta = document.createElement('meta');
    meta.name = 'description';
    meta.content = props.description;
    document.head.appendChild(meta);
  }

  // Meta keywords
  const metaKeywords = document.querySelector('meta[name="keywords"]');
  if (metaKeywords) {
    metaKeywords.setAttribute('content', props.keywords);
  } else {
    const meta = document.createElement('meta');
    meta.name = 'keywords';
    meta.content = props.keywords;
    document.head.appendChild(meta);
  }

  // Open Graph
  updateOpenGraphTags();

  // Twitter Cards
  updateTwitterTags();

  // Canonical URL
  updateCanonicalUrl();
};

// Mise à jour des balises Open Graph
const updateOpenGraphTags = () => {
  const tags = [
    { property: 'og:title', content: props.title },
    { property: 'og:description', content: props.description },
    { property: 'og:image', content: props.ogImage },
    { property: 'og:url', content: window.location.href }
  ];

  tags.forEach(tag => {
    const element = document.querySelector(`meta[property="${tag.property}"]`);
    if (element) {
      element.setAttribute('content', tag.content);
    } else {
      const meta = document.createElement('meta');
      meta.setAttribute('property', tag.property);
      meta.setAttribute('content', tag.content);
      document.head.appendChild(meta);
    }
  });
};

// Mise à jour des balises Twitter
const updateTwitterTags = () => {
  const tags = [
    { property: 'twitter:title', content: props.title },
    { property: 'twitter:description', content: props.description },
    { property: 'twitter:image', content: props.twitterImage },
    { property: 'twitter:url', content: window.location.href }
  ];

  tags.forEach(tag => {
    const element = document.querySelector(`meta[property="${tag.property}"]`);
    if (element) {
      element.setAttribute('content', tag.content);
    } else {
      const meta = document.createElement('meta');
      meta.setAttribute('property', tag.property);
      meta.setAttribute('content', tag.content);
      document.head.appendChild(meta);
    }
  });
};

// Mise à jour de l'URL canonique
const updateCanonicalUrl = () => {
  const canonicalLink = document.querySelector('link[rel="canonical"]');
  const url = props.canonicalUrl || window.location.origin + route.path;

  if (canonicalLink) {
    canonicalLink.setAttribute('href', url);
  } else {
    const link = document.createElement('link');
    link.rel = 'canonical';
    link.href = url;
    document.head.appendChild(link);
  }
};

// Mise à jour des balises au montage du composant
onMounted(() => {
  updateMetaTags();

  // Injection des données structurées Schema.org
  injectStructuredData(websiteSchema);
  injectStructuredData(organizationSchema);
});

// Mise à jour des balises lorsque les props changent
watch(props, () => {
  updateMetaTags();
});

// Mise à jour des balises lorsque la route change
watch(() => route.path, () => {
  updateMetaTags();
});
</script>

<template>
  <!-- Composant sans rendu visuel -->
</template>
