<script setup>
import { ref, onMounted } from "vue";
import {
  RocketLaunchIcon,
  LightBulbIcon,
  DevicePhoneMobileIcon,
  CodeBracketIcon,
  CursorArrowRaysIcon,
} from "@heroicons/vue/24/solid";

// Animation des éléments
const titleVisible = ref(false);
const cardsVisible = ref(false);
const buttonVisible = ref(false);

// Données des cartes
const features = [
  {
    title: "Optimisé et rapide",
    subtitle: "Performances optimisées",
    icon: RocketLaunchIcon,
    description:
      'Un site performant, <span class="font-medium text-b6">ultra-rapide</span>, et entièrement <span class="font-medium text-b6">responsive</span> pour tous vos appareils.',
    gradient: "from-b1 via-b2 to-b3",
    stats: [
      { value: "2x", label: "plus rapide", color: "text-b6" },
      { value: "100%", label: "responsive", color: "text-b4" },
    ],
    techIcons: [CodeBracketIcon, CursorArrowRaysIcon],
  },
  {
    title: "Modulaire et évolutif",
    subtitle: "Flexibilité et évolutivité",
    icon: LightBulbIcon,
    description:
      'Ajoutez des sections selon vos besoins. Une structure complètement <span class="font-medium text-b6">flexible</span> pour chaque projet.',
    gradient: "from-b2 via-b3 to-b4",
    stats: [
      { value: "+XX", label: "modules", color: "text-b6" },
      { value: "100%", label: "personnalisable", color: "text-b3" },
    ],
    techIcons: [CodeBracketIcon, CursorArrowRaysIcon],
  },
  {
    title: "Design unique",
    subtitle: "Design personnalisé",
    icon: DevicePhoneMobileIcon,
    description:
      'Un site qui vous ressemble, avec un design totalement <span class="font-medium text-b6">personnalisé</span>, loin des templates génériques.',
    gradient: "from-b3 via-b2 to-b1",
    stats: [
      { value: "100%", label: "sur mesure", color: "text-b6" },
      { value: "0", label: "template", color: "text-b4" },
    ],
    techIcons: [CodeBracketIcon, CursorArrowRaysIcon],
  },
];

onMounted(() => {
  // Animation séquentielle des éléments
  setTimeout(() => {
    titleVisible.value = true;
  }, 300);
  setTimeout(() => {
    cardsVisible.value = true;
  }, 800);
  setTimeout(() => {
    buttonVisible.value = true;
  }, 1500);
});
</script>

<template>
  <section
    id="services"
    class="relative overflow-hidden bg-gradient-to-b from-white to-gray-50 pt-8 pb-8 lg:py-24"
    aria-label="Nos services et avantages"
  >
    <!-- Fond avec motif et formes -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- Motif de points -->
      <div
        class="absolute inset-0 opacity-[0.03]"
        style="
          background-image: radial-gradient(#57737a 1px, transparent 1px);
          background-size: 30px 30px;
        "
      ></div>

      <!-- Formes décoratives modernes -->
      <div
        class="absolute -top-40 -left-40 w-[500px] h-[500px] bg-gradient-to-br from-b3/10 to-b4/5 rounded-full filter blur-3xl"
      ></div>
      <div
        class="absolute -bottom-20 -right-20 w-[400px] h-[400px] bg-gradient-to-tl from-b6/10 to-b3/5 rounded-full filter blur-3xl"
      ></div>

      <!-- Lignes décoratives -->
      <div
        class="absolute top-1/4 left-0 right-0 h-px bg-gradient-to-r from-transparent via-b3/20 to-transparent"
      ></div>
      <div
        class="absolute bottom-1/4 left-0 right-0 h-px bg-gradient-to-r from-transparent via-b6/20 to-transparent"
      ></div>
    </div>

    <!-- Contenu principal -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- En-tête avec animation -->
      <header class="text-center mb-6 md:mb-20" :class="{ 'animate-fade-in': titleVisible }">
        <!-- Badge moderne -->
        <div
          class="inline-flex items-center px-3 py-1 md:px-4 md:py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-xs md:text-sm font-medium mb-3 md:mb-8 shadow-sm"
        >
          <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
          Approche modulaire
          <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
        </div>

        <!-- Titre principal avec design moderne -->
        <h2
          class="text-3xl sm:text-5xl lg:text-6xl font-bold tracking-tight mb-3 md:mb-8 text-gray-800"
        >
          <span>Un site web </span>
          <span class="relative inline-block">
            <span
              class="relative z-10 bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text"
              >sur-mesure</span
            >
            <span
              class="absolute bottom-2 left-0 w-full h-3 bg-b3/20 -rotate-1 z-0"
            ></span>
          </span>
          <span>, flexible et </span>
          <span class="bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text"
            >impactant</span
          >
        </h2>

        <!-- Sous-titre avec design moderne -->
        <p
          class="max-w-3xl mx-auto text-base sm:text-lg md:text-xl text-gray-600 font-light leading-relaxed mb-0"
        >
        <span class="font-bold">Notre objectif ·</span> vous livrer un site dont vous serez fier·e, prêt à convaincre vos futurs clients, et qui correspond <span class="font-semibold text-b3 px-1 py-0.5 rounded bg-b3/10">PARFAITEMENT</span> à vos besoins et objectifs.
        </p>
      </header>

      <!-- Cartes de fonctionnalités avec design moderne -->
      <div
        class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-8 lg:gap-10"
        :class="{ 'animate-fade-in-delayed': cardsVisible }"
      >
        <article
          v-for="(feature, index) in features"
          :key="index"
          class="relative group overflow-visible feature-card"
        >
          <!-- Carte avec design moderne -->
          <div
            class="relative bg-white rounded-2xl overflow-hidden shadow-lg transition-all duration-500 group-hover:shadow-xl h-full transform group-hover:-translate-y-2"
          >
            <!-- Élément décoratif de coin -->
            <div
              class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-b3/5 to-transparent rounded-bl-[100px] z-0"
            ></div>

            <!-- Barre de gradient en haut -->
            <div :class="`h-1.5 w-full bg-gradient-to-r ${feature.gradient}`"></div>

            <!-- Contenu de la carte -->
            <div class="p-3 sm:p-5 md:p-8 relative z-10">
              <!-- Badge de catégorie -->
              <div
                class="absolute top-2 right-2 md:top-3 md:right-3 px-1.5 py-0.5 md:px-2 md:py-1 bg-gray-100 rounded-md text-xs font-medium text-gray-600"
              >
                {{ feature.subtitle }}
              </div>

              <!-- Icône avec design moderne -->
              <div class="flex items-center my-2 md:my-6">
                <div class="relative mr-3 md:mr-4">
                  <!-- Cercle principal avec ombre portée -->
                  <div
                    :class="`rounded-xl p-2 md:p-3 bg-gradient-to-r ${feature.gradient} shadow-lg transform transition-transform duration-500 group-hover:scale-110`"
                  >
                    <component :is="feature.icon" class="w-5 h-5 md:w-6 md:h-6 text-white" />
                  </div>
                </div>

                <!-- Titre aligné avec l'icône -->
                <h3 class="text-lg sm:text-xl md:text-2xl font-bold text-gray-800">
                  {{ feature.title }}
                </h3>
              </div>

              <!-- Description avec style moderne -->
              <div class="mb-3 pb-3 md:mb-6 md:pb-6 border-b border-gray-100">
                <p class="text-gray-600 text-sm md:text-base leading-relaxed" v-html="feature.description"></p>
              </div>

              <!-- Statistiques avec design moderne -->
              <div class="flex space-x-2 md:space-x-4 mb-3 md:mb-4">
                <div
                  v-for="(stat, sIndex) in feature.stats"
                  :key="sIndex"
                  class="flex-1 bg-gray-50 rounded-lg p-2 md:p-3 text-center border border-gray-100 transition-all duration-300 hover:border-b3/30 hover:shadow-sm"
                >
                  <div :class="`text-lg sm:text-xl md:text-2xl font-bold ${stat.color}`">
                    {{ stat.value }}
                  </div>
                  <div class="text-gray-500 text-xs sm:text-sm">{{ stat.label }}</div>
                </div>
              </div>

              <!-- Badges techniques -->
              <div class="flex justify-end space-x-2">
                <div class="bg-b3/10 rounded-full p-1.5 shadow-sm">
                  <component :is="feature.techIcons[0]" class="w-3.5 h-3.5 text-b3" />
                </div>
                <div class="bg-b6/10 rounded-full p-1.5 shadow-sm">
                  <component :is="feature.techIcons[1]" class="w-3.5 h-3.5 text-b6" />
                </div>
              </div>
            </div>
          </div>

          <!-- Élément décoratif derrière la carte -->
          <div
            :class="`absolute -bottom-1 -right-1 md:-bottom-3 md:-right-3 w-2/3 h-8 md:h-16 bg-gradient-to-r ${feature.gradient} opacity-20 rounded-lg blur-xl -z-10 transition-all duration-500 group-hover:opacity-30`"
          ></div>
        </article>
      </div>
    </div>
  </section>
</template>

<style scoped>
/* Animations améliorées */
.animate-fade-in {
  animation: fadeIn 1s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}

.animate-fade-in-delayed {
  opacity: 0;
  animation: fadeIn 1s cubic-bezier(0.22, 1, 0.36, 1) 0.5s forwards;
}

.animate-fade-in-delayed-more {
  opacity: 0;
  animation: fadeIn 1s cubic-bezier(0.22, 1, 0.36, 1) 1.2s forwards;
}

/* Animation des cartes */
.feature-card {
  transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1);
}

.feature-card:hover {
  z-index: 10;
}

/* Effet de flottement subtil */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Effet de brillance */
@keyframes shine {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design amélioré */
@media (max-width: 768px) {
  .grid-cols-1 > div {
    margin-bottom: 0.5rem;
  }

  h2 {
    font-size: 2.25rem;
  }
}
</style>
