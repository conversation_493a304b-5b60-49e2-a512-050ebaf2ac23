<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import { useModalState } from "../../store/modalStore";
import { useRoute } from "vue-router";
import ContactModal from "./ContactModal.vue";

// Icons
import {
  HomeIcon,
  CogIcon,
  UserGroupIcon,
  EnvelopeIcon,
  XMarkIcon,
  Bars3Icon,
  ChevronDownIcon,
  SparklesIcon,
  ShoppingCartIcon,
  PuzzlePieceIcon,
  RocketLaunchIcon,
  ClockIcon,
  MegaphoneIcon
} from "@heroicons/vue/24/outline";

const isAtTop = ref(true);
const mobileMenuOpen = ref(false);
const route = useRoute();

// États des dropdowns
const createDropdownOpen = ref(false);
const supportDropdownOpen = ref(false);

// Gestion des dropdowns
const toggleCreateDropdown = () => {
  createDropdownOpen.value = !createDropdownOpen.value;
  supportDropdownOpen.value = false;
};

const toggleSupportDropdown = () => {
  supportDropdownOpen.value = !supportDropdownOpen.value;
  createDropdownOpen.value = false;
};

const closeAllDropdowns = () => {
  createDropdownOpen.value = false;
  supportDropdownOpen.value = false;
};

// Récupérer l'état des modales
const { isAnyModalOpen } = useModalState();

// Calculer si la navbar doit être visible
const shouldShowNavbar = computed(() => !isAnyModalOpen.value);

// Calculer si on est sur une page avec CarouselTop
const hasCarousel = computed(() => route.path === '/' || route.path === '/formules');

const handleScroll = () => {
  isAtTop.value = window.scrollY === 0;
};

// Fonction pour basculer l'état du menu mobile
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

// Fonction pour fermer le menu mobile
const closeMobileMenu = () => {
  mobileMenuOpen.value = false;
  closeAllDropdowns();
};

// État de la modale de contact
const isContactModalOpen = ref(false);

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true;
  // Fermer le menu mobile si ouvert
  if (mobileMenuOpen.value) {
    closeMobileMenu();
  }
};

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false;
};

// Fonction isActiveLink supprimée car plus utilisée avec le nouveau design

// Fonction getLinkClasses supprimée car plus utilisée avec le nouveau design

// Fonction pour scroll vers le bas de la page courante
const scrollToBottom = () => {
  // Fermer le menu mobile si ouvert
  closeMobileMenu();
  
  // Utilisation de requestAnimationFrame pour optimiser les performances
  requestAnimationFrame(() => {
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth'
    });
  });
};

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
  // Fermer les dropdowns quand on clique ailleurs
  document.addEventListener("click", (e) => {
    if (!e.target.closest('.dropdown-container')) {
      closeAllDropdowns();
    }
  });
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
  document.removeEventListener("click", closeAllDropdowns);
});
</script>
<template>
  <nav
    v-if="shouldShowNavbar"
    class="navbar fixed bg-black shadow-sm px-2  sm:px-4 p-2 xs:p-3 sm:p-4 z-80 transition-all duration-300"
    :class="{ 'mt-10': isAtTop && hasCarousel }"
    aria-label="Navigation principale"
    role="navigation"
  >
    <div class="navbar-start">
      <div class="dropdown">
        <button @click="toggleMobileMenu" class="btn btn-ghost p-0 lg:hidden cursor-pointer transition-all duration-300 hover:opacity-80" aria-label="Menu mobile" :aria-expanded="mobileMenuOpen ? 'true' : 'false'">
          <Bars3Icon v-if="!mobileMenuOpen" class="h-6 w-6" />
          <XMarkIcon v-else class="h-6 w-6" />
        </button>

        <div v-if="mobileMenuOpen" class="dropdown-content z-50 w-full mt-3 shadow-lg rounded-lg p-0 border border-white/10">
          <ul class="menu font-bold bg-black p-4 sm:p-6 rounded-lg max-h-[80vh] overflow-y-auto">
            <!-- Accueil -->
            <li class="mb-3">
              <a href="/" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-3 px-4 rounded-lg">
                <HomeIcon class="w-5 h-5 text-b3" />
                <span>Accueil</span>
              </a>
            </li>

            <!-- Créer un site - Section -->
            <li class="mb-2">
              <div class="text-b3 text-sm font-semibold px-4 py-2 flex items-center gap-2">
                <CogIcon class="w-4 h-4" />
                Créer un site
              </div>
              <div class="ml-4 space-y-1">
                <a href="/formules" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-2 px-4 rounded-lg text-sm">
                  <SparklesIcon class="w-4 h-4 text-b3" />
                  <div>
                    <div>Sur-mesure</div>
                    <div class="text-xs text-white/60">On part de zéro</div>
                  </div>
                </a>
                <a href="/boutique" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-2 px-4 rounded-lg text-sm">
                  <ShoppingCartIcon class="w-4 h-4 text-b6" />
                  <div>
                    <div class="text-b6">La Collection</div>
                    <div class="text-xs text-white/60">Sites uniques et personnalisables</div>
                  </div>
                </a>
                <a href="/options" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-2 px-4 rounded-lg text-sm">
                  <PuzzlePieceIcon class="w-4 h-4 text-b4" />
                  <div>
                    <div>Options & modules</div>
                    <div class="text-xs text-white/60">Fonctionnalités supplémentaires</div>
                  </div>
                </a>
              </div>
            </li>

            <!-- Être accompagné - Section -->
            <li class="mb-2">
              <div class="text-b3 text-sm font-semibold px-4 py-2 flex items-center gap-2">
                <UserGroupIcon class="w-4 h-4" />
                Être accompagné
              </div>
              <div class="ml-4 space-y-1">
                <a href="/pack-creation" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-2 px-4 rounded-lg text-sm">
                  <RocketLaunchIcon class="w-4 h-4 text-b3" />
                  <div>
                    <div>Pack création d'entreprise</div>
                    <div class="text-xs text-white/60">Lancement clé en main</div>
                  </div>
                </a>
                <a href="/accompagnement" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-2 px-4 rounded-lg text-sm">
                  <ClockIcon class="w-4 h-4 text-b6" />
                  <div>
                    <div class="text-b6">Accompagnement horaire</div>
                    <div class="text-xs text-white/60">Aide personnalisée</div>
                  </div>
                </a>
                <a href="/community-management" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-2 px-4 rounded-lg text-sm">
                  <MegaphoneIcon class="w-4 h-4 text-b4" />
                  <div>
                    <div>Community management</div>
                    <div class="text-xs text-white/60">Gestion des réseaux sociaux</div>
                  </div>
                </a>
              </div>
            </li>

            <!-- Contact -->
            <li>
              <button @click="scrollToBottom" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-3 px-4 rounded-lg w-full text-left">
                <EnvelopeIcon class="w-5 h-5 text-b3" />
                <span>Contact</span>
              </button>
            </li>
            
            <!-- Boutons CTA mobile -->
            <li class="mt-6">
              <a href="https://www.comeup.com/fr/@thedevimpact" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center gap-3 bg-gradient-to-r from-[#FEEF6C] to-b6 hover:from-[#FEEF6C]/90 hover:to-b6/90 hover:shadow-lg hover:scale-[1.02] text-gray-800 transition-all duration-300 py-4 px-4 rounded-lg font-medium w-full cursor-pointer mb-3 navbar-comeup" aria-label="Me retrouver sur ComeUp">
                <svg class="w-5 h-5 navbar-comeup" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                </svg>
                <span class="navbar-comeup">Me retrouver sur COMEUP</span>
              </a>
              <button @click="openContactModal" class="flex items-center justify-center gap-3 bg-b6 hover:bg-b6/90 hover:shadow-lg hover:scale-[1.02] text-white transition-all duration-300 py-4 px-4 rounded-lg font-medium w-full cursor-pointer navbar-devis" aria-label="Obtenir un devis gratuit">
                <EnvelopeIcon class="w-5 h-5 navbar-devis" />
                <span class="navbar-devis">Obtenir un devis</span>
              </button>
            </li>
        </ul>
        </div>
      </div>
      <!-- Logo et nom de l'entreprise -->
      <div class="flex justify-start items-center btn btn-ghost p-1 xs:p-2 sm:p-4" role="banner" aria-label="Logo et nom de l'entreprise">
        <img class="h-3.5 sm:h-4.5 w-auto" src="/src/assets/logo-TheDI-removeBG.png" alt="Logo TheDevImpact" width="40" height="40" />
        <a href="#" class="text-white text-base sm:text-xl whitespace-nowrap" aria-label="Accueil TheDevImpact">
          <span class="xs:hidden">TDI</span>
          <span class="hidden xs:inline">TheDevImpact</span>
        </a>
      </div>
    </div>
    <div class="navbar-center hidden lg:flex">
      <ul class="flex items-center space-x-6" role="menubar" aria-label="Navigation principale">
        <!-- Accueil -->
        <li class="relative nav-link">
          <a href="/" class="flex items-center gap-2 px-3 py-2 text-white/80 hover:text-white transition-colors duration-300 rounded-lg hover:bg-white/5 cursor-pointer">
            <HomeIcon class="w-4 h-4 text-b3" />
            <span>Accueil</span>
          </a>
        </li>

        <!-- Créer un site - Dropdown -->
        <li class="relative dropdown-container">
          <button
            @click="toggleCreateDropdown"
            class="flex items-center gap-2 px-3 py-2 text-white/80 hover:text-white transition-colors duration-300 rounded-lg hover:bg-white/5 cursor-pointer"
            :class="{ 'text-white bg-white/5': createDropdownOpen }"
          >
            <CogIcon class="w-4 h-4 text-b3" />
            <span>Créer un site</span>
            <ChevronDownIcon class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': createDropdownOpen }" />
          </button>

          <!-- Dropdown Menu -->
          <div v-if="createDropdownOpen" class="absolute top-full left-0 mt-2 w-80 lg:w-80 md:w-72 sm:w-64 bg-black rounded-xl shadow-2xl border border-white/20 py-2 z-50">
            <a href="/formules" class="flex items-start gap-3 px-4 py-3 hover:bg-white/10 transition-colors duration-200 group cursor-pointer">
              <div class="w-8 h-8 rounded-full bg-b3/20 flex items-center justify-center group-hover:bg-b3/30 transition-colors">
                <SparklesIcon class="w-4 h-4 text-b3" />
              </div>
              <div>
                <div class="font-medium text-white">Sur-mesure</div>
                <div class="text-sm text-white/70">On part de zéro</div>
              </div>
            </a>
            <a href="/boutique" class="flex items-start gap-3 px-4 py-3 hover:bg-white/10 transition-colors duration-200 group cursor-pointer">
              <div class="w-8 h-8 rounded-full bg-b6/20 flex items-center justify-center group-hover:bg-b6/30 transition-colors">
                <ShoppingCartIcon class="w-4 h-4 text-b6" />
              </div>
              <div>
                <div class="font-medium text-b6">La Collection</div>
                <div class="text-sm text-white/70">Sites uniques et personnalisables</div>
              </div>
            </a>
            <a href="/options" class="flex items-start gap-3 px-4 py-3 hover:bg-white/10 transition-colors duration-200 group cursor-pointer">
              <div class="w-8 h-8 rounded-full bg-b4/20 flex items-center justify-center group-hover:bg-b4/30 transition-colors">
                <PuzzlePieceIcon class="w-4 h-4 text-b4" />
              </div>
              <div>
                <div class="font-medium text-white">Options & modules</div>
                <div class="text-sm text-white/70">Fonctionnalités supplémentaires</div>
              </div>
            </a>
          </div>
        </li>

        <!-- Être accompagné - Dropdown -->
        <li class="relative dropdown-container">
          <button
            @click="toggleSupportDropdown"
            class="flex items-center gap-2 px-3 py-2 text-white/80 hover:text-white transition-colors duration-300 rounded-lg hover:bg-white/5 cursor-pointer"
            :class="{ 'text-white bg-white/5': supportDropdownOpen }"
          >
            <UserGroupIcon class="w-4 h-4 text-b3" />
            <span>Être accompagné</span>
            <ChevronDownIcon class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': supportDropdownOpen }" />
          </button>

          <!-- Dropdown Menu avec tooltips -->
          <div v-if="supportDropdownOpen" class="absolute top-full left-0 mt-2 w-96 lg:w-96 md:w-80 sm:w-72 bg-black rounded-xl shadow-2xl border border-white/20 py-2 z-50">
            <div class="relative group">
              <a href="/pack-creation" class="flex items-start gap-3 px-4 py-3 hover:bg-white/10 transition-colors duration-200 cursor-pointer">
                <div class="w-8 h-8 rounded-full bg-b3/20 flex items-center justify-center group-hover:bg-b3/30 transition-colors">
                  <RocketLaunchIcon class="w-4 h-4 text-b3" />
                </div>
                <div>
                  <div class="font-medium text-white">Pack création d'entreprise</div>
                  <div class="text-sm text-white/70">Lancement clé en main</div>
                </div>
              </a>
              <!-- Tooltip -->
              <div class="hidden lg:block absolute left-full top-0 ml-2 w-72 bg-white text-gray-900 text-xs rounded-lg p-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-60 shadow-xl">
                Lancement clé en main : logo, site one-page, réseaux sociaux, cartes de visite, adresse mail pro, page Google Business etc - offre modulable
                <div class="absolute top-3 left-0 transform -translate-x-1 w-2 h-2 bg-white rotate-45"></div>
              </div>
            </div>

            <div class="relative group">
              <a href="/accompagnement" class="flex items-start gap-3 px-4 py-3 hover:bg-white/10 transition-colors duration-200 cursor-pointer">
                <div class="w-8 h-8 rounded-full bg-b6/20 flex items-center justify-center group-hover:bg-b6/30 transition-colors">
                  <ClockIcon class="w-4 h-4 text-b6" />
                </div>
                <div>
                  <div class="font-medium text-b6">Accompagnement horaire</div>
                  <div class="text-sm text-white/70">Aide personnalisée</div>
                </div>
              </a>
              <!-- Tooltip -->
              <div class="hidden lg:block absolute left-full top-0 ml-2 w-72 bg-white text-gray-900 text-xs rounded-lg p-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-60 shadow-xl">
                Aide personnalisée à l'heure : je vous montre, je fais avec vous (site, réseaux, Canva, newsletter, etc.)
                <div class="absolute top-3 left-0 transform -translate-x-1 w-2 h-2 bg-white rotate-45"></div>
              </div>
            </div>

            <div class="relative group">
              <a href="/community-management" class="flex items-start gap-3 px-4 py-3 hover:bg-white/10 transition-colors duration-200 cursor-pointer">
                <div class="w-8 h-8 rounded-full bg-b4/20 flex items-center justify-center group-hover:bg-b4/30 transition-colors">
                  <MegaphoneIcon class="w-4 h-4 text-b4" />
                </div>
                <div>
                  <div class="font-medium text-white">Community management</div>
                  <div class="text-sm text-white/70">Gestion des réseaux sociaux</div>
                </div>
              </a>
              <!-- Tooltip -->
              <div class="hidden lg:block absolute left-full top-0 ml-2 w-72 bg-white text-gray-900 text-xs rounded-lg p-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-60 shadow-xl">
                Stratégie, optimisation ou gestion de vos réseaux sociaux (Instagram, Facebook)
                <div class="absolute top-3 left-0 transform -translate-x-1 w-2 h-2 bg-white rotate-45"></div>
              </div>
            </div>
          </div>
        </li>

        <!-- Contact -->
        <li class="relative nav-link">
          <button @click="scrollToBottom" class="flex items-center gap-2 px-3 py-2 text-white/80 hover:text-white transition-colors duration-300 rounded-lg hover:bg-white/5 cursor-pointer">
            <EnvelopeIcon class="w-4 h-4 text-b3" />
            <span>Contact</span>
          </button>
        </li>
      </ul>
    </div>
    <div class="navbar-end flex items-center gap-1 sm:gap-2 navbar-comeup">
      <a href="https://www.comeup.com/fr/@thedevimpact" target="_blank" rel="noopener noreferrer" class="flex items-center gap-1 bg-gradient-to-r from-[#FEEF6C] to-b6 hover:from-[#FEEF6C]/90 hover:to-b6/90 hover:shadow-lg text-gray-800 text-xs sm:text-sm font-medium transition-all duration-300 rounded-lg px-1.5 sm:px-4 py-2 transform hover:scale-105 cursor-pointer navbar-comeup" aria-label="Me retrouver sur ComeUp">
        <svg class="w-4 h-4 navbar-comeup" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
        </svg>
        <span class="whitespace-nowrap navbar-comeup">COMEUP</span>
      </a>
      <button @click="openContactModal" class="flex items-center gap-1 bg-b6 hover:bg-b6/90 hover:shadow-lg text-white text-xs sm:text-sm font-medium transition-all duration-300 rounded-lg px-1.5 sm:px-4 py-2 transform hover:scale-105 cursor-pointer" aria-label="Demander un devis gratuit navbar-devis">
        <EnvelopeIcon class="w-4 h-4 navbar-devis" />
        <span class="whitespace-nowrap navbar-devis">Devis gratuit</span>
      </button>
    </div>
  </nav>

  <!-- Modale de contact -->
  <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
</template>

<style scoped>
/* Animation pour les dropdowns */
.dropdown-container {
  position: relative;
}

/* Suppression des soulignements pour un design plus épuré */

/* Styles pour les tooltips */
.group:hover .opacity-0 {
  opacity: 1;
}

/* Styles pour le menu mobile */
.dropdown-content {
  background-color: black !important;
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
}

/* Animation des chevrons */
.rotate-180 {
  transform: rotate(180deg);
}

/* Z-index élevé pour les tooltips */
.z-60 {
  z-index: 60;
}

/* Amélioration de l'accessibilité */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

/* Responsive pour les dropdowns */
@media (max-width: 1024px) {
  .dropdown-container .absolute {
    position: fixed !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 90vw !important;
    max-width: 400px !important;
  }
}

/* Amélioration pour très petits écrans */
@media (max-width: 480px) {
  .dropdown-container .absolute {
    width: 95vw !important;
    left: 2.5vw !important;
    transform: none !important;
  }
}
</style>
