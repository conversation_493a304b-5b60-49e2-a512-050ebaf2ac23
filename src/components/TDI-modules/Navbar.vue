<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import { useModalState } from "../../store/modalStore";
import { useRoute } from "vue-router";
import ContactModal from "./ContactModal.vue";

// Icons
import {
  HomeIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  EnvelopeIcon,
  XMarkIcon,
  Bars3Icon
} from "@heroicons/vue/24/outline";

const isAtTop = ref(true);
const mobileMenuOpen = ref(false);
const route = useRoute();

// Récupérer l'état des modales
const { isAnyModalOpen } = useModalState();

// Calculer si la navbar doit être visible
const shouldShowNavbar = computed(() => !isAnyModalOpen.value);

// Calculer si on est sur une page avec CarouselTop
const hasCarousel = computed(() => route.path === '/' || route.path === '/formules');

const handleScroll = () => {
  isAtTop.value = window.scrollY === 0;
};

// Fonction pour basculer l'état du menu mobile
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

// Fonction pour fermer le menu mobile
const closeMobileMenu = () => {
  mobileMenuOpen.value = false;
};

// État de la modale de contact
const isContactModalOpen = ref(false);

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true;
  // Fermer le menu mobile si ouvert
  if (mobileMenuOpen.value) {
    closeMobileMenu();
  }
};

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false;
};

// Fonction pour déterminer si un lien est actif
const isActiveLink = (path) => {
  if (path === '/') {
    return route.path === '/';
  }
  return route.path.startsWith(path);
};

// Fonction pour obtenir les classes CSS d'un lien
const getLinkClasses = (path) => {
  const baseClasses = 'flex items-center gap-1 px-2 py-2 transition-colors duration-300 rounded-lg hover:bg-white/5';
  
  if (isActiveLink(path)) {
    if (path === '/boutique') {
      return `${baseClasses} text-b6 active-link`;
    } else {
      return `${baseClasses} text-white active-link`;
    }
  } else {
    if (path === '/boutique') {
      return `${baseClasses} text-white/80 hover:text-b6`;
    } else {
      return `${baseClasses} text-white/80 hover:text-white`;
    }
  }
};

// Fonction pour scroll vers le bas de la page courante
const scrollToBottom = () => {
  // Fermer le menu mobile si ouvert
  closeMobileMenu();
  
  // Utilisation de requestAnimationFrame pour optimiser les performances
  requestAnimationFrame(() => {
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth'
    });
  });
};

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>
<template>
  <nav
    v-if="shouldShowNavbar"
    class="navbar fixed bg-black shadow-sm px-2  sm:px-4 p-2 xs:p-3 sm:p-4 z-80 transition-all duration-300"
    :class="{ 'mt-10': isAtTop && hasCarousel }"
    aria-label="Navigation principale"
    role="navigation"
  >
    <div class="navbar-start">
      <div class="dropdown">
        <button @click="toggleMobileMenu" class="btn btn-ghost p-0 lg:hidden cursor-pointer transition-all duration-300 hover:opacity-80" aria-label="Menu mobile" :aria-expanded="mobileMenuOpen ? 'true' : 'false'">
          <Bars3Icon v-if="!mobileMenuOpen" class="h-6 w-6" />
          <XMarkIcon v-else class="h-6 w-6" />
        </button>

        <div v-if="mobileMenuOpen" class="dropdown-content z-50 w-full mt-3 shadow-lg rounded-lg p-0 border border-white/10">
          <ul class="menu menu-lg font-bold bg-black p-6 rounded-lg">
            <li class="mb-3 navbar-accueil">
              <a href="/" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-3 px-4 rounded-lg navbar-accueil">
                <HomeIcon class="w-5 h-5 text-b3 navbar-accueil" />
                <span class="navbar-accueil">Accueil</span>
              </a>
            </li>
            <li class="mb-3 navbar-formules">
              <a href="/formules" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-3 px-4 rounded-lg navbar-formules">
                <CurrencyDollarIcon class="w-5 h-5 text-b3 navbar-formules" />
                <span class="navbar-formules">Création sur-mesure</span>
              </a>
            </li>
            <li class="mb-3 navbar-boutique">
              <a href="/boutique" @click="closeMobileMenu" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-3 px-4 rounded-lg navbar-boutique">
                <ShoppingBagIcon class="w-5 h-5 text-b6 navbar-boutique" />
                <span class="navbar-boutique text-b6">La Collection</span>
              </a>
            </li>

            <li class="navbar-contact">
              <button @click="scrollToBottom" class="flex items-center gap-3 hover:bg-white/10 transition-colors duration-300 py-3 px-4 rounded-lg navbar-contact w-full text-left">
                <EnvelopeIcon class="w-5 h-5 text-b3 navbar-contact" />
                <span class="navbar-contact">Contact</span>
              </button>
            </li>
            
            <!-- Boutons CTA mobile -->
            <li class="mt-6">
              <a href="https://www.comeup.com/fr/@thedevimpact" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center gap-3 bg-gradient-to-r from-[#FEEF6C] to-b6 hover:from-[#FEEF6C]/90 hover:to-b6/90 hover:shadow-lg hover:scale-[1.02] text-gray-800 transition-all duration-300 py-4 px-4 rounded-lg font-medium w-full cursor-pointer mb-3 navbar-comeup" aria-label="Me retrouver sur ComeUp">
                <svg class="w-5 h-5 navbar-comeup" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                </svg>
                <span class="navbar-comeup">Me retrouver sur COMEUP</span>
              </a>
              <button @click="openContactModal" class="flex items-center justify-center gap-3 bg-b6 hover:bg-b6/90 hover:shadow-lg hover:scale-[1.02] text-white transition-all duration-300 py-4 px-4 rounded-lg font-medium w-full cursor-pointer navbar-devis" aria-label="Obtenir un devis gratuit">
                <EnvelopeIcon class="w-5 h-5 navbar-devis" />
                <span class="navbar-devis">Obtenir un devis</span>
              </button>
            </li>
        </ul>
        </div>
      </div>
      <!-- Logo et nom de l'entreprise -->
      <div class="flex justify-start items-center btn btn-ghost p-1 xs:p-2 sm:p-4" role="banner" aria-label="Logo et nom de l'entreprise">
        <img class="h-3.5 sm:h-4.5 w-auto" src="/src/assets/logo-TheDI-removeBG.png" alt="Logo TheDevImpact" width="40" height="40" />
        <a href="#" class="text-white text-base sm:text-xl whitespace-nowrap" aria-label="Accueil TheDevImpact">
          <span class="xs:hidden">TDI</span>
          <span class="hidden xs:inline">TheDevImpact</span>
        </a>
      </div>
    </div>
    <div class="navbar-center hidden lg:flex">
      <ul class="flex items-center space-x-1 sm:space-x-2" role="menubar" aria-label="Navigation principale">
        <li class="relative nav-link" :class="{ 'active-nav': isActiveLink('/') }">
          <a href="/" :class="getLinkClasses('/')" class="navbar-accueil">
            <HomeIcon class="w-4 h-4 text-b3 navbar-accueil" />
            <span class="navbar-accueil">Accueil</span>
          </a>
        </li>
        <li class="relative nav-link" :class="{ 'active-nav': isActiveLink('/formules') }">
          <a href="/formules" :class="getLinkClasses('/formules')" class="navbar-formules">
            <CurrencyDollarIcon class="w-4 h-4 text-b3 navbar-formules" />
            <span class="navbar-formules">Création sur-mesure</span>
          </a>
        </li>
        <li class="relative nav-link nav-link-shop" :class="{ 'active-nav': isActiveLink('/boutique') }">
          <a href="/boutique" :class="getLinkClasses('/boutique')" class="navbar-boutique">
            <ShoppingBagIcon class="w-4 h-4 text-b6 navbar-boutique" />
            <span class="navbar-boutique text-b6">La Collection</span>
          </a>
        </li>

        <li class="relative nav-link" :class="{ 'active-nav': route.path.includes('contact') }">
          <button @click="scrollToBottom" :class="getLinkClasses('/#contact')" class="navbar-contact">
            <EnvelopeIcon class="w-4 h-4 text-b3 navbar-contact" />
            <span class="navbar-contact">Contact</span>
          </button>
        </li>
      </ul>
    </div>
    <div class="navbar-end flex items-center gap-1 sm:gap-2 navbar-comeup">
      <a href="https://www.comeup.com/fr/@thedevimpact" target="_blank" rel="noopener noreferrer" class="flex items-center gap-1 bg-gradient-to-r from-[#FEEF6C] to-b6 hover:from-[#FEEF6C]/90 hover:to-b6/90 hover:shadow-lg text-gray-800 text-xs sm:text-sm font-medium transition-all duration-300 rounded-lg px-1.5 sm:px-4 py-2 transform hover:scale-105 cursor-pointer navbar-comeup" aria-label="Me retrouver sur ComeUp">
        <svg class="w-4 h-4 navbar-comeup" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
        </svg>
        <span class="whitespace-nowrap navbar-comeup">COMEUP</span>
      </a>
      <button @click="openContactModal" class="flex items-center gap-1 bg-b6 hover:bg-b6/90 hover:shadow-lg text-white text-xs sm:text-sm font-medium transition-all duration-300 rounded-lg px-1.5 sm:px-4 py-2 transform hover:scale-105 cursor-pointer" aria-label="Demander un devis gratuit navbar-devis">
        <EnvelopeIcon class="w-4 h-4 navbar-devis" />
        <span class="whitespace-nowrap navbar-devis">Devis gratuit</span>
      </button>
    </div>
  </nav>

  <!-- Modale de contact -->
  <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
</template>

<style scoped>
.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, var(--color-b3), var(--color-b6));
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

/* Style spécial pour LE SHOP */
.nav-link-shop::after {
  background: var(--color-b6);
}

.nav-link:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Soulignement pour les liens actifs */
.nav-link.active-nav::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Styles pour le menu mobile */
.dropdown-content {
  background-color: black !important;
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
}
</style>
