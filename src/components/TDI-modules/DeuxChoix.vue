<script setup>
import { ref, onMounted } from 'vue';
import { CodeBracketIcon, ShoppingBagIcon } from '@heroicons/vue/24/outline';

// Animation
const sectionVisible = ref(false);

onMounted(() => {
  setTimeout(() => {
    sectionVisible.value = true;
  }, 200);
});
</script>

<template>
  <section id="deux-choix" class="relative py-20 lg:py-32 overflow-hidden">
    <!-- Background sophistiqué multicouche -->
    <div class="absolute inset-0 bg-gradient-to-br from-b1 via-b2/40 to-b1"></div>
    
    <!-- Mesh gradient moderne -->
    <div class="absolute inset-0 opacity-30">
      <div class="absolute inset-0" 
           style="background: 
             radial-gradient(circle at 20% 20%, rgba(133, 189, 191, 0.15) 0%, transparent 40%),
             radial-gradient(circle at 80% 80%, rgba(183, 228, 232, 0.12) 0%, transparent 40%),
             radial-gradient(circle at 40% 70%, rgba(239, 148, 108, 0.08) 0%, transparent 30%),
             radial-gradient(circle at 70% 30%, rgba(201, 251, 255, 0.1) 0%, transparent 35%);"></div>
    </div>
    
    <!-- Grille subtile -->
    <div class="absolute inset-0 opacity-5">
      <div class="w-full h-full" 
           style="background-image: 
             linear-gradient(rgba(133, 189, 191, 0.1) 1px, transparent 1px),
             linear-gradient(90deg, rgba(133, 189, 191, 0.1) 1px, transparent 1px);
             background-size: 60px 60px;"></div>
    </div>
    
    <!-- Particules flottantes améliorées -->
    <div class="absolute inset-0">
      <!-- Grandes particules -->
      <div class="absolute top-1/4 left-1/4 w-3 h-3 bg-gradient-to-br from-b3/30 to-b4/20 rounded-full animate-float blur-sm"></div>
      <div class="absolute top-3/4 right-1/4 w-4 h-4 bg-gradient-to-br from-b4/25 to-b5/15 rounded-full animate-float blur-sm" style="animation-delay: 1s; animation-duration: 4s;"></div>
      <div class="absolute top-1/2 left-3/4 w-2 h-2 bg-gradient-to-br from-b5/35 to-b3/20 rounded-full animate-float blur-sm" style="animation-delay: 2s; animation-duration: 5s;"></div>
      <div class="absolute top-1/3 right-1/3 w-3 h-3 bg-gradient-to-br from-b6/20 to-b4/15 rounded-full animate-float blur-sm" style="animation-delay: 0.5s; animation-duration: 3.5s;"></div>
      
      <!-- Petites particules -->
      <div class="absolute top-1/6 left-1/2 w-1 h-1 bg-b3/40 rounded-full animate-float" style="animation-delay: 3s; animation-duration: 6s;"></div>
      <div class="absolute top-5/6 left-1/6 w-1 h-1 bg-b4/35 rounded-full animate-float" style="animation-delay: 1.5s; animation-duration: 4.5s;"></div>
      <div class="absolute top-2/3 right-1/6 w-1 h-1 bg-b5/45 rounded-full animate-float" style="animation-delay: 2.5s; animation-duration: 5.5s;"></div>
    </div>
    
    <!-- Lignes géométriques subtiles -->
    <div class="absolute inset-0 opacity-10">
      <svg class="w-full h-full" viewBox="0 0 1000 600" fill="none">
        <path d="M0,300 Q250,100 500,300 T1000,300" stroke="url(#gradient1)" stroke-width="1" fill="none" opacity="0.6"/>
        <path d="M0,200 Q300,400 600,200 T1000,200" stroke="url(#gradient2)" stroke-width="1" fill="none" opacity="0.4"/>
        <defs>
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:rgba(133, 189, 191, 0.8);stop-opacity:0" />
            <stop offset="50%" style="stop-color:rgba(133, 189, 191, 0.8);stop-opacity:1" />
            <stop offset="100%" style="stop-color:rgba(133, 189, 191, 0.8);stop-opacity:0" />
          </linearGradient>
          <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:rgba(183, 228, 232, 0.6);stop-opacity:0" />
            <stop offset="50%" style="stop-color:rgba(183, 228, 232, 0.6);stop-opacity:1" />
            <stop offset="100%" style="stop-color:rgba(183, 228, 232, 0.6);stop-opacity:0" />
          </linearGradient>
        </defs>
      </svg>
    </div>
    
    <!-- Overlay final avec vignette -->
    <div class="absolute inset-0 bg-gradient-to-t from-b1/30 via-transparent to-b1/30"></div>
    <div class="absolute inset-0 bg-gradient-to-r from-b1/20 via-transparent to-b1/20"></div>
    
    <!-- Contenu -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- En-tête de section -->
      <div class="text-center mb-16 opacity-0" :class="{ 'animate-fade-in': sectionVisible }">
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6">
          <span class="text-b3 text-sm font-medium">L'art de vous mettre en ligne</span>
        </div>
        <h2 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
          Deux chemins vers votre 
          <span class="bg-gradient-to-r from-b3 via-b4 to-b6 text-transparent bg-clip-text">
            site d'exception
          </span>
        </h2>
        <p class="text-xl text-white/80 max-w-3xl mx-auto mb-8">
          Choisissez votre chemin vers l'excellence digitale !
        </p>
      </div>

      <!-- Les deux choix avec séparateur OU -->
      <div class="grid grid-cols-1 lg:grid-cols-[1fr_auto_1fr] gap-8 lg:gap-3 items-center">
        
        <!-- Choix 1: Sur-mesure -->
        <div class="group opacity-0 animate-fade-in-delayed" :class="{ 'animate-fade-in-delayed': sectionVisible }">
          <div class="relative bg-gradient-to-br from-b3/10 to-b4/5 backdrop-blur-sm rounded-3xl p-8 lg:p-10 border-2 border-b3/30 shadow-2xl transition-all duration-500 hover:scale-101 hover:shadow-b3/40 hover:border-b3/50 h-full">
            
            <!-- Image locale - développement sur-mesure -->
            <div class="relative mb-6 rounded-2xl overflow-hidden">
              <img 
                src="/src/assets/website-1.webp" 
                alt="Développement web sur-mesure - Code et création" 
                class="w-full h-48 object-cover rounded-2xl"
                loading="lazy"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-b3/60 via-b3/20 to-transparent rounded-2xl"></div>
              <div class="absolute top-4 left-4">
                <span class="px-3 py-1 bg-b3 text-white text-xs font-bold rounded-full">SUR-MESURE</span>
              </div>
              <div class="absolute bottom-4 right-4">
                <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                  <CodeBracketIcon class="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
            
            <!-- Badge -->
            <div class="inline-flex items-center px-4 py-2 rounded-full bg-b3/20 text-b3 text-sm font-medium mb-6">
              <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
              Création personnalisée
            </div>

            <!-- Icône et titre -->
            <div class="flex items-center mb-6">
              <div class="w-16 h-16 bg-gradient-to-br from-b3 to-b3/80 rounded-2xl flex items-center justify-center mr-4">
                <CodeBracketIcon class="h-8 w-8 text-white" />
              </div>
              <div>
                <h3 class="text-2xl lg:text-3xl font-bold text-white mb-2">Création sur-mesure</h3>
                <p class="text-b3 font-medium">Une œuvre unique, née de votre vision</p>
              </div>
            </div>

            <!-- Description -->
            <p class="text-white/80 text-lg mb-8 leading-relaxed">
              Vision claire, ambitions fortes ? Nous créons votre site vitrine sur‑mesure, sans thème préfabriqué. Tout part d'une page blanche : design, contenus et parcours, chaque détail est calibré pour convertir. Pas de template, pas de limite : votre marque prend le devant de la scène.
            </p>

            <!-- Points clés -->
            <div class="space-y-4 mb-8">
              <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-b3 flex items-center justify-center mr-3">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-white/90">Design original, créé de zéro</span>
              </div>
              <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-b3 flex items-center justify-center mr-3">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-white/90">Accompagnement créatif complet</span>
              </div>
              <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-b3 flex items-center justify-center mr-3">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-white/90">Parfait pour les projets ambitieux ou haut de gamme</span>
              </div>
            </div>

            <!-- CTA amélioré -->
            <div class="space-y-3">
              <router-link 
                to="/formules" 
                class="inline-flex items-center justify-center w-full px-8 py-5 bg-gradient-to-r from-b3 to-b4 text-white font-bold text-lg rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:from-b3/90 hover:to-b4/90 group-hover:shadow-b3/50"
              >
                <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
                CRÉER MON SITE SUR-MESURE
                <svg class="ml-3 h-6 w-6 transition-transform duration-300 group-hover:translate-x-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </router-link>
              <!-- <p class="text-center text-white/60 text-sm italic">
                Accompagnement personnalisé inclus
              </p> -->
            </div>
          </div>
        </div>

        <!-- Séparateur "OU" central amélioré -->
        <div class="flex justify-center items-center lg:my-0 my-8 lg:mx-3">
          <div class="flex flex-col items-center gap-4">
            <!-- Badge OU avec animation -->
            <div class="relative">
              <div class="px-6 py-4 rounded-full bg-gradient-to-r from-white/15 to-white/10 backdrop-blur-sm border-2 border-white/20 shadow-xl hover:scale-110 transition-transform duration-300">
                <span class="text-white font-bold text-lg bg-gradient-to-r from-b3 via-white to-b6  bg-clip-text">
                  OU
                </span>
              </div>
              <!-- Particules autour du OU -->
              <div class="absolute -top-1 -right-1 w-2 h-2 bg-b3/60 rounded-full animate-pulse"></div>
              <div class="absolute -bottom-1 -left-1 w-2 h-2 bg-b6/60 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
            </div>
            
            <!-- Ligne de connexion mobile -->
            <div class="lg:hidden w-px h-8 bg-gradient-to-b from-b3/30 via-white/40 to-b6/30"></div>
          </div>
        </div>

        <!-- Choix 2: Collection exclusive -->
        <div class="group opacity-0 animate-fade-in-delayed-more" :class="{ 'animate-fade-in-delayed-more': sectionVisible }">
          <div class="relative bg-gradient-to-br from-b6/10 to-b5/5 backdrop-blur-sm rounded-3xl p-8 lg:p-10 border-2 border-b6/30 shadow-2xl transition-all duration-500 hover:scale-101 hover:shadow-b6/40 hover:border-b6/50 h-full">
            
            <!-- Image locale - boutique/collection -->
            <div class="relative mb-6 rounded-2xl overflow-hidden">
              <img 
                src="/src/assets/website-2.webp" 
                alt="Collection exclusive de sites web - Boutique design prêt" 
                class="w-full h-48 object-cover rounded-2xl"
                loading="lazy"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-b6/60 via-b6/20 to-transparent rounded-2xl"></div>
              <div class="absolute top-4 left-4">
                <span class="px-3 py-1 bg-b6 text-white text-xs font-bold rounded-full">EXCLUSIF</span>
              </div>
              <div class="absolute bottom-4 right-4">
                <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                  <ShoppingBagIcon class="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
            
            <!-- Badge -->
            <div class="inline-flex items-center px-4 py-2 rounded-full bg-b6/20 text-b6 text-sm font-medium mb-6">
              <span class="w-2 h-2 rounded-full bg-b6 mr-2"></span>
              Collection exclusive
            </div>

            <!-- Icône et titre -->
            <div class="flex items-center mb-6">
              <div class="w-16 h-16 bg-gradient-to-br from-b6 to-b6/80 rounded-2xl flex items-center justify-center mr-4">
                <ShoppingBagIcon class="h-8 w-8 text-white" />
              </div>
              <div>
                <h3 class="text-2xl lg:text-3xl font-bold text-white mb-2">La Collection</h3>
                <p class="text-b6 font-medium">Des modèles uniques, pensés pour vous</p>
              </div>
            </div>

            <!-- Description -->
            <p class="text-white/80 text-lg mb-8 leading-relaxed">
              Nous concevons des sites vitrines professionnels, prêts à être adaptés à votre contenu (logo, textes, images…).
              Chaque design est une création originale, vendu une seule fois pour garantir votre exclusivité.
              Pas de modèle réutilisé, pas de doublon : vous êtes unique, votre site aussi.
            </p>

            <!-- Points clés -->
            <div class="space-y-4 mb-8">
              <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-b6 flex items-center justify-center mr-3">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-white/90">Design déjà prêt, livraison rapide</span>
              </div>
              <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-b6 flex items-center justify-center mr-3">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-white/90">Personnalisation soignée avec vos textes et visuels</span>
              </div>
              <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-b6 flex items-center justify-center mr-3">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-white/90">Idéal pour un budget maîtrisé sans sacrifier le style</span>
              </div>
            </div>

            <!-- CTA amélioré -->
            <div class="space-y-3">
              <router-link 
                to="/boutique" 
                class="inline-flex items-center justify-center w-full px-8 py-5 bg-b6 font-bold text-lg rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:from-b6/90 hover:to-b5/90 group-hover:shadow-b6/50"
              >
                <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                DÉCOUVRIR LA COLLECTION
                <svg class="ml-3 h-6 w-6 transition-transform duration-300 group-hover:translate-x-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </router-link>
              <!-- <p class="text-center text-white/60 text-sm italic">
                Accompagnement personnalisé inclus
              </p> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.8s ease-out 0.3s forwards;
}

.animate-fade-in-delayed-more {
  animation: fadeIn 0.8s ease-out 0.6s forwards;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
</style> 