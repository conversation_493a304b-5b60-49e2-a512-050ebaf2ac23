<template>
  <div v-if="toasts.length > 0" class="fixed top-4 right-4 z-[100] space-y-2 min-w-[400px]">
    <div
      v-for="toast in toasts"
      :key="toast.id"
      class="w-full min-w-[400px] bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300"
      :class="[
        toast.show ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0',
        getToastClasses(toast.type)
      ]"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <i :class="getIconClass(toast.type)" class="text-lg"></i>
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900">
              {{ toast.title }}
            </p>
            <p class="mt-1 text-sm text-gray-500">
              {{ toast.message }}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="removeToast(toast.id)"
              class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <i class="fas fa-times text-sm"></i>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Barre de progression -->
      <div class="h-1 bg-gray-200">
        <div 
          class="h-full transition-all duration-100 ease-linear"
          :class="getProgressBarClass(toast.type)"
          :style="{ width: `${toast.progress}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const toasts = ref([])
let toastId = 0
const activeTimers = new Map() // Pour gérer les timers actifs

function showToast(event) {
  const { type, title, message, duration } = event.detail
  // Durée par défaut basée sur le type de message
  const defaultDuration = type === 'info' ? 8000 : 5000
  const toastDuration = duration || defaultDuration
  
  // Prolonger la durée des toasts existants (désactivé temporairement)
  // extendExistingToasts(toastDuration)
  
  const toast = {
    id: ++toastId,
    type,
    title,
    message,
    show: false,
    progress: 100
  }
  
  toasts.value.push(toast)
  
  // Animation d'entrée immédiate
  toast.show = true
  
  // Animation de la barre de progression
  const progressInterval = setInterval(() => {
    toast.progress -= (100 / toastDuration) * 100
    if (toast.progress <= 0) {
      clearInterval(progressInterval)
      activeTimers.delete(toast.id)
      removeToast(toast.id)
    }
  }, 100)
  
  // Auto-suppression
  const removeTimer = setTimeout(() => {
    clearInterval(progressInterval)
    activeTimers.delete(toast.id)
    removeToast(toast.id)
  }, toastDuration)
  
  // Stocker les timers pour pouvoir les prolonger
  activeTimers.set(toast.id, {
    progressInterval,
    removeTimer,
    toast,
    duration: toastDuration,
    startTime: Date.now()
  })
}

function extendExistingToasts(newDuration) {
  // Optimisation : ne prolonger que si il y a moins de 5 toasts actifs
  if (activeTimers.size > 5) return
  
  activeTimers.forEach((timerData, toastId) => {
    // Calculer le temps écoulé
    const elapsed = Date.now() - timerData.startTime
    const remainingTime = Math.max(timerData.duration - elapsed, 0)
    
    // Si le toast existant a moins de 3 secondes restantes, l'étendre
    if (remainingTime < 3000) {
      const extensionTime = newDuration / 2 // Extension plus courte pour éviter l'accumulation
      
      // Nettoyer l'ancien timer
      clearTimeout(timerData.removeTimer)
      
      // Créer un nouveau timer prolongé
      const newRemoveTimer = setTimeout(() => {
        clearInterval(timerData.progressInterval)
        activeTimers.delete(toastId)
        removeToast(toastId)
      }, remainingTime + extensionTime)
      
      // Mettre à jour les données du timer
      timerData.removeTimer = newRemoveTimer
      timerData.duration = timerData.duration + extensionTime
    }
  })
}

function removeToast(id) {
  // Nettoyer les timers actifs
  if (activeTimers.has(id)) {
    const timerData = activeTimers.get(id)
    clearInterval(timerData.progressInterval)
    clearTimeout(timerData.removeTimer)
    activeTimers.delete(id)
  }
  
  const index = toasts.value.findIndex(t => t.id === id)
  if (index > -1) {
    toasts.value[index].show = false
    setTimeout(() => {
      toasts.value.splice(index, 1)
    }, 300)
  }
}

function getToastClasses(type) {
  switch (type) {
    case 'success':
      return 'border-l-4 border-green-400'
    case 'error':
      return 'border-l-4 border-red-400'
    case 'warning':
      return 'border-l-4 border-yellow-400'
    case 'info':
      return 'border-l-4 border-blue-400'
    default:
      return 'border-l-4 border-gray-400'
  }
}

function getIconClass(type) {
  switch (type) {
    case 'success':
      return 'fas fa-check-circle text-green-400'
    case 'error':
      return 'fas fa-exclamation-circle text-red-400'
    case 'warning':
      return 'fas fa-exclamation-triangle text-yellow-400'
    case 'info':
      return 'fas fa-info-circle text-blue-400'
    default:
      return 'fas fa-bell text-gray-400'
  }
}

function getProgressBarClass(type) {
  switch (type) {
    case 'success':
      return 'bg-green-400'
    case 'error':
      return 'bg-red-400'
    case 'warning':
      return 'bg-yellow-400'
    case 'info':
      return 'bg-blue-400'
    default:
      return 'bg-gray-400'
  }
}

onMounted(() => {
  window.addEventListener('show-toast', showToast)
})

onUnmounted(() => {
  window.removeEventListener('show-toast', showToast)
})
</script>

<style scoped>
/* Responsive */
@media (max-width: 640px) {
  .min-w-\[400px\] {
    min-width: calc(100vw - 2rem);
    max-width: calc(100vw - 2rem);
  }
}
</style> 