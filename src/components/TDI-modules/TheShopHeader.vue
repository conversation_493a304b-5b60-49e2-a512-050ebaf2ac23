<template>
  <header class="fixed top-0 left-0 right-0 z-50 transition-all duration-300" :class="headerClasses">
    <nav class="container-custom">
      <div class="flex items-center justify-between py-4">
        <!-- Logo -->
        <router-link to="/" class="flex items-center space-x-3 text-2xl font-bold text-gradient">
          <img class="h-5 w-auto mr-1" src="../../assets/logo-shop.webp" alt="Logo TheDevImpact" width="20" height="20" />
          <span>TheDevImpact</span>
        </router-link>

        <!-- Navigation Desktop -->
        <div class="hidden md:flex items-center space-x-8">
          <router-link 
            v-for="link in navigationLinks" 
            :key="link.path"
            :to="link.path" 
            class="nav-link text-gray-800 hover:text-gray-600"
            :class="{ 
              'text-b3': $route.path === link.path && link.path !== '/boutique',
              'text-b6 hover:text-b6': link.path === '/boutique'
            }"
          >
            {{ link.name }}
          </router-link>
          
          <!-- Bouton Contact -->
          <button @click="scrollToBottom" class="nav-link">
            Contact
          </button>
          
          <!-- Cart Icon -->
          <div class="relative">
            <button @click="toggleCart" class="nav-link">
              <i class="fas fa-shopping-cart text-xl"></i>
              <span v-if="cartItemsCount > 0" 
                    class="absolute -top-2 -right-2 bg-b6 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {{ cartItemsCount }}
              </span>
            </button>
          </div>
        </div>

        <!-- Menu Mobile Button -->
        <button @click="toggleMobileMenu" class="md:hidden p-2">
          <div class="w-6 h-6 relative">
            <span class="mobile-menu-line" :class="{ 'mobile-menu-open': isMobileMenuOpen }"></span>
            <span class="mobile-menu-line" :class="{ 'mobile-menu-open': isMobileMenuOpen }"></span>
            <span class="mobile-menu-line" :class="{ 'mobile-menu-open': isMobileMenuOpen }"></span>
          </div>
        </button>
      </div>
    </nav>

    <!-- Menu Mobile -->
    <div class="md:hidden mobile-menu" :class="{ 'mobile-menu-open': isMobileMenuOpen }">
      <div class="bg-white/95 backdrop-blur-sm border-t border-b3/20 py-6">
        <div class="container-custom">
          <div class="flex flex-col space-y-4">
            <router-link 
              v-for="link in navigationLinks" 
              :key="link.path"
              :to="link.path" 
              @click="closeMobileMenu"
              class="nav-link text-lg text-gray-800 hover:text-gray-600"
              :class="{ 
                'text-b3': $route.path === link.path && link.path !== '/boutique',
                'text-b6 hover:text-b6': link.path === '/boutique'
              }"
            >
              {{ link.name }}
            </router-link>
            
            <!-- Bouton Contact mobile -->
            <button @click="scrollToBottom" class="nav-link text-lg">
              Contact
            </button>
            
            <div class="border-t border-b3/20 pt-4">
              <button @click="toggleCart" class="nav-link flex items-center space-x-2">
                <i class="fas fa-shopping-cart"></i>
                <span>Panier ({{ cartItemsCount }})</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cart Sidebar -->
    <CartSidebar :isOpen="cartStore.isOpen" @close="closeCart" />
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useCartStore } from '../../store/cart.js'
import CartSidebar from './CartSidebar.vue'

const route = useRoute()
const cartStore = useCartStore()

// État réactif
const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)

// Navigation links
const navigationLinks = ref([
  { name: 'Accueil', path: '/' },
  { name: 'Création sur-mesure', path: '/formules' },
  { name: 'La Collection', path: '/boutique' }
])

// Computed
const headerClasses = computed(() => ({
  'bg-white/90 backdrop-blur-md shadow-lg': isScrolled.value,
  'bg-transparent': !isScrolled.value
}))

const cartItemsCount = computed(() => cartStore.itemsCount)

// Méthodes
function toggleMobileMenu() {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

function closeMobileMenu() {
  isMobileMenuOpen.value = false
}

function toggleCart() {
  cartStore.toggleCart()
  closeMobileMenu()
}

function closeCart() {
  cartStore.closeCart()
}

function handleScroll() {
  isScrolled.value = window.scrollY > 50
}

// Fonction pour scroll vers le bas de la page courante
const scrollToBottom = () => {
  closeMobileMenu();
  
  requestAnimationFrame(() => {
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth'
    });
  });
};

// Lifecycle
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.nav-link {
  color: #374151; /* text-gray-700 par défaut */
  position: relative;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #4b5563; /* text-gray-600 */
}

/* Style spécifique pour LE SHOP - toujours orange */
.nav-link.text-b6 {
  color: var(--color-b6) !important;
}

.nav-link.text-b6:hover {
  color: var(--color-b6) !important;
}

/* Style pour les liens actifs */
.nav-link.text-b3 {
  color: var(--color-b3) !important;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 50%;
  background: var(--color-b3);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

/* Style spécial pour LE SHOP */
.nav-link.text-b6::after {
  background: var(--color-b6);
}

.nav-link:hover::after {
  width: 100%;
}

.nav-link.router-link-active::after {
  width: 100%;
}

/* Menu mobile */
.mobile-menu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.mobile-menu.mobile-menu-open {
  max-height: 400px;
}

.mobile-menu-line {
  display: block;
  width: 100%;
  height: 2px;
  background: #374151;
  position: absolute;
  transition: all 0.3s ease;
}

.mobile-menu-line:nth-child(1) {
  top: 0;
}

.mobile-menu-line:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.mobile-menu-line:nth-child(3) {
  bottom: 0;
}

.mobile-menu-line.mobile-menu-open:nth-child(1) {
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

.mobile-menu-line.mobile-menu-open:nth-child(2) {
  opacity: 0;
}

.mobile-menu-line.mobile-menu-open:nth-child(3) {
  bottom: 50%;
  transform: translateY(50%) rotate(-45deg);
}
</style> 