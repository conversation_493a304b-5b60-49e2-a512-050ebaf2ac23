<template>
  <header class="fixed top-0 left-0 right-0 z-50 transition-all duration-300" :class="headerClasses">
    <nav class="container-custom">
      <div class="flex items-center justify-between py-4">
        <!-- Logo -->
        <router-link to="/" class="flex items-center space-x-3 text-2xl font-bold text-gradient">
          <img class="h-5 w-auto mr-1" src="../../assets/logo-shop.webp" alt="Logo TheDevImpact" width="20" height="20" />
          <span>TheDevImpact</span>
        </router-link>

        <!-- Navigation Desktop -->
        <div class="hidden md:flex items-center space-x-6">
          <!-- Accueil -->
          <router-link to="/" class="flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-300 rounded-lg hover:bg-gray-50 cursor-pointer">
            <HomeIcon class="w-4 h-4 text-b3" />
            <span>Accueil</span>
          </router-link>

          <!-- Créer un site - Dropdown -->
          <div class="relative dropdown-container">
            <button
              @click="toggleCreateDropdown"
              class="flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-300 rounded-lg hover:bg-gray-50 cursor-pointer"
              :class="{ 'text-gray-900 bg-gray-50': createDropdownOpen }"
            >
              <CogIcon class="w-4 h-4 text-b3" />
              <span>Créer un site</span>
              <ChevronDownIcon class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': createDropdownOpen }" />
            </button>

            <!-- Dropdown Menu -->
            <div v-if="createDropdownOpen" class="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50">
              <a href="/sur-mesure" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 group cursor-pointer">
                <div class="w-8 h-8 rounded-full bg-b3/10 flex items-center justify-center group-hover:bg-b3/20 transition-colors">
                  <SparklesIcon class="w-4 h-4 text-b3" />
                </div>
                <div>
                  <div class="font-medium text-gray-900">Sur-mesure</div>
                  <div class="text-sm text-gray-500">On part de zéro</div>
                </div>
              </a>
              <a href="/boutique" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 group cursor-pointer">
                <div class="w-8 h-8 rounded-full bg-b6/10 flex items-center justify-center group-hover:bg-b6/20 transition-colors">
                  <ShoppingCartIcon class="w-4 h-4 text-b6" />
                </div>
                <div>
                  <div class="font-medium text-b6">La Collection</div>
                  <div class="text-sm text-gray-500">Sites uniques et personnalisables</div>
                </div>
              </a>
              <a href="/options" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 group cursor-pointer">
                <div class="w-8 h-8 rounded-full bg-b4/10 flex items-center justify-center group-hover:bg-b4/20 transition-colors">
                  <PuzzlePieceIcon class="w-4 h-4 text-b4" />
                </div>
                <div>
                  <div class="font-medium text-gray-900">Options & modules</div>
                  <div class="text-sm text-gray-500">Fonctionnalités supplémentaires</div>
                </div>
              </a>
            </div>
          </div>

          <!-- Être accompagné - Dropdown -->
          <div class="relative dropdown-container">
            <button
              @click="toggleSupportDropdown"
              class="flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-300 rounded-lg hover:bg-gray-50 cursor-pointer"
              :class="{ 'text-gray-900 bg-gray-50': supportDropdownOpen }"
            >
              <UserGroupIcon class="w-4 h-4 text-b3" />
              <span>Être accompagné</span>
              <ChevronDownIcon class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': supportDropdownOpen }" />
            </button>

            <!-- Dropdown Menu -->
            <div v-if="supportDropdownOpen" class="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50">
              <a href="/pack-creation" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
                <div class="w-8 h-8 rounded-full bg-b3/10 flex items-center justify-center group-hover:bg-b3/20 transition-colors">
                  <RocketLaunchIcon class="w-4 h-4 text-b3" />
                </div>
                <div>
                  <div class="font-medium text-gray-900">Pack création d'entreprise</div>
                  <div class="text-sm text-gray-500">Lancement clé en main</div>
                </div>
              </a>
              <a href="/accompagnement" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
                <div class="w-8 h-8 rounded-full bg-b6/10 flex items-center justify-center group-hover:bg-b6/20 transition-colors">
                  <ClockIcon class="w-4 h-4 text-b6" />
                </div>
                <div>
                  <div class="font-medium text-b6">Accompagnement horaire</div>
                  <div class="text-sm text-gray-500">Aide personnalisée</div>
                </div>
              </a>
              <a href="/community-management" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
                <div class="w-8 h-8 rounded-full bg-b4/10 flex items-center justify-center group-hover:bg-b4/20 transition-colors">
                  <MegaphoneIcon class="w-4 h-4 text-b4" />
                </div>
                <div>
                  <div class="font-medium text-gray-900">Community management</div>
                  <div class="text-sm text-gray-500">Gestion des réseaux sociaux</div>
                </div>
              </a>
            </div>
          </div>

          <!-- Contact -->
          <button @click="scrollToBottom" class="flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-300 rounded-lg hover:bg-gray-50 cursor-pointer">
            <EnvelopeIcon class="w-4 h-4 text-b3" />
            <span>Contact</span>
          </button>

          <!-- Cart Icon -->
          <div class="relative">
            <button @click="toggleCart" class="nav-link cursor-pointer">
              <i class="fas fa-shopping-cart text-xl"></i>
              <span v-if="cartItemsCount > 0"
                    class="absolute -top-2 -right-2 bg-b6 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {{ cartItemsCount }}
              </span>
            </button>
          </div>
        </div>

        <!-- Menu Mobile Button -->
        <button @click="toggleMobileMenu" class="md:hidden p-2">
          <div class="w-6 h-6 relative">
            <span class="mobile-menu-line" :class="{ 'mobile-menu-open': isMobileMenuOpen }"></span>
            <span class="mobile-menu-line" :class="{ 'mobile-menu-open': isMobileMenuOpen }"></span>
            <span class="mobile-menu-line" :class="{ 'mobile-menu-open': isMobileMenuOpen }"></span>
          </div>
        </button>
      </div>
    </nav>

    <!-- Menu Mobile -->
    <div class="md:hidden mobile-menu" :class="{ 'mobile-menu-open': isMobileMenuOpen }">
      <div class="bg-white/95 backdrop-blur-sm border-t border-b3/20 py-6">
        <div class="container-custom">
          <div class="flex flex-col space-y-4">
            <!-- Accueil -->
            <router-link to="/" @click="closeMobileMenu" class="flex items-center gap-3 text-lg text-gray-800 hover:text-gray-600">
              <HomeIcon class="w-5 h-5 text-b3" />
              <span>Accueil</span>
            </router-link>

            <!-- Créer un site - Section -->
            <div class="mb-2">
              <div class="text-b3 text-sm font-semibold py-2 flex items-center gap-2">
                <CogIcon class="w-4 h-4" />
                Créer un site
              </div>
              <div class="ml-4 space-y-1">
                <a href="/sur-mesure" @click="closeMobileMenu" class="flex items-center gap-3 py-2 text-gray-700 hover:text-gray-900">
                  <SparklesIcon class="w-4 h-4 text-b3" />
                  <div>
                    <div>Sur-mesure</div>
                    <div class="text-xs text-gray-500">On part de zéro</div>
                  </div>
                </a>
                <a href="/boutique" @click="closeMobileMenu" class="flex items-center gap-3 py-2 text-gray-700 hover:text-gray-900">
                  <ShoppingCartIcon class="w-4 h-4 text-b6" />
                  <div>
                    <div class="text-b6">La Collection</div>
                    <div class="text-xs text-gray-500">Sites uniques et personnalisables</div>
                  </div>
                </a>
                <a href="/options" @click="closeMobileMenu" class="flex items-center gap-3 py-2 text-gray-700 hover:text-gray-900">
                  <PuzzlePieceIcon class="w-4 h-4 text-b4" />
                  <div>
                    <div>Options & modules</div>
                    <div class="text-xs text-gray-500">Fonctionnalités supplémentaires</div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Être accompagné - Section -->
            <div class="mb-2">
              <div class="text-b3 text-sm font-semibold py-2 flex items-center gap-2">
                <UserGroupIcon class="w-4 h-4" />
                Être accompagné
              </div>
              <div class="ml-4 space-y-1">
                <a href="/pack-creation" @click="closeMobileMenu" class="flex items-center gap-3 py-2 text-gray-700 hover:text-gray-900">
                  <RocketLaunchIcon class="w-4 h-4 text-b3" />
                  <div>
                    <div>Pack création d'entreprise</div>
                    <div class="text-xs text-gray-500">Lancement clé en main</div>
                  </div>
                </a>
                <a href="/accompagnement" @click="closeMobileMenu" class="flex items-center gap-3 py-2 text-gray-700 hover:text-gray-900">
                  <ClockIcon class="w-4 h-4 text-b6" />
                  <div>
                    <div class="text-b6">Accompagnement horaire</div>
                    <div class="text-xs text-gray-500">Aide personnalisée</div>
                  </div>
                </a>
                <a href="/community-management" @click="closeMobileMenu" class="flex items-center gap-3 py-2 text-gray-700 hover:text-gray-900">
                  <MegaphoneIcon class="w-4 h-4 text-b4" />
                  <div>
                    <div>Community management</div>
                    <div class="text-xs text-gray-500">Gestion des réseaux sociaux</div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Contact -->
            <button @click="scrollToBottom" class="flex items-center gap-3 text-lg text-gray-800 hover:text-gray-600">
              <EnvelopeIcon class="w-5 h-5 text-b3" />
              <span>Contact</span>
            </button>

            <div class="border-t border-b3/20 pt-4">
              <button @click="toggleCart" class="nav-link flex items-center space-x-2 cursor-pointer">
                <i class="fas fa-shopping-cart"></i>
                <span>Panier ({{ cartItemsCount }})</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cart Sidebar -->
    <CartSidebar :isOpen="cartStore.isOpen" @close="closeCart" />
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useCartStore } from '../../store/cart.js'
import CartSidebar from './CartSidebar.vue'

// Icons
import {
  HomeIcon,
  CogIcon,
  UserGroupIcon,
  EnvelopeIcon,
  ChevronDownIcon,
  SparklesIcon,
  ShoppingCartIcon,
  PuzzlePieceIcon,
  RocketLaunchIcon,
  ClockIcon,
  MegaphoneIcon
} from "@heroicons/vue/24/outline"

const route = useRoute()
const cartStore = useCartStore()

// État réactif
const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)

// États des dropdowns
const createDropdownOpen = ref(false)
const supportDropdownOpen = ref(false)

// Gestion des dropdowns
const toggleCreateDropdown = () => {
  createDropdownOpen.value = !createDropdownOpen.value
  supportDropdownOpen.value = false
}

const toggleSupportDropdown = () => {
  supportDropdownOpen.value = !supportDropdownOpen.value
  createDropdownOpen.value = false
}

const closeAllDropdowns = () => {
  createDropdownOpen.value = false
  supportDropdownOpen.value = false
}

// Computed
const headerClasses = computed(() => ({
  'bg-white/90 backdrop-blur-md shadow-lg': isScrolled.value,
  'bg-transparent': !isScrolled.value
}))

const cartItemsCount = computed(() => cartStore.itemsCount)

// Méthodes
function toggleMobileMenu() {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

function closeMobileMenu() {
  isMobileMenuOpen.value = false
  closeAllDropdowns()
}

function toggleCart() {
  cartStore.toggleCart()
  closeMobileMenu()
}

function closeCart() {
  cartStore.closeCart()
}

function handleScroll() {
  isScrolled.value = window.scrollY > 50
}

// Fonction pour scroll vers le bas de la page courante
const scrollToBottom = () => {
  closeMobileMenu();
  
  requestAnimationFrame(() => {
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth'
    });
  });
};

// Lifecycle
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // Fermer les dropdowns quand on clique ailleurs
  document.addEventListener("click", (e) => {
    if (!e.target.closest('.dropdown-container')) {
      closeAllDropdowns()
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  document.removeEventListener("click", closeAllDropdowns)
})
</script>

<style scoped>
/* Animation pour les dropdowns */
.dropdown-container {
  position: relative;
}

/* Animation des chevrons */
.rotate-180 {
  transform: rotate(180deg);
}

.nav-link {
  color: #374151; /* text-gray-700 par défaut */
  position: relative;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #4b5563; /* text-gray-600 */
}

/* Style spécifique pour LE SHOP - toujours orange */
.nav-link.text-b6 {
  color: var(--color-b6) !important;
}

.nav-link.text-b6:hover {
  color: var(--color-b6) !important;
}

/* Style pour les liens actifs */
.nav-link.text-b3 {
  color: var(--color-b3) !important;
}

/* Menu mobile */
.mobile-menu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.mobile-menu.mobile-menu-open {
  max-height: 400px;
}

.mobile-menu-line {
  display: block;
  width: 100%;
  height: 2px;
  background: #374151;
  position: absolute;
  transition: all 0.3s ease;
}

.mobile-menu-line:nth-child(1) {
  top: 0;
}

.mobile-menu-line:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.mobile-menu-line:nth-child(3) {
  bottom: 0;
}

.mobile-menu-line.mobile-menu-open:nth-child(1) {
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

.mobile-menu-line.mobile-menu-open:nth-child(2) {
  opacity: 0;
}

.mobile-menu-line.mobile-menu-open:nth-child(3) {
  bottom: 50%;
  transform: translateY(50%) rotate(-45deg);
}
</style> 