<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  ChevronDownIcon,
  ChatBubbleLeftRightIcon,
  CreditCardIcon,
  CubeIcon,
  DevicePhoneMobileIcon,
  WrenchScrewdriverIcon,
} from "@heroicons/vue/24/outline";

// Animation des éléments
const titleVisible = ref(false);
const categoriesVisible = ref(false);
const contactVisible = ref(false);

// Catégories avec icônes et couleurs
const categories = ref([
  {
    name: "À propos des sites vitrines",
    icon: CubeIcon,
    color: "black",
    items: [
      {
        question: "Qu'est-ce qu'un site vitrine ?",
        answer:
          "Un site vitrine est une présence web professionnelle qui met en valeur votre entreprise, vos services et votre identité de marque. C'est comme une vitrine digitale disponible 24/7, permettant à vos clients potentiels de découvrir votre activité.",
      },
      {
        question: "Pourquoi ai-je besoin d'un site vitrine ?",
        answer:
          "Un site vitrine est essentiel car il renforce votre crédibilité professionnelle, améliore votre visibilité en ligne, et permet d'atteindre de nouveaux clients. C'est souvent le premier point de contact avec vos prospects, disponible en permanence pour présenter vos services.",
      },
      {
        question: "Quels sont les délais de création d'un site ?",
        answer:
          "Le délai moyen de création d'un site vitrine est de 10 jours ouvrés, à compter de la réception de l'ensemble des éléments nécessaires à sa conception (textes, logo et images si fournis). Ce délai peut varier selon la complexité du projet et votre réactivité lors des échanges.",
      },
    ],
  },
  {
    name: "Aspects techniques",
    icon: DevicePhoneMobileIcon,
    color: "b2",
    items: [
      {
        question: "Est-ce que mon site sera optimisé pour le référencement (SEO) ?",
        answer:
          "Absolument ! Nous intégrons les meilleures pratiques SEO dès la conception : structure optimisée, temps de chargement rapide, métadonnées personnalisées, et contenu structuré.",
      },
      {
        question: "Le site sera-t-il adapté aux mobiles et tablettes ?",
        answer:
          "Oui, nous adoptons une approche 'mobile-first' pour garantir une expérience optimale sur tous les appareils. Chaque élément est soigneusement adapté pour offrir la meilleure expérience utilisateur, quel que soit l'écran.",
      },
      {
        question: "Mon site sera-t-il sécurisé ?",
        answer:
          "La sécurité est notre priorité. Nous utilisons HTTPS/SSL, protection contre les attaques DDoS, et des pratiques de code sécurisées. L'hébergement sur Netlify garantit une infrastructure robuste et des mises à jour de sécurité automatiques.",
      },
    ],
  },
  {
    name: "Maintenance et support",
    icon: WrenchScrewdriverIcon,
    color: "b3",
    items: [
      {
        question: "Comment puis-je mettre à jour mon site après sa création ?",
        answer:
          "Nous proposons un forfait maintenance mensuel pour des modifications régulières, ou des interventions ponctuelles selon vos besoins.",
      },
      {
        question: "Que se passe-t-il si j'ai un problème avec mon site ?",
        answer:
          "Notre support technique est disponible par e-mail avec un temps de réponse garanti sous 48h. Pour les problèmes urgents, nous intervenons dans les 24h. Chaque projet inclut 1 mois minimum de support gratuit après le lancement.",
      },
    ],
  },
  {
    name: "Tarification et paiement",
    icon: CreditCardIcon,
    color: "b6",
    items: [
      {
        question: "Quels sont les coûts d'hébergement du site ?",
        answer:
          "L'hébergement sur Netlify est inclus gratuitement dans nos forfaits. Cette solution professionnelle offre d'excellentes performances et une fiabilité optimale. Pour des besoins spécifiques, ou un trafic important nous pouvons configurer un hébergement personnalisé (coût à définir selon vos besoins).",
      },
      {
        question: "Quels sont les modes de paiement acceptés ?",
        answer:
          "Nous acceptons les virements bancaires, paiements PayPal. Le paiement se fait en deux fois : 50% à la commande (pour démarrer le projet), 50% à la livraison (avant la mise en ligne définitive).",
      },
    ],
  },
]);

// Gestion des états actifs
const activeCategory = ref<number>(0);
const activeItems = ref<Set<number>>(new Set());

const toggleItem = (categoryIndex: number, itemIndex: number) => {
  const key = categoryIndex * 1000 + itemIndex;
  if (activeItems.value.has(key)) {
    activeItems.value.delete(key);
  } else {
    activeItems.value.add(key);
  }
};

const isItemActive = (categoryIndex: number, itemIndex: number): boolean => {
  return activeItems.value.has(categoryIndex * 1000 + itemIndex);
};

// Animation séquentielle des éléments
onMounted(() => {
  setTimeout(() => {
    titleVisible.value = true;
  }, 300);
  setTimeout(() => {
    categoriesVisible.value = true;
  }, 800);
  setTimeout(() => {
    contactVisible.value = true;
  }, 1500);
});
</script>

<template>
  <section
    id="faq"
    class="relative py-16 md:py-24 bg-gradient-to-b from-white to-gray-100 overflow-hidden"
    aria-label="Foire aux questions"
    itemscope
    itemtype="https://schema.org/FAQPage"
  >
    <!-- Formes décoratives -->
    <div class="absolute inset-0 overflow-hidden">
      <div
        class="absolute top-0 -right-20 w-96 h-96 bg-b3/15 rounded-full filter blur-3xl"
      ></div>
      <div
        class="absolute bottom-0 left-0 w-80 h-80 bg-b6/15 rounded-full filter blur-3xl"
      ></div>
      <div
        class="absolute top-1/3 left-1/4 w-64 h-64 bg-b2/15 rounded-full filter blur-3xl"
      ></div>
    </div>

    <!-- Contenu principal -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Titre de section élégant -->
      <div class="text-center mb-8 md:mb-16" :class="{ 'animate-fade-in': titleVisible }">
        <!-- Badge moderne -->
        <div
          class="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-xs md:text-sm font-medium mb-4 md:mb-8 shadow-sm"
        >
          <span class="w-2 h-2 rounded-full bg-b3 mr-2 text-gray-800"></span>
          Questions fréquentes
          <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
        </div>

        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2 md:mb-4">Foire aux questions</h2>
        <p class="max-w-2xl mx-auto text-gray-700 text-sm md:text-base">
          Retrouvez les réponses aux questions les plus fréquemment posées sur nos
          services
        </p>
      </div>

      <!-- Onglets de catégories -->
      <div
        class="flex flex-wrap justify-center gap-2 md:gap-4 mb-6 md:mb-12"
        :class="{ 'animate-fade-in-delayed': categoriesVisible }"
      >
        <button
          v-for="(category, index) in categories"
          :key="index"
          @click="activeCategory = index"
          :class="[
            'flex items-center px-3 py-2 md:px-5 md:py-3 rounded-full transition-all duration-300 border cursor-pointer text-xs md:text-base',
            activeCategory === index
              ? `bg-${category.color} text-white shadow-md`
              : 'bg-white text-gray-700 hover:bg-gray-50 hover:shadow-md',
          ]"
        >
          <component :is="category.icon" class="w-4 h-4 md:w-5 md:h-5 mr-1 md:mr-2" />
          <span>{{ category.name }}</span>
        </button>
      </div>

      <!-- Questions et réponses -->
      <div
        class="bg-white rounded-2xl shadow-xl p-4 md:p-8 mb-8 md:mb-16"
        :class="{ 'animate-fade-in-delayed': categoriesVisible }"
      >
        <div
          v-for="(category, categoryIndex) in categories"
          :key="categoryIndex"
          v-show="activeCategory === categoryIndex"
        >
          <div class="space-y-2 md:space-y-4">
            <div
              v-for="(item, itemIndex) in category.items"
              :key="itemIndex"
              class="border border-gray-200 rounded-xl overflow-hidden transition-all duration-300 hover:border-b3/50"
              :class="{ 'shadow-md': isItemActive(categoryIndex, itemIndex) }"
            >
              <!-- Question -->
              <button
                @click="toggleItem(categoryIndex, itemIndex)"
                class="w-full text-left px-4 py-3 md:px-6 md:py-5 flex justify-between items-center gap-4 md:gap-6 bg-white hover:bg-gray-50 transition-colors duration-300 cursor-pointer"
                :aria-expanded="isItemActive(categoryIndex, itemIndex) ? 'true' : 'false'"
                :aria-controls="`faq-answer-${categoryIndex}-${itemIndex}`"
              >
                <div class="flex items-center">
                  <span class="text-base md:text-lg font-medium text-gray-800">{{
                    item.question
                  }}</span>
                </div>
                <div
                  :class="`w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center bg-b6/10`"
                >
                  <ChevronDownIcon
                    :class="`w-4 h-4 md:w-5 md:h-5 text-b6 transition-transform duration-300 ${isItemActive(categoryIndex, itemIndex) ? 'rotate-180' : ''}`"
                  />
                </div>
              </button>

              <!-- Réponse -->
              <div
                v-show="isItemActive(categoryIndex, itemIndex)"
                class="px-4 py-3 md:px-6 md:py-5 bg-gray-50 border-t border-gray-200"
                itemscope
                itemtype="https://schema.org/Answer"
                itemprop="acceptedAnswer"
                :id="`faq-answer-${categoryIndex}-${itemIndex}`"
              >
                <p class="text-gray-700 leading-relaxed pl-0 text-sm md:text-base">
                  {{ item.answer }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Section de contact -->
      <div
        class="bg-gradient-to-r from-b3/20 to-b6/20 rounded-2xl p-4 md:p-8 flex flex-col md:flex-row justify-between items-center gap-4 md:gap-6"
        :class="{ 'animate-fade-in-delayed-more': contactVisible }"
      >
        <div class="flex items-center">
          <ChatBubbleLeftRightIcon class="w-8 h-8 md:w-10 md:h-10 text-b6 mr-3 md:mr-4" />
          <div>
            <h3 class="text-lg md:text-xl font-semibold text-gray-800">
              Vous ne trouvez pas la réponse à votre question ?
            </h3>
            <p class="text-gray-700 text-sm md:text-base">Notre équipe est disponible pour vous aider</p>
          </div>
        </div>
        <a
          href="#contact"
          class="px-4 py-2 md:px-6 md:py-3 bg-b6 text-white rounded-lg shadow-md hover:bg-b6/90 hover:shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center cursor-pointer group text-sm md:text-base faq-contact"
        >
          <span class="faq-contact">Contactez-nous</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 md:h-5 md:w-5 ml-1 md:ml-2 transition-transform duration-300 group-hover:translate-x-1 faq-contact"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </a>
      </div>
    </div>
  </section>
</template>

<style scoped>
.animate-fade-in {
  animation: fadeIn 1s ease-out forwards;
}

.animate-fade-in-delayed {
  opacity: 0;
  animation: fadeIn 1s ease-out 0.5s forwards;
}

.animate-fade-in-delayed-more {
  opacity: 0;
  animation: fadeIn 1s ease-out 1.2s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Style pour tous les éléments cliquables */
button,
a,
[role="button"],
.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}
</style>
