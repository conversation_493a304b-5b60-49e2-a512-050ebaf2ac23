<template>
  <div class="min-h-screen bg-white">
    
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-slate-200/50 sticky top-0 z-50">
      <div class="container mx-auto px-6 lg:px-8">
        <div class="flex items-center justify-between h-20">
          <!-- Logo -->
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-b3 to-b6 rounded-xl flex items-center justify-center mr-3">
              <span class="text-white font-bold text-lg">TD</span>
            </div>
            <span class="text-2xl font-black text-slate-900">TheDevImpact</span>
          </div>
          
          <!-- Navigation -->
          <div class="hidden md:flex items-center space-x-6">
            <!-- Accueil -->
            <a href="/" class="flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-300 rounded-lg hover:bg-gray-50 cursor-pointer">
              <HomeIcon class="w-4 h-4 text-b3" />
              <span>Accueil</span>
            </a>

            <!-- Créer un site - Dropdown -->
            <div class="relative dropdown-container">
              <button
                @click="toggleCreateDropdown"
                class="flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-300 rounded-lg hover:bg-gray-50 cursor-pointer"
                :class="{ 'text-gray-900 bg-gray-50': createDropdownOpen }"
              >
                <CogIcon class="w-4 h-4 text-b3" />
                <span>Créer un site</span>
                <ChevronDownIcon class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': createDropdownOpen }" />
              </button>

              <!-- Dropdown Menu -->
              <div v-if="createDropdownOpen" class="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50">
                <a href="/formules" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 group cursor-pointer">
                  <div class="w-8 h-8 rounded-full bg-b3/10 flex items-center justify-center group-hover:bg-b3/20 transition-colors">
                    <SparklesIcon class="w-4 h-4 text-b3" />
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">Sur-mesure</div>
                    <div class="text-sm text-gray-500">On part de zéro</div>
                  </div>
                </a>
                <a href="/boutique" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 group cursor-pointer">
                  <div class="w-8 h-8 rounded-full bg-b6/10 flex items-center justify-center group-hover:bg-b6/20 transition-colors">
                    <ShoppingCartIcon class="w-4 h-4 text-b6" />
                  </div>
                  <div>
                    <div class="font-medium text-b6">La Collection</div>
                    <div class="text-sm text-gray-500">Sites uniques et personnalisables</div>
                  </div>
                </a>
                <a href="/options" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 group cursor-pointer">
                  <div class="w-8 h-8 rounded-full bg-b4/10 flex items-center justify-center group-hover:bg-b4/20 transition-colors">
                    <PuzzlePieceIcon class="w-4 h-4 text-b4" />
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">Options & modules</div>
                    <div class="text-sm text-gray-500">Fonctionnalités supplémentaires</div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Être accompagné - Dropdown -->
            <div class="relative dropdown-container">
              <button
                @click="toggleSupportDropdown"
                class="flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-300 rounded-lg hover:bg-gray-50 cursor-pointer"
                :class="{ 'text-gray-900 bg-gray-50': supportDropdownOpen }"
              >
                <UserGroupIcon class="w-4 h-4 text-b3" />
                <span>Être accompagné</span>
                <ChevronDownIcon class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': supportDropdownOpen }" />
              </button>

              <!-- Dropdown Menu -->
              <div v-if="supportDropdownOpen" class="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50">
                <a href="/pack-creation" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
                  <div class="w-8 h-8 rounded-full bg-b3/10 flex items-center justify-center group-hover:bg-b3/20 transition-colors">
                    <RocketLaunchIcon class="w-4 h-4 text-b3" />
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">Pack création d'entreprise</div>
                    <div class="text-sm text-gray-500">Lancement clé en main</div>
                  </div>
                </a>
                <a href="/accompagnement" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
                  <div class="w-8 h-8 rounded-full bg-b6/10 flex items-center justify-center group-hover:bg-b6/20 transition-colors">
                    <ClockIcon class="w-4 h-4 text-b6" />
                  </div>
                  <div>
                    <div class="font-medium text-b6">Accompagnement horaire</div>
                    <div class="text-sm text-gray-500">Aide personnalisée</div>
                  </div>
                </a>
                <a href="/community-management" class="flex items-start gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
                  <div class="w-8 h-8 rounded-full bg-b4/10 flex items-center justify-center group-hover:bg-b4/20 transition-colors">
                    <MegaphoneIcon class="w-4 h-4 text-b4" />
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">Community management</div>
                    <div class="text-sm text-gray-500">Gestion des réseaux sociaux</div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Contact -->
            <button @click="scrollToBottom" class="flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-300 rounded-lg hover:bg-gray-50 cursor-pointer">
              <EnvelopeIcon class="w-4 h-4 text-b3" />
              <span>Contact</span>
            </button>

            <!-- Cart Icon -->
            <div class="relative">
              <button @click="toggleCart" class="p-2 text-slate-600 hover:text-b3 transition-colors cursor-pointer">
                <i class="fas fa-shopping-cart text-lg"></i>
                <span v-if="cartItemsCount > 0" class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-b3 to-b6 rounded-full flex items-center justify-center text-white text-xs font-bold">{{ cartItemsCount }}</span>
              </button>
            </div>
          </div>

          <!-- Menu Mobile Button -->
          <button class="md:hidden p-2 text-slate-600">
            <i class="fas fa-bars text-lg"></i>
          </button>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section id="accueil" class="relative py-12 lg:py-16 bg-gradient-to-br from-slate-50 to-white overflow-hidden">
      <!-- Formes décoratives -->
      <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-b3/5 to-b6/5 rounded-full blur-3xl"></div>
        <div class="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-b4/5 to-b5/5 rounded-full blur-3xl"></div>
      </div>
      
      <div class="container mx-auto px-6 lg:px-8 relative z-10">
        <div class="grid lg:grid-cols-2 gap-16 items-center">
          
          <!-- Contenu -->
          <div class="space-y-8">
            <div class="inline-flex items-center px-4 py-2 bg-b3/10 rounded-full border border-b3/20">
              <div class="w-2 h-2 bg-b3 rounded-full mr-3 animate-pulse"></div>
              <span class="text-b3 font-semibold text-sm uppercase tracking-wider">Nouvelle Collection</span>
            </div>
            
            <h1 class="text-5xl lg:text-7xl font-black text-slate-900 leading-tight">
              Sites
              <span class="bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text block">
                Premium
              </span>
            </h1>
            
            <p class="text-xl text-slate-600 leading-relaxed max-w-lg">
              Des sites web exceptionnels qui transforment vos visiteurs en clients. Design moderne, performances optimales et expérience utilisateur remarquable.
            </p>
            
            <div class="flex flex-wrap gap-4 text-sm text-slate-500">
              <div class="flex items-center">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                <span>Design sur-mesure</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                <span>Responsive mobile</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                <span>SEO optimisé</span>
              </div>
            </div>
            
            <div class="flex flex-col sm:flex-row gap-4">
              <button @click="scrollToShop" class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-b3 to-b6 text-white font-bold rounded-2xl hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                <span>Voir les sites</span>
                <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform"></i>
              </button>
              <button @click="scrollToProcess" class="group inline-flex items-center px-8 py-4 bg-white border-2 border-slate-200 hover:border-b3 text-slate-700 hover:text-b3 font-bold rounded-2xl hover:shadow-lg transition-all duration-300">
                <span>Découvrir le processus</span>
                <i class="fas fa-info-circle ml-3 group-hover:scale-110 transition-transform"></i>
              </button>
            </div>
            
            <!-- Stats -->
            <div class="flex items-center gap-8 pt-8">
              <div class="text-center">
                <div class="text-3xl font-black text-slate-900">Web</div>
                <div class="text-sm text-slate-500 font-semibold">& Mobile</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-black bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">100%</div>
                <div class="text-sm text-slate-500 font-semibold">Sur-mesure</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-black text-slate-900">5-10j</div>
                <div class="text-sm text-slate-500 font-semibold">Après démarrage</div>
              </div>
            </div>
          </div>
          
          <!-- Visuel -->
          <div class="relative">
            <div class="relative bg-white rounded-3xl shadow-2xl border border-slate-200/50 p-8 transform rotate-2 hover:rotate-0 transition-transform duration-500">
              <div class="aspect-[4.8/3] rounded-2xl overflow-hidden">
                <img src="../../assets/lvb.webp" alt="Aperçu Site Web Premium - Design Moderne" class="w-full h-full object-cover" />
              </div>
            </div>
            
            <!-- Éléments flottants -->
            <div class="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-b4 to-b5 rounded-2xl flex items-center justify-center shadow-lg">
              <i class="fas fa-star text-2xl text-white"></i>
            </div>
            
            <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-b6 to-b3 rounded-xl flex items-center justify-center shadow-lg">
              <i class="fas fa-heart text-xl text-white"></i>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Process Section -->
    <section id="process" class="py-12 lg:py-16 bg-white">
      <div class="container mx-auto px-6 lg:px-8">
        
        <!-- Header -->
        <div class="text-center mb-12">
          <div class="inline-flex items-center px-4 py-2 bg-b3/10 rounded-full border border-b3/20 mb-6">
            <div class="w-2 h-2 bg-b3 rounded-full mr-3 animate-pulse"></div>
            <span class="text-b3 font-semibold text-sm uppercase tracking-wider">Processus Simple</span>
          </div>
          
          <h2 class="text-3xl lg:text-4xl font-black text-slate-900 mb-4">
            Comment ça 
            <span class="bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">marche ?</span>
          </h2>
          
          <p class="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Un processus simplifié en 5 étapes pour obtenir votre site web professionnel. 
            Rapide, sécurisé et sans stress.
          </p>
        </div>

        <!-- Timeline Process -->
        <div class="relative">
          <!-- Ligne de connexion -->
          <div class="hidden lg:block absolute top-24 left-0 right-0 h-0.5 bg-gradient-to-r from-b3 via-b4 via-b5 to-b6 opacity-30"></div>
          
          <!-- Étapes -->
          <div class="grid md:grid-cols-3 lg:grid-cols-5 gap-8 lg:gap-4">
            
            <!-- Étape 1: Choisissez -->
            <div class="group relative">
              <div class="bg-white rounded-3xl p-8 border border-slate-200/50 shadow-lg hover:shadow-xl transition-all duration-500 h-full relative overflow-hidden">
                <!-- Accent coloré -->
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-b3 to-b4"></div>
                
                <!-- Numéro avec icône -->
                <div class="relative mb-6">
                  <div class="w-16 h-16 bg-gradient-to-br from-b3 to-b4 rounded-2xl flex items-center justify-center text-white font-bold text-xl mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                    <i class="fas fa-mouse-pointer text-2xl"></i>
                  </div>
                  <div class="absolute -top-2 -right-2 w-8 h-8 bg-b3 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                </div>
                
                <!-- Contenu -->
                <div class="text-center">
                  <h3 class="text-xl font-bold text-slate-900 mb-3">Choisissez votre site</h3>
                  <p class="text-slate-600 leading-relaxed text-sm">
                    Parcourez notre collection et sélectionnez le modèle qui correspond parfaitement à votre vision et vos besoins.
                  </p>
                </div>
              </div>
            </div>

            <!-- Étape 2: Commandez -->
            <div class="group relative">
              <div class="bg-white rounded-3xl p-8 border border-slate-200/50 shadow-lg hover:shadow-xl transition-all duration-500 h-full relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-b4 to-b5"></div>
                
                <div class="relative mb-6">
                  <div class="w-16 h-16 bg-gradient-to-br from-b4 to-b5 rounded-2xl flex items-center justify-center text-white font-bold text-xl mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                    <i class="fas fa-credit-card text-2xl"></i>
                  </div>
                  <div class="absolute -top-2 -right-2 w-8 h-8 bg-b4 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                </div>
                
                <div class="text-center">
                  <h3 class="text-xl font-bold text-slate-900 mb-3">Commandez en ligne</h3>
                  <p class="text-slate-600 leading-relaxed text-sm">
                    Procédez au paiement sécurisé directement sur notre plateforme. Rapide, simple et 100% protégé.
                  </p>
                </div>
              </div>
            </div>

            <!-- Étape 3: Envoyez -->
            <div class="group relative">
              <div class="bg-white rounded-3xl p-8 border border-slate-200/50 shadow-lg hover:shadow-xl transition-all duration-500 h-full relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-b5 to-b6"></div>
                
                <div class="relative mb-6">
                  <div class="w-16 h-16 bg-gradient-to-br from-b5 to-b6 rounded-2xl flex items-center justify-center text-white font-bold text-xl mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                    <i class="fas fa-upload text-2xl"></i>
                  </div>
                  <div class="absolute -top-2 -right-2 w-8 h-8 bg-b5 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                </div>
                
                <div class="text-center">
                  <h3 class="text-xl font-bold text-slate-900 mb-3">Envoyez vos contenus</h3>
                  <p class="text-slate-600 leading-relaxed text-sm">
                    Transmettez-nous vos textes, images et logo. Un processus guidé et simple pour ne rien oublier.
                  </p>
                </div>
              </div>
            </div>

            <!-- Étape 4: Personnalisation -->
            <div class="group relative">
              <div class="bg-white rounded-3xl p-8 border border-slate-200/50 shadow-lg hover:shadow-xl transition-all duration-500 h-full relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-b6 to-b3"></div>
                
                <div class="relative mb-6">
                  <div class="w-16 h-16 bg-gradient-to-br from-b6 to-b3 rounded-2xl flex items-center justify-center text-white font-bold text-xl mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                    <i class="fas fa-magic text-2xl"></i>
                  </div>
                  <div class="absolute -top-2 -right-2 w-8 h-8 bg-b6 rounded-full flex items-center justify-center text-white font-bold text-sm">4</div>
                </div>
                
                <div class="text-center">
                  <h3 class="text-xl font-bold text-slate-900 mb-3">Personnalisation rapide</h3>
                  <p class="text-slate-600 leading-relaxed text-sm">
                    Nous personnalisons votre site avec vos contenus. Vous n'avez rien à faire, nous nous occupons de tout !
                  </p>
                </div>
              </div>
            </div>

            <!-- Étape 5: Livraison -->
            <div class="group relative">
              <div class="bg-white rounded-3xl p-8 border border-slate-200/50 shadow-lg hover:shadow-xl transition-all duration-500 h-full relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-b3 to-b6"></div>
                
                <div class="relative mb-6">
                  <div class="w-16 h-16 bg-gradient-to-br from-b3 to-b6 rounded-2xl flex items-center justify-center text-white font-bold text-xl mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                    <i class="fas fa-rocket text-2xl"></i>
                  </div>
                  <div class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-b3 to-b6 rounded-full flex items-center justify-center text-white font-bold text-sm">5</div>
                </div>
                
                                 <div class="text-center">
                   <h3 class="text-xl font-bold text-slate-900 mb-3">Livraison express</h3>
                   <p class="text-slate-600 leading-relaxed text-sm">
                     Votre site finalisé est livré sous 5 à 10 jours. Vous recevez le lien de votre site prêt à impressionner !
                   </p>
                 </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-12">
          <div class="inline-flex items-center bg-gradient-to-r from-slate-50 to-white rounded-3xl shadow-xl border border-slate-200/50 p-8">
            <div class="flex items-center gap-6">
              <div class="w-16 h-16 bg-gradient-to-br from-b3 to-b6 rounded-2xl flex items-center justify-center">
                <i class="fas fa-clock text-2xl text-white"></i>
              </div>
              <div class="text-left">
                <h3 class="text-xl font-bold text-slate-900 mb-2">Délai garanti</h3>
                <p class="text-slate-600">
                  <span class="font-semibold text-b3">5-10 jours</span> après réception de vos contenus
                </p>
              </div>
            </div>
            
            <div class="w-px h-16 bg-slate-200 mx-8"></div>
            
            <div class="flex items-center gap-6">
              <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
                <i class="fas fa-shield-alt text-2xl text-white"></i>
              </div>
              <div class="text-left">
                <h3 class="text-xl font-bold text-slate-900 mb-2">Paiement sécurisé</h3>
                <p class="text-slate-600">
                  Transactions <span class="font-semibold text-green-600">100% protégées</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Shop Section -->
    <section id="shop" class="py-12 lg:py-16 bg-gradient-to-br from-slate-50 to-white">
      <div class="container mx-auto px-6 lg:px-8">
        
        <!-- Header -->
        <div class="text-center mb-10">
          <div class="inline-flex items-center px-4 py-2 bg-b3/10 rounded-full border border-b3/20 mb-8">
            <span class="text-b3 font-semibold text-sm uppercase tracking-wider">Notre Collection</span>
          </div>
          
          <h2 class="text-4xl lg:text-5xl font-black text-slate-900 mb-6">
            Sites Vitrines
            <span class="bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">Premium</span>
          </h2>
          
          <p class="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
            Découvrez notre sélection de designs uniques, créés par nos soins, pour sublimer votre présence digitale
          </p>
          
          <!-- Message d'exclusivité -->
          <div class="mt-6 max-w-5xl mx-auto">
            <div class="bg-gradient-to-r from-amber-50 via-orange-50 to-amber-50 border-l-4 border-amber-400 p-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="flex-shrink-0">
                    <i class="fas fa-star text-amber-500 text-lg"></i>
                  </div>
                  <div>
                    <p class="text-slate-800 font-medium">
                      <span class="font-bold">Exclusivité garantie</span> — Chaque site ne sera vendu qu'une seule fois
                    </p>
                  </div>
                </div>
                <div class="hidden md:flex items-center gap-4 text-sm text-slate-600">
                  <span class="flex items-center gap-1">
                    <i class="fas fa-check text-green-500"></i>
                    Unique
                  </span>
                  <span class="flex items-center gap-1">
                    <i class="fas fa-shield-alt text-b3"></i>
                    Certifié
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Filtres -->
        <div class="mb-8">
          <div class="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-4 lg:p-5">
            <div class="space-y-4 lg:space-y-0 lg:flex lg:items-center lg:gap-6">
              
              <!-- Catégories -->
              <div class="flex-1">
                <label class="block text-xs font-bold text-slate-700 mb-2 uppercase tracking-wider">Catégorie</label>
                <div class="flex flex-wrap gap-2">
                  <button 
                    v-for="category in categories" 
                    :key="category.id"
                    @click="selectedCategory = category.id"
                    :class="[
                      'px-3 py-1.5 rounded-lg font-semibold text-xs transition-all duration-300 border whitespace-nowrap',
                      selectedCategory === category.id 
                        ? 'bg-gradient-to-r from-b3 to-b6 text-white border-transparent shadow-md' 
                        : 'bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100 hover:border-slate-300'
                    ]"
                  >
                    {{ category.name }}
                    <span class="ml-1.5 px-1.5 py-0.5 rounded-full text-xs bg-black/10">
                      {{ category.count }}
                    </span>
                  </button>
                </div>
              </div>

              <!-- Tri -->
              <div class="w-full lg:w-48">
                <label class="block text-xs font-bold text-slate-700 mb-2 uppercase tracking-wider">Trier par</label>
                <select 
                  v-model="sortBy"
                  class="w-full px-3 py-2 bg-slate-50 border border-slate-200 rounded-lg text-slate-700 font-medium text-sm focus:outline-none focus:ring-2 focus:ring-b3/20 focus:border-b3 transition-all duration-300"
                >
                  <option value="featured">Mis en avant</option>
                  <option value="price-low">Prix croissant</option>
                  <option value="price-high">Prix décroissant</option>
                  <option value="name">Nom A-Z</option>
                </select>
              </div>

              <!-- Vue (masquée si peu de produits) -->
              <div v-if="filteredProducts.length > 1" class="w-full lg:w-20">
                <label class="block text-xs font-bold text-slate-700 mb-2 uppercase tracking-wider">Vue</label>
                <div class="flex gap-1">
                  <button 
                    @click="viewMode = 'grid'"
                    :class="[
                      'flex-1 py-2 px-2 rounded-lg transition-all duration-300 border text-sm',
                      viewMode === 'grid' 
                        ? 'bg-gradient-to-r from-b3 to-b6 text-white border-transparent shadow-md' 
                        : 'bg-slate-50 text-slate-600 border-slate-200 hover:bg-slate-100'
                    ]"
                  >
                    <i class="fas fa-th-large"></i>
                  </button>
                  <button 
                    @click="viewMode = 'list'"
                    :class="[
                      'flex-1 py-2 px-2 rounded-lg transition-all duration-300 border text-sm',
                      viewMode === 'list' 
                        ? 'bg-gradient-to-r from-b3 to-b6 text-white border-transparent shadow-md' 
                        : 'bg-slate-50 text-slate-600 border-slate-200 hover:bg-slate-100'
                    ]"
                  >
                    <i class="fas fa-list"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Grille de produits -->
        <div class="mb-16">
          <!-- Produits disponibles -->
          <div 
            v-if="filteredProducts.length > 0"
            :class="[
              'grid gap-8',
              viewMode === 'grid' 
                ? 'md:grid-cols-2 lg:grid-cols-3' 
                : 'grid-cols-1 max-w-5xl mx-auto'
            ]"
          >
            <ProductCard 
              v-for="product in filteredProducts" 
              :key="product.id"
              :product="product"
              class="group transform transition-all duration-500"
            />
          </div>
          
          <!-- Message quand aucun produit -->
          <div v-else class="text-center py-16">
            <div class="max-w-md mx-auto">
              <div class="w-20 h-20 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-search text-3xl text-slate-400"></i>
              </div>
              <h3 class="text-2xl font-bold text-slate-900 mb-4">Aucun site disponible</h3>
              <p class="text-slate-600 mb-6">
                Nous n'avons pas encore de sites dans la catégorie 
                <span class="font-semibold text-b3">{{ getCategoryName() }}</span>.
              </p>
              <p class="text-sm text-slate-500 mb-8">
                De nouveaux designs arrivent bientôt ! Revenez consulter notre collection régulièrement.
              </p>
              <button 
                @click="selectedCategory = 'all'" 
                class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-b3 to-b6 text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300">
                <i class="fas fa-arrow-left mr-2"></i>
                Voir tous les sites
              </button>
            </div>
          </div>
        </div>

        <!-- Statistiques -->
        <div class="text-center">
          <div class="inline-flex items-center bg-white rounded-3xl shadow-xl border border-slate-200/50 p-8">
            <div class="text-center px-6">
              <div class="text-3xl font-black text-slate-900 mb-2">1</div>
              <div class="text-sm font-semibold text-slate-500 uppercase tracking-wider">Site unique</div>
            </div>
            
            <div class="w-px h-16 bg-slate-200 mx-6"></div>
            
            <div class="text-center px-6">
              <div class="text-3xl font-black bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text mb-2">100%</div>
              <div class="text-sm font-semibold text-slate-500 uppercase tracking-wider">Personnalisable</div>
            </div>
            
            <div class="w-px h-16 bg-slate-200 mx-6"></div>
            
            <div class="text-center px-6">
              <div class="text-3xl font-black bg-gradient-to-r from-b4 to-b5 text-transparent bg-clip-text mb-2">5-10j</div>
              <div class="text-sm font-semibold text-slate-500 uppercase tracking-wider">Délai de livraison</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Promotion Section -->
    <section class="py-12 bg-gradient-to-r from-b3 to-b6 relative overflow-hidden">
      <!-- Formes décoratives -->
      <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-40 h-40 bg-white/10 rounded-full blur-2xl"></div>
        <div class="absolute bottom-10 right-10 w-60 h-60 bg-white/5 rounded-full blur-3xl"></div>
      </div>
      
      <div class="container mx-auto px-6 lg:px-8 relative z-10">
        <div class="text-center">
          <div class="inline-flex items-center px-4 py-2 bg-white/20 rounded-full border border-white/30 mb-8">
            <i class="fas fa-fire text-white mr-2"></i>
            <span class="text-white font-semibold text-sm uppercase tracking-wider">Offre Été</span>
          </div>
          
          <h2 class="text-4xl lg:text-5xl font-black text-white mb-6">
            -10% sur tous nos sites
          </h2>
          
          <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Profitez de notre offre été et créez votre présence digitale avec un site web professionnel à prix réduit
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <button @click="scrollToShop" class="inline-flex items-center px-8 py-4 bg-white text-b3 font-bold rounded-2xl hover:shadow-xl transform hover:scale-105 transition-all duration-300">
              <i class="fas fa-tag mr-3"></i>
              Profiter de l'offre
            </button>
            
            <div class="flex items-center text-white/90">
              <i class="fas fa-clock mr-2"></i>
              <span class="font-semibold">Offre valable jusqu'au 31 août</span>
            </div>
          </div>
          
          <!-- Countdown -->
          <div class="flex justify-center gap-4">
            <div class="bg-white/20 rounded-2xl p-4 backdrop-blur-sm">
              <div class="text-2xl font-black text-white">{{ countdown.days }}</div>
              <div class="text-sm text-white/80">Jours</div>
            </div>
            <div class="bg-white/20 rounded-2xl p-4 backdrop-blur-sm">
              <div class="text-2xl font-black text-white">{{ countdown.hours }}</div>
              <div class="text-sm text-white/80">Heures</div>
            </div>
            <div class="bg-white/20 rounded-2xl p-4 backdrop-blur-sm">
              <div class="text-2xl font-black text-white">{{ countdown.minutes }}</div>
              <div class="text-sm text-white/80">Minutes</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-12 lg:py-16 bg-gradient-to-br from-slate-50 to-white">
      <div class="container mx-auto px-6 lg:px-8">
        <div class="grid lg:grid-cols-2 gap-16 items-center">
          
          <!-- Contenu -->
          <div class="space-y-8">
            <div class="inline-flex items-center px-4 py-2 bg-b3/10 rounded-full border border-b3/20">
              <span class="text-b3 font-semibold text-sm uppercase tracking-wider">Notre Mission</span>
            </div>
            
            <h2 class="text-4xl lg:text-5xl font-black text-slate-900 leading-tight">
              Créer des sites web qui 
              <span class="bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">
                marquent les esprits
              </span>
            </h2>
            
            <div class="space-y-6 text-lg text-slate-600 leading-relaxed">
              <p>
                Chez TheDevImpact, nous croyons que chaque entreprise mérite une présence digitale exceptionnelle. 
                Notre passion pour le design et la technologie nous pousse à créer des sites web qui ne se contentent 
                pas d'être beaux, mais qui convertissent réellement.
              </p>
              
              <p>
                Chaque modèle de notre collection est pensé pour offrir une expérience utilisateur optimale, 
                tout en reflétant l'identité unique de votre marque. Nous combinons créativité, performance 
                et facilité d'utilisation pour vous donner l'avantage concurrentiel que vous méritez.
              </p>
            </div>
            
            <div class="flex items-center gap-8">
              <div class="text-center">
                <div class="text-3xl font-black text-slate-900">Web</div>
                <div class="text-3xl font-black bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">& Mobile</div>
                <div class="text-sm text-slate-500 font-semibold">Technologies</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-black bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">100%</div>
                <div class="text-sm text-slate-500 font-semibold">Sur-mesure</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-black text-slate-900">Support</div>
                <div class="text-3xl font-black bg-gradient-to-r from-b4 to-b5 text-transparent bg-clip-text">Premium</div>
                <div class="text-sm text-slate-500 font-semibold">30 jours inclus</div>
              </div>
            </div>
          </div>
          
          <!-- Visuel -->
          <div class="relative">
            <div class="aspect-square bg-gradient-to-br from-b3/10 to-b6/10 rounded-3xl flex items-center justify-center p-8">
              <img src="/src/assets/UI-UX.png" alt="UI/UX Design" class="w-full h-full object-contain">
            </div>
            
            <!-- Badges flottants -->
            <div class="absolute -top-4 -right-4 bg-white rounded-2xl shadow-lg p-4 border border-slate-200/50">
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-sm font-semibold text-slate-700">En ligne</span>
              </div>
            </div>
            
            <div class="absolute -bottom-4 -left-4 bg-white rounded-2xl shadow-lg p-4 border border-slate-200/50">
              <div class="flex items-center gap-2">
                <i class="fas fa-shield-alt text-b3"></i>
                <span class="text-sm font-semibold text-slate-700">Sécurisé</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- Cart Sidebar -->
    <CartSidebar :isOpen="cartStore.isOpen" @close="closeCart" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { products, getProductsByCategory, getCategories } from '../../data/products.js'
import { useCartStore } from '../../store/cart.js'
import ProductCard from './ProductCard.vue'
import CartSidebar from './CartSidebar.vue'

// Icons
import {
  HomeIcon,
  CogIcon,
  UserGroupIcon,
  EnvelopeIcon,
  ChevronDownIcon,
  SparklesIcon,
  ShoppingCartIcon,
  PuzzlePieceIcon,
  RocketLaunchIcon,
  ClockIcon,
  MegaphoneIcon
} from "@heroicons/vue/24/outline"

// Store
const cartStore = useCartStore()

// État réactif
const selectedCategory = ref('all')
const sortBy = ref('featured')
const viewMode = ref('grid')
const countdown = ref({
  days: 0,
  hours: 0,
  minutes: 0
})
let countdownInterval = null

// États des dropdowns
const createDropdownOpen = ref(false)
const supportDropdownOpen = ref(false)

// Gestion des dropdowns
const toggleCreateDropdown = () => {
  createDropdownOpen.value = !createDropdownOpen.value
  supportDropdownOpen.value = false
}

const toggleSupportDropdown = () => {
  supportDropdownOpen.value = !supportDropdownOpen.value
  createDropdownOpen.value = false
}

const closeAllDropdowns = () => {
  createDropdownOpen.value = false
  supportDropdownOpen.value = false
}

// Computed
const categories = computed(() => getCategories())
const cartItemsCount = computed(() => cartStore.itemsCount)

const filteredProducts = computed(() => {
  let filtered = selectedCategory.value === 'all' 
    ? products 
    : getProductsByCategory(selectedCategory.value)
  
  // Tri
  switch (sortBy.value) {
    case 'price-low':
      filtered = [...filtered].sort((a, b) => a.price - b.price)
      break
    case 'price-high':
      filtered = [...filtered].sort((a, b) => b.price - a.price)
      break
    case 'name':
      filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name))
      break
    case 'featured':
    default:
      filtered = [...filtered].sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0))
      break
  }
  
  return filtered
})

// Méthodes
function scrollToShop() {
  document.getElementById('shop').scrollIntoView({ behavior: 'smooth' })
}

function scrollToProcess() {
  document.getElementById('process').scrollIntoView({ behavior: 'smooth' })
}

function filterByCategory(category) {
  selectedCategory.value = category
  scrollToShop()
}

function resetFilters() {
  selectedCategory.value = 'all'
  sortBy.value = 'featured'
}

function scrollToBottom() {
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: 'smooth'
  })
}

function toggleCart() {
  cartStore.toggleCart()
}

function closeCart() {
  cartStore.closeCart()
}

function getCategoryName() {
  const category = categories.value.find(cat => cat.id === selectedCategory.value)
  return category ? category.name : 'cette catégorie'
}

function updateCountdown() {
  const now = new Date()
  const currentYear = now.getFullYear()
  // Date de fin: 31 août à 23:59:59
  let endDate = new Date(currentYear, 7, 31, 23, 59, 59) // Mois 7 = août (0-indexé)
  
  // Si on a dépassé le 31 août cette année, prendre l'année prochaine
  if (now > endDate) {
    endDate = new Date(currentYear + 1, 7, 31, 23, 59, 59)
  }
  
  const timeDiff = endDate.getTime() - now.getTime()
  
  if (timeDiff > 0) {
    countdown.value.days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
    countdown.value.hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    countdown.value.minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
  } else {
    countdown.value.days = 0
    countdown.value.hours = 0
    countdown.value.minutes = 0
  }
}

// Lifecycle
onMounted(() => {
  // Animation d'entrée
  document.querySelector('.animate-fade-in-up')?.classList.add('animate-fade-in-up')

  // Initialiser le décompte
  updateCountdown()

  // Mettre à jour le décompte toutes les minutes
  countdownInterval = setInterval(updateCountdown, 60000)

  // Fermer les dropdowns quand on clique ailleurs
  document.addEventListener("click", (e) => {
    if (!e.target.closest('.dropdown-container')) {
      closeAllDropdowns()
    }
  })
})

onUnmounted(() => {
  // Nettoyer l'interval
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }

  // Nettoyer l'event listener
  document.removeEventListener("click", closeAllDropdowns)
})
</script>

<style scoped>
/* Animation pour les dropdowns */
.dropdown-container {
  position: relative;
}

/* Animation des chevrons */
.rotate-180 {
  transform: rotate(180deg);
}

/* Animations sophistiquées */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

/* Effet de glassmorphism subtil */
.backdrop-blur-sm {
  backdrop-filter: blur(8px);
}

/* Transitions ultra-fluides */
.transition-all {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
  transition: color 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* Effet de dégradé pour les textes */
.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Hover effects sophistiqués */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\:translate-x-1 {
  transform: translateX(0.25rem);
}

.group:hover .group-hover\:bg-b3\/10 {
  background-color: rgba(59, 130, 246, 0.1);
}

/* Ombres élégantes */
.shadow-xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.03);
}

/* Focus states élégants */
.focus\:ring-4:focus {
  ring-width: 4px;
}

.focus\:ring-b3\/20:focus {
  ring-color: rgba(59, 130, 246, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  .text-5xl {
    font-size: 2.5rem;
  }
  
  .text-6xl {
    font-size: 3rem;
  }
  
  .text-7xl {
    font-size: 3.5rem;
  }
}

/* Animation de scale fluide */
.transform.hover\:scale-\[1\.02\]:hover {
  transform: scale(1.02);
}

.transform.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* Effet de blur pour les formes décoratives */
.blur-3xl {
  filter: blur(64px);
}

.blur-2xl {
  filter: blur(40px);
}

/* Grille subtile */
.opacity-\[0\.02\] {
  opacity: 0.02;
}

/* Smooth scroll */
html {
  scroll-behavior: smooth;
}

/* Bordures sophistiquées */
.border-slate-200\/50 {
  border-color: rgba(226, 232, 240, 0.5);
}

/* Backgrounds avec opacité */
.bg-b3\/10 {
  background-color: rgba(59, 130, 246, 0.1);
}

.bg-b3\/20 {
  background-color: rgba(59, 130, 246, 0.2);
}

.border-b3\/20 {
  border-color: rgba(59, 130, 246, 0.2);
}

/* Amélioration des boutons */
.rounded-2xl {
  border-radius: 1rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

/* Espacement harmonieux */
.tracking-wider {
  letter-spacing: 0.05em;
}

/* Séparateurs élégants */
.w-px {
  width: 1px;
}

/* Typographie premium */
.font-black {
  font-weight: 900;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

/* Couleurs harmonieuses */
.text-slate-900 {
  color: rgb(15 23 42);
}

.text-slate-700 {
  color: rgb(51 65 85);
}

.text-slate-600 {
  color: rgb(71 85 105);
}

.text-slate-500 {
  color: rgb(100 116 139);
}

/* Backgrounds subtils */
.bg-slate-50 {
  background-color: rgb(248 250 252);
}

.bg-slate-100 {
  background-color: rgb(241 245 249);
}

/* Bordures harmonieuses */
.border-slate-200 {
  border-color: rgb(226 232 240);
}

.border-slate-300 {
  border-color: rgb(203 213 225);
}

/* États hover sophistiqués */
.hover\:bg-slate-100:hover {
  background-color: rgb(241 245 249);
}

.hover\:border-slate-300:hover {
  border-color: rgb(203 213 225);
}

.hover\:border-b3:hover {
  border-color: var(--color-b3);
}

.hover\:shadow-xl:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(255, 255, 255, 0.05);
}
</style> 