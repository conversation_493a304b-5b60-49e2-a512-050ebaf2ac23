<template>
  <!-- <PERSON><PERSON> des CGV -->
  <div
    v-if="isOpen"
    class="fixed inset-0 z-90 overflow-y-auto bg-gradient-to-br from-b1 via-b2/99 to-b2/95"
    role="dialog"
    aria-modal="true"
    aria-labelledby="cgv-modal-title"
  >
    <div class="flex items-start justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-black opacity-75"></div>
      </div>

      <!-- Conteneur de la modale -->
      <div class="inline-block align-bottom bg-white/5 backdrop-blur-md rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl w-full">
        <!-- Bouton de fermeture accessible -->
        <button
          @click="closeModal"
          class="absolute top-2 sm:top-4 right-2 sm:right-4 text-white bg-b3/30 hover:bg-b3/50 hover:scale-110 p-2 rounded-full transition-all duration-300 z-50 cursor-pointer"
          aria-label="Fermer les CGV"
          title="Fermer les CGV"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        <div class="p-6 sm:p-8">
          <!-- En-tête -->
          <div class="text-center mb-6">
            <h2 id="cgv-modal-title" class="text-2xl font-bold text-white mb-2">Conditions Générales de Vente (CGV)</h2>
            <div class="w-16 h-1 bg-gradient-to-r from-b3 to-b6 mx-auto rounded-full"></div>
            <p class="text-white/70 text-sm mt-2">Dernière mise à jour : 10 juillet 2025</p>
          </div>

          <!-- Contenu des CGV -->
          <div class="text-white/90 space-y-6 text-sm sm:text-base">
            <p>
              Les présentes Conditions Générales de Vente (ci-après "CGV") régissent les relations contractuelles entre :
            </p>

            <div class="bg-white/5 p-4 rounded-lg">
              <p class="font-semibold">The Dev Impact, activité portée juridiquement par la coopérative d'entrepreneurs Coopilote – SCOP SA à capital variable – RCS Belfort 452 207 077.</p>
              <p class="mt-2">Adresse : 13 avenue Léon Blum, 25200 Montbéliard</p>
              <p>E-mail : <EMAIL></p>
              <p class="mt-2">(ci-après "le Prestataire")</p>
              <p class="mt-2">et toute personne physique ou morale (ci-après "le Client") ayant recours aux services proposés par le Prestataire.</p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">1. Objet</h3>
              <p>
                Les présentes CGV ont pour objet de définir les droits et obligations des parties dans le cadre de la vente de prestations de services liées à :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>La création de sites web vitrines statiques hébergés sur des plateformes type Netlify</li>
                <li>La conception de supports marketing digitaux : flyers, cartes de visite, visuels pour réseaux sociaux, etc.</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">1. Objet</h3>
              <p>
                Les présentes CGV ont pour objet de définir les droits et obligations des parties dans le cadre de la vente de prestations de services liées à :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>La création de sites web vitrines statiques hébergés sur des plateformes type Netlify</li>
                <li>La conception de supports marketing digitaux : flyers, cartes de visite, visuels pour réseaux sociaux, etc.</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">2. Acceptation des CGV</h3>
              <p>
                Toute commande passée implique l'adhésion entière et sans réserve du Client aux présentes CGV, qui prévalent sur tout autre document. Le Client déclare en avoir pris connaissance et les accepter sans restriction.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">3. Prestations proposées</h3>
              <p>
                Les prestations incluent, sans s'y limiter :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>L'analyse des besoins du Client</li>
                <li>La réalisation d'une première version de site ou de supports</li>
                <li>Les ajustements successifs sur la base des retours du Client</li>
                <li>L'intégration et développement de sites vitrines statiques</li>
                <li>La livraison de fichiers prêts à l'impression ou à l'usage digital</li>
              </ul>
              <p class="mt-2">
                Chaque prestation est personnalisée selon les besoins exprimés par le Client et validée par devis.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">4. Devis et commande</h3>
              <p>
                Chaque prestation fait l'objet d'un devis personnalisé valable 30 jours.
                La commande est considérée comme ferme et définitive à réception du devis signé ou d'un accord écrit explicite (par e-mail).
              </p>
              <p class="mt-2">
                Un acompte de 50 % est exigé à la commande. Le solde est dû à la livraison, sauf mention contraire au devis. Aucun travail ne débute sans acompte.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">5. Tarifs et paiements</h3>
              <p>
                Les tarifs sont exprimés en euros hors taxes (HT).
                Les paiements s'effectuent par virement bancaire ou tout autre moyen précisé sur le devis.
              </p>
              <p class="mt-2">
                Pénalités de retard : tout retard de paiement entraîne, sans mise en demeure préalable :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Des intérêts de retard au taux légal majoré de 10 points</li>
                <li>Une indemnité forfaitaire de 40 € pour frais de recouvrement</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">6. Délai de réalisation</h3>
              <p>
                Les délais sont donnés à titre indicatif, sauf mention expresse "délai ferme" sur le devis.
                Le Prestataire ne pourra être tenu responsable des retards dus :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>À des retards dans la transmission des éléments par le Client (textes, images, etc.)</li>
                <li>À des modifications de brief en cours de réalisation</li>
                <li>À des cas de force majeure</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">7. Obligations du Client</h3>
              <p>
                Le Client s'engage à fournir dans les délais les éléments nécessaires à la bonne exécution de la prestation (contenus, visuels, accès, etc.). En cas de manquement, le délai initial pourra être prolongé, voire la commande suspendue.
              </p>
              <p class="mt-2">
                Le Client est seul responsable du contenu fourni (textes, images, logos, etc.), notamment au regard des droits d'auteur et de la législation applicable.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">8. Droit de rétractation</h3>
              <p>
                Conformément à l'article L221-28 du Code de la consommation, le droit de rétractation ne s'applique pas aux prestations personnalisées exécutées à la demande du Client.
                Toute commande validée est ferme et définitive.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">9. Propriété intellectuelle</h3>
              <p>
                Les créations (sites, visuels, codes sources, maquettes, etc.) restent la propriété exclusive du Prestataire tant que le solde de la prestation n'est pas réglé en totalité.
              </p>
              <p class="mt-2">
                Après paiement complet, les droits d'usage sont transférés au Client dans les limites définies dans le devis. Le Prestataire se réserve le droit d'afficher les créations à des fins de communication ou de portfolio, sauf refus écrit du Client.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">10. Communication et droit de citation</h3>
              <p>
                Sauf opposition écrite du Client avant le début de la prestation, le Prestataire se réserve le droit :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>D'ajouter une mention discrète en bas de page des sites réalisés, de type : « Site réalisé avec ❤️ par The Dev Impact »</li>
                <li>D'utiliser à des fins de communication (site web, réseaux sociaux, supports commerciaux, portfolio) tout ou partie des réalisations produites (liens, captures, extraits, visuels, vidéos, etc.)</li>
              </ul>
              <p class="mt-2">
                Le Client peut demander la suppression de la mention ou la non-diffusion d'un projet spécifique, par simple demande écrite préalable.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">11. Résiliation</h3>
              <p>
                En cas de résiliation de la commande par le Client sans faute du Prestataire :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>L'acompte reste acquis</li>
                <li>Toute prestation entamée est facturée au prorata</li>
                <li>Les éléments créés ne peuvent être utilisés sans autorisation écrite du Prestataire</li>
              </ul>
              <p class="mt-2">
                En cas de manquement grave du Client (retard de paiement, non-coopération), le Prestataire pourra résilier le contrat de plein droit, sans indemnité, et conserver les sommes déjà perçues.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">12. Responsabilité</h3>
              <p>
                Le Prestataire s'engage à fournir les prestations conformément aux règles de l'art et aux usages de la profession.
              </p>
              <p class="mt-2">
                Sa responsabilité ne pourra être engagée :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Pour des dommages indirects ou immatériels</li>
                <li>Pour des dysfonctionnements liés à des outils ou services tiers (hébergeur, plateforme, outil d'impression, etc.)</li>
                <li>En cas d'usage inapproprié des livrables par le Client</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">13. Loi applicable et juridiction compétente</h3>
              <p>
                Les présentes CGV sont régies par le droit français. En cas de litige, les tribunaux compétents seront ceux du siège de la coopérative Coopilote, sauf disposition légale contraire.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">14. Modifications des CGV</h3>
              <p>
                Le Prestataire se réserve le droit de modifier les présentes CGV à tout moment. Les conditions applicables sont celles en vigueur à la date de validation de la commande.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

defineProps({
  isOpen: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits(['close']);

const closeModal = () => {
  emit('close');
};


</script>

<style scoped>
/* Style pour les liens */
a {
  text-decoration: none;
  transition: all 0.3s ease;
}

/* Style pour les éléments cliquables */
button, 
a, 
[role="button"],
.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}
</style>
