<script setup>
// Données des témoignages clients - textes plus courts et contrôlés
const testimonials = [
  {
    name: "<PERSON>",
    company: "Agence",
    role: "<PERSON><PERSON><PERSON><PERSON>",
    text:
      "Notre site convertit mieux qu'avant. Un design qui impressionne nos clients..",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
  },
  {
    name: "<PERSON>",
    company: "Startup",
    role: "<PERSON><PERSON><PERSON><PERSON>",
    text:
      "Design sur mesure qui correspond parfaitement à notre identité. Excellent travail !",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  },
  {
    name: "<PERSON>",
    company: "Cabinet Conseil",
    role: "Consultante",
    text: "Site livré dans les délais, design élégant et parfaitement responsive.",
    rating: 4,
    avatar: "https://randomuser.me/api/portraits/women/68.jpg",
  },
  {
    name: "<PERSON>",
    company: "Vitrine Artisanale",
    role: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    text: "Ventes augmentées de 40% en 2 mois. Un investissement rentable !",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/men/75.jpg",
  },
  {
    name: "Émilie B.",
    company: "Agence Design",
    role: "Designer",
    text:
      "En tant que designer, j'ai des exigences élevées. The Dev Impact les a dépassées.",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/women/90.jpg",
  },
];
</script>

<template>
  <div class="pt-8 sm:pt-12 bg-white relative z-0">
    <!-- Titre de section élégant -->
    <div class="text-center mb-8 sm:mb-12 px-3">
      <!-- Badge moderne -->
      <div
        class="inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-xs sm:text-sm font-medium mb-6 sm:mb-8 shadow-sm"
      >
        <span class="w-1.5 sm:w-2 h-1.5 sm:h-2 rounded-full bg-b3 mr-1.5 sm:mr-2 text-gray-800"></span>
        Témoignages clients
        <span class="w-1.5 sm:w-2 h-1.5 sm:h-2 rounded-full bg-b6 ml-1.5 sm:ml-2"></span>
      </div>

      <h2 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800">Ce que nos clients disent</h2>
    </div>

    <!-- Carousel de témoignages optimisé avec clone pour éviter les trous -->
    <div class="w-full overflow-hidden" style="overflow-y: hidden">
      <Vue3Marquee class="py-3 sm:py-4 overflow-hidden" :duration="60" pauseOnHover clone>
        <div class="flex gap-3 sm:gap-4 lg:gap-6 px-3 sm:px-4">
          <div
            v-for="(testimonial, index) in testimonials"
            :key="index"
            class="flex-shrink-0 w-[240px] xs:w-[260px] sm:w-64 lg:w-72 bg-white rounded-xl p-3 sm:p-4 lg:p-6 shadow-md border border-gray-100 text-left transition-all duration-300 hover:shadow-lg hover:border-b6/20 flex flex-col h-52 sm:h-56 lg:h-60"
          >
            <!-- En-tête avec avatar et étoiles -->
            <div class="flex justify-between items-start mb-3 sm:mb-4">
              <div class="flex items-center">
                <img
                  :src="testimonial.avatar"
                  :alt="testimonial.name"
                  loading="lazy"
                  width="32"
                  height="32"
                  class="w-8 sm:w-10 h-8 sm:h-10 rounded-full object-cover mr-2 sm:mr-3 border-2 border-white shadow-sm"
                />
                <div>
                  <p class="font-semibold text-gray-800 text-xs sm:text-sm">
                    {{ testimonial.name }}
                  </p>
                  <p class="text-xs text-gray-500">{{ testimonial.company }}</p>
                </div>
              </div>
              <div class="flex">
                <div v-for="n in testimonial.rating" :key="'star-' + n" class="w-3 sm:w-4 h-3 sm:h-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="#F59E0B"
                    class="w-3 sm:w-4 h-3 sm:h-4"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Texte du témoignage avec hauteur fixe -->
            <div class="bg-gray-50 rounded-lg p-3 sm:p-4 mb-2 sm:mb-3 flex-grow">
              <div class="h-12 sm:h-15 overflow-y-auto">
                <p class="text-gray-600 text-xs sm:text-sm whitespace-normal">
                  "{{ testimonial.text }}"
                </p>
              </div>
            </div>

            <!-- Badge de rôle -->
            <div class="flex justify-end">
              <span class="inline-block px-2 sm:px-3 py-0.5 sm:py-1 bg-b3/10 text-b3 text-xs rounded-full">
                {{ testimonial.role }}
              </span>
            </div>
          </div>
        </div>
      </Vue3Marquee>
    </div>

    <!-- Indicateurs et logo -->
    <div class="mt-6 sm:mt-8 lg:mt-10 flex flex-col items-center pb-4 sm:pb-6">
      <div class="flex justify-center space-x-1.5 sm:space-x-2 mb-4 sm:mb-6">
        <div class="w-6 sm:w-8 h-1 bg-b3 rounded-full"></div>
        <div class="w-1.5 sm:w-2 h-1 bg-b4/50 rounded-full"></div>
        <div class="w-1.5 sm:w-2 h-1 bg-b6/50 rounded-full"></div>
      </div>
      <img
        src="/src/assets/logo-TheDI-removeBG.png"
        alt="Logo TheDI"
        class="h-6 sm:h-8 w-auto opacity-70"
      />
    </div>
  </div>
</template>

<style scoped>
/* Styles pour assurer que le texte s'affiche correctement */
.whitespace-normal {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

/* Empêcher le scroll vertical dans le carousel */
:deep(.vue3-marquee) {
  overflow-y: hidden !important;
}

:deep(.vue3-marquee-content) {
  overflow-y: hidden !important;
}
</style>
