<template>
  <div class="restaurant-card group cursor-pointer transform transition-all duration-500 hover:shadow-2xl" @click="navigateToProduct">
<!-- Carousel d'images avec Vue Marquee -->
<div class="relative overflow-hidden rounded-t-3xl h-60 lg:h-64">
  <Vue3Marquee 
    class="h-full" 
    :duration="50" 
    pauseOnHover 
    direction="normal"
    :clone="true"
  >
    <div class="flex h-full gap-1">
      <img 
        v-for="(image, index) in product.images" 
        :key="index"
        :src="image" 
        :alt="`${product.name} - Image ${index + 1}`"
        class="h-full object-cover flex-shrink-0 transition-all duration-700"
        style="width: auto; min-width: 400px;"
        @error="handleImageError"
      />
    </div>
  </Vue3Marquee>
  

  
  <!-- Badge featured premium -->
  <div v-if="featured || product.featured" 
       class="absolute top-6 left-6 bg-gradient-to-r from-amber-400 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg z-20 flex items-center">
    <i class="fas fa-crown mr-2 text-amber-200"></i>
    Premium
  </div>
  

  

</div>

<!-- Contenu de la carte -->
<div class="p-5 lg:p-6 bg-white rounded-b-3xl">
  <!-- Header avec catégorie -->
  <div class="flex items-start justify-between mb-3">
    <div class="flex items-center gap-3">
      <div class="w-3 h-3 bg-gradient-to-r from-b3 to-b6 rounded-full"></div>
      <span class="text-b3 font-semibold text-sm uppercase tracking-wider">
        {{ product.category }}
      </span>
    </div>
    
    <!-- Badges alignés à droite -->
    <div class="flex items-center gap-2 flex-wrap justify-end">
      <!-- Badge exemplaire unique -->
      <div class="bg-gradient-to-r from-emerald-400 to-emerald-600 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center">
        <i class="fas fa-gem mr-1 text-emerald-200"></i>
        <span class="hidden sm:inline">Exemplaire unique</span>
        <span class="sm:hidden">Unique</span>
      </div>
      <!-- Indicateur discret des options -->
      <div class="flex items-center text-xs text-slate-500">
        <i class="fas fa-plus-circle text-purple-500 mr-1"></i>
        <span>+6</span>
      </div>
    </div>
  </div>
  
  <!-- Titre principal -->
  <h3 class="text-2xl lg:text-3xl font-black text-slate-900 mb-2 group-hover:text-b3 transition-colors leading-tight">
    {{ product.name }}
  </h3>
  
  <!-- Sous-titre avec meilleure lisibilité -->
  <div class="mb-4">
    <p class="text-slate-600 text-sm lg:text-base leading-relaxed line-clamp-3">
      {{ product.subtitle }}
    </p>
  </div>
  
  <!-- Features essentielles -->
  <div class="space-y-2 mb-4">
    <div v-for="feature in product.features.slice(0, 3)" 
         :key="feature"
         class="flex items-center space-x-2 text-sm text-slate-600">
      <i class="fas fa-check text-green-500 text-xs flex-shrink-0"></i>
      <span class="truncate">{{ feature }}</span>
    </div>
  </div>
  
  <!-- Technologies simplifiées -->
  <div class="flex flex-wrap gap-2 mb-4">
    <span v-for="tech in product.technologies.slice(0, 3)" 
          :key="tech"
          class="px-3 py-1 bg-slate-100 text-slate-600 text-xs rounded-full">
      {{ tech }}
    </span>
    <span v-if="product.technologies.length > 3" 
          class="px-3 py-1 bg-slate-100 text-slate-500 text-xs rounded-full">
      +{{ product.technologies.length - 3 }}
    </span>
  </div>
  
  <!-- Informations pratiques -->
  <div class="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-xl p-3 mb-4">
    <div class="flex items-center text-sm text-blue-800">
      <i class="fas fa-shipping-fast text-blue-600 mr-2"></i>
      <span class="font-medium">Livraison</span>
    </div>
    <span class="text-sm font-bold text-blue-900">5-10 jours</span>
  </div>
  
  <!-- Section prix épurée -->
  <div class="bg-white border-2 border-slate-200 rounded-2xl p-4 mb-4 relative">
    <!-- Badge de promotion -->
    <div v-if="product.promotion" class="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">
      {{ product.promotion.label }}
    </div>
    
    <!-- Prix principal -->
    <div class="text-center">
      <div class="flex items-center justify-center space-x-3 mb-2">
        <span class="text-3xl font-black text-slate-900">{{ formatPrice(product.price) }}</span>
        <span v-if="product.originalPrice && product.originalPrice > product.price" 
              class="text-lg text-slate-400 line-through">
          {{ formatPrice(product.originalPrice) }}
        </span>
      </div>
      
      <p class="text-sm text-slate-500 mb-3">
        {{ calculatePriceWithVAT(product.price) }}€ TTC • TVA incluse
      </p>
      
      <!-- Date de fin d'offre (si promotion) -->
      <div v-if="product.promotion" class="inline-block">
        <p class="text-xs text-red-600 font-semibold bg-red-50 px-3 py-1 rounded-full">
          <i class="fas fa-clock text-red-500 mr-1"></i>
          Jusqu'au {{ formatDate(product.promotion.endDate) }}
        </p>
      </div>
    </div>
  </div>
  
  <!-- Actions avec design moderne -->
  <div class="space-y-3">
    <button @click.stop="addToCart" 
            :disabled="isAddingToCart"
            class="w-full bg-gradient-to-r from-b3 to-b6 hover:from-b4 hover:to-b3 text-white font-bold py-3 px-6 rounded-2xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-lg"
            :class="{ 'bg-green-600': isInCart }">
      <i v-if="!isAddingToCart" :class="isInCart ? 'fas fa-check text-white' : 'fas fa-shopping-cart text-white'" class="mr-3 text-xl"></i>
      <div v-if="isAddingToCart" class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
      {{ isAddingToCart ? 'Traitement...' : isInCart ? 'Ajouté au panier' : 'Acheter maintenant' }}
    </button>
    
    <div class="grid grid-cols-2 gap-3">
      <button @click.stop="navigateToProduct" class="bg-white border-2 border-slate-200 hover:border-b3 text-slate-700 hover:text-b3 font-semibold py-2 px-4 rounded-xl transition-all duration-300 flex items-center justify-center">
        <i class="fas fa-info-circle mr-2 text-blue-500"></i>
        Détails
      </button>
                 <button @click.stop="requestDemo" 
               class="bg-white border-2 border-slate-200 hover:border-b3 text-slate-700 hover:text-b3 font-semibold py-2 px-4 rounded-xl transition-all duration-300 flex items-center justify-center">
         <i class="fas fa-vial mr-2 text-green-500"></i>
         Test gratuit
       </button>
    </div>
  </div>
</div>

    <!-- Modale de demande de démo -->
    <DemoModal 
      :isOpen="showDemoModal" 
      :product="product" 
      @close="closeDemoModal" 
    />
</div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { formatPrice, calculateDiscount as calcDiscount } from '../../services/stripe.js'
import { useCartStore } from '../../store/cart.js'
import DemoModal from './DemoModal.vue'

// Props
const props = defineProps({
product: {
type: Object,
required: true
},
featured: {
type: Boolean,
default: false
}
})

// Composables
const router = useRouter()
const cartStore = useCartStore()

// État réactif
const isInWishlist = ref(false)
const imageLoaded = ref(true)
const isAddingToCart = ref(false)
const showDemoModal = ref(false)

// Computed
const isInCart = computed(() => {
return cartStore.isInCart(props.product.id)
})

// Méthodes
function navigateToProduct() {
router.push(`/product/${props.product.id}`)
}

function addToCart() {
if (isAddingToCart.value) return

isAddingToCart.value = true

try {
const success = cartStore.addItem(props.product)

if (success) {
  // Feedback visuel supplémentaire
  const button = event.target.closest('button')
  if (button) {
    button.classList.add('animate-pulse')
    setTimeout(() => {
      button.classList.remove('animate-pulse')
    }, 1000)
  }
}
} catch (error) {
console.error('Erreur lors de l\'ajout au panier:', error)
} finally {
isAddingToCart.value = false
}
}

function toggleWishlist() {
isInWishlist.value = !isInWishlist.value

// Sauvegarder dans localStorage
try {
const wishlist = JSON.parse(localStorage.getItem('thedevimpact-wishlist') || '[]')

if (isInWishlist.value) {
  if (!wishlist.includes(props.product.id)) {
    wishlist.push(props.product.id)
    window.dispatchEvent(new CustomEvent('show-toast', {
      detail: {
        type: 'success',
        title: 'Ajouté aux favoris',
        message: `${props.product.name} a été ajouté à vos favoris`
      }
    }))
  }
} else {
  const index = wishlist.indexOf(props.product.id)
  if (index > -1) {
    wishlist.splice(index, 1)
    window.dispatchEvent(new CustomEvent('show-toast', {
      detail: {
        type: 'info',
        title: 'Retiré des favoris',
        message: `${props.product.name} a été retiré de vos favoris`
      }
    }))
  }
}

localStorage.setItem('thedevimpact-wishlist', JSON.stringify(wishlist))
} catch (error) {
console.error('Erreur lors de la gestion des favoris:', error)
}
}

function quickView() {
// Pour l'instant, on redirige vers la page produit
// TODO: Ouvrir modal avec aperçu rapide
navigateToProduct()
}

function requestDemo() {
showDemoModal.value = true
}

function closeDemoModal() {
showDemoModal.value = false
}

function handleImageError(event) {
const img = event.target

// Éviter la boucle infinie en vérifiant si on est déjà sur l'image de fallback
if (img.src.includes('via.placeholder.com')) {
return
}

imageLoaded.value = false
// Utiliser une image de fallback
img.src = 'https://via.placeholder.com/400x250/85BDBF/FFFFFF?text=TheDevImpact'
}

function calculateDiscount() {
if (!props.product.originalPrice || props.product.originalPrice <= props.product.price) {
return 0
}
return calcDiscount(props.product.originalPrice, props.product.price)
}

function calculatePriceWithVAT(price) {
// Calcul du prix TTC avec TVA de 20% - garder les centimes pour la précision
return (price * 1.20).toFixed(2)
}

function formatDate(dateString) {
const date = new Date(dateString)
return date.toLocaleDateString('fr-FR', {
day: 'numeric',
month: 'long',
year: 'numeric'
})
}

// Lifecycle
onMounted(() => {
// Charger l'état des favoris
try {
const wishlist = JSON.parse(localStorage.getItem('thedevimpact-wishlist') || '[]')
isInWishlist.value = wishlist.includes(props.product.id)
} catch (error) {
console.error('Erreur lors du chargement des favoris:', error)
}
})
</script>

<style scoped>
/* Limitation du nombre de lignes pour la description */
.line-clamp-3 {
display: -webkit-box;
-webkit-line-clamp: 3;
-webkit-box-orient: vertical;
overflow: hidden;
}


/* Styles pour Vue3Marquee dans les cartes produits */
:deep(.vue3-marquee) {
overflow: hidden !important;
height: 100% !important;
width: 100% !important;
}

:deep(.vue3-marquee-content) {
display: flex !important;
height: 100% !important;
align-items: center !important;
width: 100% !important;
}

/* Images du carousel avec proportions naturelles */
.restaurant-card img {
width: auto !important;
min-width: 400px !important;
max-width: none !important;
object-fit: cover !important;
}

/* Effet de masque pour le carousel - très subtil */
.restaurant-card .vue3-marquee {
mask-image: linear-gradient(
to right,
transparent 0%,
black 1%,
black 99%,
transparent 100%
);
}

/* Pause automatique au survol de la carte */
.restaurant-card:hover .vue3-marquee {
animation-play-state: paused !important;
}

/* Transition fluide pour les images du carousel */
.restaurant-card img {
transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Amélioration des badges avec z-index */
.restaurant-card .absolute {
z-index: 10;
}

/* Design premium pour la carte restaurant */
.restaurant-card {
background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
border: 1px solid rgba(226, 232, 240, 0.8);
border-radius: 1.5rem;
box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
overflow: hidden;
}

.restaurant-card:hover {
box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
border-color: rgba(59, 130, 246, 0.3);
}

/* Responsive design optimisé */
@media (max-width: 640px) {
.restaurant-card {
margin: 0 auto;
max-width: 100%;
}

.restaurant-card .h-72 {
height: 16rem;
}

.restaurant-card .text-2xl {
font-size: 1.5rem;
}

.restaurant-card .text-3xl {
font-size: 2rem;
}

.restaurant-card .p-6 {
padding: 1.25rem;
}

.restaurant-card .grid-cols-2 {
grid-template-columns: 1fr;
gap: 0.5rem;
}

.restaurant-card .flex-col {
flex-direction: column;
}

.restaurant-card .sm\:flex-row {
flex-direction: column;
}
}

@media (min-width: 768px) {
.restaurant-card {
max-width: 28rem;
}
}

@media (min-width: 1024px) {
.restaurant-card {
max-width: 32rem;
}

.restaurant-card .lg\:h-80 {
height: 20rem;
}

.restaurant-card .lg\:p-8 {
padding: 2rem;
}

.restaurant-card .lg\:text-3xl {
font-size: 1.875rem;
}

.restaurant-card .lg\:text-base {
font-size: 1rem;
}
}

/* Animations fluides */
.restaurant-card * {
transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Effet glassmorphism sur les badges */
.restaurant-card .backdrop-blur-sm {
backdrop-filter: blur(8px);
-webkit-backdrop-filter: blur(8px);
}

/* Amélioration des boutons */
.restaurant-card button:hover {
transform: translateY(-1px);
}

.restaurant-card button:active {
transform: translateY(0);
}

/* Grid responsive pour les features */
@media (max-width: 640px) {
.restaurant-card .grid-cols-2 {
grid-template-columns: 1fr;
}
}

/* Truncate pour le responsive */
.truncate {
overflow: hidden;
text-overflow: ellipsis;
white-space: nowrap;
}




</style> 