<template>
  <!-- Container principal avec portail pour éviter les conflits -->
  <Teleport to="body">
    <!-- Overlay backdrop -->
    <Transition name="fade">
      <div v-if="isOpen" 
           class="cart-overlay fixed inset-0 bg-black/20 z-[9998]"
           @click="$emit('close')">
      </div>
    </Transition>
    
    <!-- Sidebar -->
    <Transition name="slide">
      <div v-if="isOpen"
           class="cart-sidebar fixed top-0 right-0 h-full w-96 bg-white shadow-2xl z-[9999] flex flex-col">
        
        <!-- Header fixe -->
        <div class="cart-header flex items-center justify-between p-6 border-b border-gray-200 bg-white flex-shrink-0">
          <h2 class="text-xl font-bold text-b1">Panier d'Achats</h2>
          <button @click="$emit('close')" 
                  class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors">
            <i class="fas fa-times text-gray-600"></i>
          </button>
        </div>
        
        <!-- Contenu scrollable -->
        <div class="cart-content flex-1 overflow-y-auto">
          <div class="p-6">
            <!-- Panier vide -->
            <div v-if="cartItems.length === 0" class="text-center py-12">
              <div class="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <i class="fas fa-shopping-cart text-2xl text-gray-400"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Votre panier est vide</h3>
              <p class="text-gray-500 mb-6">Découvrez notre collection exceptionnelle</p>
              <button @click="goToCollection" 
                      class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-b3 to-b6 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300">
                <i class="fas fa-eye mr-2"></i>
                Voir la Collection
              </button>
            </div>
            
            <!-- Articles du panier -->
            <div v-else class="space-y-4">
              <div v-for="item in cartItems" 
                   :key="item.id" 
                   class="bg-gray-50 rounded-lg p-4 flex items-start space-x-4 transition-all duration-300 hover:shadow-md">
                
                <!-- Image du produit -->
                <div class="flex-shrink-0">
                  <img v-if="item.image" 
                       :src="item.image" 
                       :alt="item.name"
                       class="w-16 h-16 object-cover rounded-lg shadow-sm" />
                  <div v-else 
                       class="w-16 h-16 bg-gradient-to-br from-b3 to-b6 rounded-lg shadow-sm flex items-center justify-center">
                    <i class="fas fa-puzzle-piece text-white text-xl"></i>
                  </div>
                </div>
                
                <!-- Détails -->
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-b1 truncate">{{ item.name }}</h4>
                  <p class="text-sm text-gray-500 mb-1">{{ item.category }}</p>
                  
                  <!-- Prix avec promotion -->
                  <div class="mb-2">
                    <div v-if="item.promotion" class="flex items-center gap-2 mb-1">
                      <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full font-medium">
                        {{ item.promotion.label }}
                      </span>
                    </div>
                    <div class="flex items-center gap-2">
                      <span v-if="item.originalPrice" class="text-sm text-gray-400 line-through">
                        {{ formatPrice(item.originalPrice) }}
                      </span>
                      <span class="text-lg font-bold text-b6">{{ formatPrice(item.price) }}</span>
                    </div>
                  </div>
                  
                  <!-- Contrôles de quantité -->
                  <div class="flex items-center space-x-2">
                    <button @click="decreaseQuantity(item.id)" 
                            class="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
                            :disabled="item.quantity <= 1">
                      <i class="fas fa-minus text-xs text-gray-700"></i>
                    </button>
                    <span class="px-3 py-1 bg-white rounded-md border text-sm font-bold text-gray-900 min-w-[2.5rem] text-center">
                      {{ item.quantity }}
                    </span>
                    <button @click="increaseQuantity(item.id)" 
                            class="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors">
                      <i class="fas fa-plus text-xs text-gray-700"></i>
                    </button>
                  </div>
                </div>
                
                <!-- Actions -->
                <div class="flex flex-col items-end space-y-2">
                  <button @click="confirmRemoveItem(item)" 
                          class="w-8 h-8 rounded-full bg-red-100 hover:bg-red-200 flex items-center justify-center transition-colors group"
                          title="Supprimer cet article">
                    <i class="fas fa-trash text-red-600 text-sm group-hover:scale-110 transition-transform"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Footer fixe avec total et checkout -->
        <div v-if="cartItems.length > 0" 
             class="cart-footer border-t border-gray-200 p-6 space-y-4 bg-white flex-shrink-0">
          <!-- Résumé -->
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Sous-total ({{ itemsCount }} article{{ itemsCount > 1 ? 's' : '' }})</span>
              <span class="font-bold text-gray-900">{{ formatPrice(subtotal) }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">TVA (20%)</span>
              <span class="font-bold text-gray-900">{{ formatPrice(tax) }}</span>
            </div>
            <div class="border-t pt-2 flex justify-between font-bold text-lg">
              <span class="text-b1">Total</span>
              <span class="text-b6 font-black">{{ formatPrice(total) }}</span>
            </div>
          </div>
          
          <!-- Actions -->
          <div class="space-y-3">
            <button @click="proceedToCheckout" 
                    :disabled="isProcessing"
                    class="w-full bg-gradient-to-r from-b3 to-b6 text-white font-medium py-3 px-4 rounded-lg hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
              <i v-if="!isProcessing" class="fas fa-credit-card mr-2"></i>
              <div v-if="isProcessing" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {{ isProcessing ? 'Traitement...' : 'Procéder au Paiement' }}
            </button>
            
            <div class="flex space-x-2">
              <button @click="$emit('close')" 
                      class="flex-1 border-2 border-gray-300 text-gray-700 font-medium py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors">
                Continuer les Achats
              </button>
              <button @click="confirmClearCart" 
                      class="px-4 py-2 border-2 border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors"
                      title="Vider le panier">
                <i class="fas fa-trash-alt"></i>
              </button>
            </div>
          </div>
          
          <!-- Sécurité -->
          <div class="text-center text-xs text-gray-500 flex items-center justify-center">
            <i class="fas fa-lock mr-1"></i>
            Paiement sécurisé avec Stripe
          </div>
        </div>
      </div>
    </Transition>

    <!-- Modal de confirmation de suppression -->
    <Transition name="modal">
      <div v-if="showConfirmModal" 
           class="fixed inset-0 bg-black/70 z-[10000] flex items-center justify-center p-4">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full shadow-2xl">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Confirmer la suppression</h3>
          <p class="text-gray-600 mb-4">
            {{ confirmAction === 'removeItem' ? `Êtes-vous sûr de vouloir supprimer "${itemToRemove?.name}" de votre panier ?` : 'Êtes-vous sûr de vouloir vider complètement votre panier ?' }}
          </p>
          <div class="flex space-x-3">
            <button @click="cancelConfirm" 
                    class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
              Annuler
            </button>
            <button @click="executeConfirm" 
                    class="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
              Supprimer
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { formatPrice } from '../../services/stripe.js'
import { useCartStore } from '../../store/cart.js'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close'])

// Composables
const router = useRouter()
const cartStore = useCartStore()

// État local
const isProcessing = ref(false)
const showConfirmModal = ref(false)
const confirmAction = ref('')
const itemToRemove = ref(null)

// Computed
const cartItems = computed(() => cartStore.items)
const subtotal = computed(() => cartStore.subtotal)
const tax = computed(() => cartStore.tax)
const total = computed(() => cartStore.total)
const itemsCount = computed(() => cartStore.itemsCount)

// Watcher pour gérer le scroll du body
watch(() => props.isOpen, async (newValue) => {
  await nextTick()
  
  if (newValue) {
    // Empêcher le scroll de la page principale
    document.body.style.overflow = 'hidden'
    document.body.style.paddingRight = '0px' // Éviter le décalage
  } else {
    // Restaurer le scroll
    document.body.style.overflow = ''
    document.body.style.paddingRight = ''
    
    // Fermer aussi la modal de confirmation si ouverte
    if (showConfirmModal.value) {
      showConfirmModal.value = false
    }
  }
})

// Méthodes de gestion des quantités
function increaseQuantity(itemId) {
  const item = cartItems.value.find(item => item.id === itemId)
  if (item) {
    cartStore.updateQuantity(itemId, item.quantity + 1)
  }
}

function decreaseQuantity(itemId) {
  const item = cartItems.value.find(item => item.id === itemId)
  if (item && item.quantity > 1) {
    cartStore.updateQuantity(itemId, item.quantity - 1)
  }
}

// Méthodes de suppression avec confirmation
function confirmRemoveItem(item) {
  itemToRemove.value = item
  confirmAction.value = 'removeItem'
  showConfirmModal.value = true
}

function confirmClearCart() {
  confirmAction.value = 'clearCart'
  showConfirmModal.value = true
}

function cancelConfirm() {
  showConfirmModal.value = false
  itemToRemove.value = null
  confirmAction.value = ''
}

function executeConfirm() {
  if (confirmAction.value === 'removeItem' && itemToRemove.value) {
    cartStore.removeItem(itemToRemove.value.id)
    showToast('Article supprimé', `${itemToRemove.value.name} a été supprimé du panier`, 'info')
  } else if (confirmAction.value === 'clearCart') {
    cartStore.clearCart()
    showToast('Panier vidé', 'Tous les articles ont été supprimés du panier', 'info')
  }
  cancelConfirm()
}

// Méthode pour afficher les notifications
function showToast(title, message, type = 'success') {
  window.dispatchEvent(new CustomEvent('show-toast', {
    detail: { type, title, message }
  }))
}

// Navigation
function goToCollection() {
  emit('close')
  
  // Si on est sur la page d'accueil, scroll vers la section boutique
  if (router.currentRoute.value.path === '/boutique') {
    setTimeout(() => {
      // Essayer plusieurs sélecteurs possibles
      const boutiqueSection = document.querySelector('#shop') || 
                             document.querySelector('#boutique') || 
                             document.querySelector('[id*="shop"]') ||
                             document.querySelector('.container:has(.grid)')
      
      if (boutiqueSection) {
        console.log('Section trouvée, scroll en cours...', boutiqueSection)
        boutiqueSection.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      } else {
        console.log('Section non trouvée, navigation vers /boutique')
        router.push('/boutique')
      }
    }, 100) // Délai réduit
  } else {
    // Sinon, naviguer vers la page boutique
    router.push('/boutique')
  }
}

// Checkout avec Stripe
async function proceedToCheckout() {
  if (cartStore.isEmpty || isProcessing.value) return
  
  isProcessing.value = true
  
  try {
    // Préparer les données pour Stripe
    const lineItems = cartItems.value.map(item => {
      // Valider l'URL de l'image - doit être une URL absolue pour Stripe
      const isValidImageUrl = item.image && (item.image.startsWith('http://') || item.image.startsWith('https://'))
      
      return {
        price_data: {
          currency: 'eur',
          product_data: {
            name: item.name,
            description: item.category,
            // Seulement inclure les images avec des URLs valides
            ...(isValidImageUrl && { images: [item.image] })
          },
          unit_amount: Math.round(item.price * 100) // Stripe utilise les centimes
        },
        quantity: item.quantity
      }
    })

    // Appeler la fonction Netlify pour créer la session Stripe
    const response = await fetch('/.netlify/functions/create-checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        line_items: lineItems,
        success_url: `${window.location.origin}/success`,
        cancel_url: `${window.location.origin}/boutique`
      })
    })

    const { url, error } = await response.json()

    if (error) {
      throw new Error(error)
    }

    // Rediriger vers Stripe Checkout
    window.location.href = url
    
  } catch (error) {
    console.error('Erreur lors du checkout:', error)
    showToast('Erreur', 'Une erreur est survenue lors du paiement. Veuillez réessayer.', 'error')
  } finally {
    isProcessing.value = false
  }
}
</script>

<style scoped>
/* Styles pour le panier */
.cart-sidebar {
  max-width: 24rem; /* 384px */
}

/* Responsive */
@media (max-width: 640px) {
  .cart-sidebar {
    width: 100vw;
    max-width: 100vw;
  }
}

/* Transitions */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: transform 0.3s ease;
}
.slide-enter-from, .slide-leave-to {
  transform: translateX(100%);
}

.modal-enter-active, .modal-leave-active {
  transition: opacity 0.3s ease;
}
.modal-enter-from, .modal-leave-to {
  opacity: 0;
}

.modal-enter-active .bg-white, .modal-leave-active .bg-white {
  transition: transform 0.3s ease;
}
.modal-enter-from .bg-white, .modal-leave-to .bg-white {
  transform: scale(0.95);
}

/* Amélioration du scroll */
.cart-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.cart-content::-webkit-scrollbar {
  width: 6px;
}

.cart-content::-webkit-scrollbar-track {
  background: transparent;
}

.cart-content::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.cart-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Éviter les problèmes de z-index */
.cart-overlay {
  backdrop-filter: blur(2px);
}

/* Animation pour les boutons disabled */
button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Amélioration des contrôles de quantité */
.quantity-controls button:disabled {
  opacity: 0.3;
}
</style> 