<template>
  <footer class="bg-gradient-to-br from-b1 to-b2 text-white relative overflow-hidden" role="contentinfo" aria-label="Informations de contact et liens utiles">
    <!-- Motif de points -->
    <div
      class="absolute inset-0 opacity-5"
      style="
        background-size: 30px 30px;
        background-image: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.4) 1px,
          transparent 1px
        );
      "
    ></div>

    <!-- Formes décoratives avec effet parallaxe -->
    <div
      class="absolute top-20 left-10 w-64 h-64 bg-b3/5 rounded-full filter blur-3xl parallax-element"
    ></div>
    <div
      class="absolute bottom-40 right-10 w-80 h-80 bg-b6/5 rounded-full filter blur-3xl parallax-element"
    ></div>
    <div
      class="absolute top-40 right-1/4 w-40 h-40 bg-b2/5 rounded-full filter blur-3xl parallax-element"
    ></div>

    <!-- Contenu principal -->
    <div class="max-w-7xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8 relative z-10 pt-32 pb-16">
      <!-- Section supérieure avec logo et coordonnées -->
      <div
        class="grid grid-cols-1 lg:grid-cols-5 gap-8 xs:gap-12 mb-12 xs:mb-16"
      >
        <!-- Logo et description -->
        <div
          class="lg:col-span-2 flex flex-col items-center text-center lg:items-start lg:text-left"
        >
          <div class="mb-6 relative group">
            <div
              class="absolute inset-0 bg-gradient-to-r from-b3/30 to-b6/30 rounded-full blur-md group-hover:blur-lg transition-all duration-500"
            ></div>
            <img
              class="h-12 xs:h-16 w-auto relative z-10 object-contain"
              src="/src/assets/logo-TheDI-removeBG.png"
              alt="TheDevImpact logo"
              width="64"
              height="64"
              loading="lazy"
            />
          </div>
          <h3 class="text-xl xs:text-2xl font-bold text-white mb-3 xs:mb-4">
            The Dev Impact
          </h3>
          <p class="text-white/70 max-w-md text-sm xs:text-base">
            Nous créons des sites web exceptionnels, pensés comme des œuvres d'art digitales. 
            Chaque projet est unique et conçu pour marquer les esprits.
          </p>
        </div>

        <!-- Coordonnées et contact -->
        <div class="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Coordonnées -->
          <div class="flex flex-col items-center text-center md:items-start md:text-left">
            <h4 class="text-white text-lg font-semibold mb-4 flex items-center justify-center md:justify-start">
              <MapPinIcon class="w-5 h-5 mr-2 text-b6" />
              Coordonnées
            </h4>
            <ul class="space-y-3 text-white/70">
              <li class="flex items-start justify-center md:justify-start">
                <GlobeAltIcon class="w-5 h-5 mr-2 text-b3 flex-shrink-0 mt-0.5" />
                <a
                  href="https://thedevimpact.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-white/70 hover:text-white transition-colors duration-300 cursor-pointer"
                >
                  thedevimpact.com
                </a>
              </li>
              <li class="flex items-start justify-center md:justify-start footer-email">
                <EnvelopeIcon class="w-5 h-5 mr-2 text-b3 flex-shrink-0 mt-0.5 footer-email" />
                <a
                  href="mailto:<EMAIL>?subject=Demande%20d'information%20-%20The%20Dev%20Impact&body=Bonjour,%0A%0AJe%20vous%20contacte%20suite%20à%20ma%20visite%20sur%20votre%20site%20web.%20Je%20souhaiterais%20obtenir%20plus%20d'informations%20concernant%20vos%20services.%0A%0AMerci%20d'avance%20pour%20votre%20réponse.%0A%0ACordialement,"
                  class="text-white/70 hover:text-white transition-colors duration-300 cursor-pointer footer-email"
                  aria-label="Envoyer un email à <EMAIL>"
                >
                  <EMAIL>
                </a>
              </li>

              <li class="flex items-start justify-center md:justify-start footer-telephone">
                <PhoneIcon class="w-5 h-5 mr-2 text-b3 flex-shrink-0 mt-0.5 footer-telephone" />
                <a
                  href="tel:+33601273775"
                  class="text-white/70 hover:text-white transition-colors duration-300 cursor-pointer footer-telephone"
                  aria-label="Appeler le +33 6 01 27 37 75"
                >
                  (+33) 06 01 27 37 75
                </a>
              </li>
            </ul>
          </div>

          <!-- Services -->
          <div class="flex flex-col items-center text-center md:items-start md:text-left">
            <h4 class="text-white text-lg font-semibold mb-4 flex items-center justify-center md:justify-start">
              <RocketLaunchIcon class="w-5 h-5 mr-2 text-b6" />
              Services
            </h4>
            <ul class="space-y-2">
              <li>
                <a
                  href="#offres"
                  class="text-white/70 hover:text-white transition-colors duration-300 flex items-center justify-center md:justify-start"
                >
                  <CodeBracketIcon class="w-4 h-4 mr-2 text-b3" />
                  <span>Site vitrine professionnel</span>
                </a>
              </li>
              <li>
                <a
                  href="#offres"
                  class="text-white/70 hover:text-white transition-colors duration-300 flex items-center justify-center md:justify-start"
                >
                  <PaintBrushIcon class="w-4 h-4 mr-2 text-b3" />
                  <span>Visuels personnalisés</span>
                </a>
              </li>
              <li>
                <a
                  href="#offres"
                  class="text-white/70 hover:text-white transition-colors duration-300 flex items-center justify-center md:justify-start"
                >
                  <ShieldCheckIcon class="w-4 h-4 mr-2 text-b3" />
                  <span>Maintenance & Support</span>
                </a>
              </li>
            </ul>
          </div>

                     <!-- Navigation -->
           <div class="flex flex-col items-center text-center md:items-start md:text-left">
             <h4 class="text-white text-lg font-semibold mb-4 flex items-center justify-center md:justify-start">
               <ArrowUpIcon class="w-5 h-5 mr-2 text-b6" />
               Navigation
             </h4>
            <ul class="space-y-2">
              <li class="flex justify-center md:justify-start footer-accueil">
                <a
                  href="/"
                  class="text-white/70 hover:text-white transition-colors duration-300 footer-accueil"
                  >Accueil</a
                >
              </li>
              <li class="flex justify-center md:justify-start footer-formules">
                <a
                  href="/formules"
                  class="text-white/70 hover:text-white transition-colors duration-300 footer-formules"
                  >Création sur-mesure</a
                >
              </li>
              <li class="flex justify-center md:justify-start footer-boutique">
                <a
                  href="/boutique"
                  class="text-white/70 hover:text-white transition-colors duration-300 footer-boutique"
                  >La Collection</a
                >
              </li>
              <li class="flex justify-center md:justify-start footer-contact">
                <button
                  @click="scrollToBottom"
                  class="text-white/70 hover:text-white transition-colors duration-300 footer-contact cursor-pointer"
                  >Contact</button
                >
              </li>
            </ul>
          </div>
        </div>
      </div>



    </div>
    
    <!-- Section inférieure -->
    <div class="border-t border-white/10">
      <div class="container-custom py-6">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <!-- Copyright -->
          <div class="text-gray-400 text-sm">
            © {{ currentYear }} TheDevImpact. Tous droits réservés.
          </div>
          
          <!-- Méthodes de paiement -->
          <div class="flex items-center space-x-6">
            <span class="text-gray-400 text-sm">Paiement sécurisé avec</span>
            <div class="flex items-center space-x-3">
              <div class="bg-white px-2 py-1 rounded">
                <img src="https://js.stripe.com/v3/fingerprinted/img/visa-365725566f9578a9589553aa9296d178.svg" 
                     alt="Visa" class="h-4">
              </div>
              <div class="bg-white px-2 py-1 rounded">
                <img src="https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg" 
                     alt="Mastercard" class="h-4">
              </div>
              <div class="text-b3 font-bold text-lg">stripe</div>
            </div>
          </div>
          
          <!-- Liens légaux -->
          <div class="flex flex-col md:flex-row space-y-1 md:space-y-0 md:space-x-6 text-sm text-center md:text-right relative z-10">
            <button
              @click="openLegalModal"
              class="text-gray-400 hover:text-b4 transition-colors cursor-pointer relative z-10"
            >
              Mentions légales
            </button>
            <button
              @click="openPrivacyModal"
              class="text-gray-400 hover:text-b4 transition-colors cursor-pointer relative z-10"
            >
              Politique de confidentialité
            </button>
            <button
              @click="openCGVModal"
              class="text-gray-400 hover:text-b4 transition-colors cursor-pointer relative z-10"
            >
              CGV
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bouton de retour en haut -->
    <button
      @click="scrollToTop"
      class="absolute bottom-2 left-8 w-12 h-12 rounded-full bg-b6/90 hover:bg-b6 text-white flex items-center justify-center shadow-lg transition-all duration-300 hover:scale-110 group cursor-pointer"
      aria-label="Retour en haut de page"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
      </svg>

      <!-- Tooltip -->
      <div
        class="absolute left-full ml-3 px-3 py-2 bg-white text-gray-800 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
        role="tooltip"
        aria-hidden="true"
      >
        Retour en haut
        <!-- Petite flèche pointant vers le bouton -->
        <div
          class="absolute top-1/2 left-0 transform -translate-x-1/2 -translate-y-1/2 rotate-45 w-2 h-2 bg-white"
        ></div>
      </div>
    </button>

    <!-- Modales -->
    <LegalModal :is-open="isLegalModalOpen" @close="closeLegalModal" />
    <CGVModal :is-open="isCGVModalOpen" @close="closeCGVModal" />
    <PrivacyModal :is-open="isPrivacyModalOpen" @close="closePrivacyModal" />
  </footer>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  GlobeAltIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  ArrowUpIcon,
  CodeBracketIcon,
  PaintBrushIcon,
  RocketLaunchIcon,
  ShieldCheckIcon,
} from "@heroicons/vue/24/outline";
import LegalModal from "./LegalModal.vue";
import CGVModal from "./CGVModal.vue";
import PrivacyModal from "./PrivacyModal.vue";

// Animation des éléments
const isVisible = ref(false);
const currentYear = computed(() => new Date().getFullYear());

// État des modales
const isLegalModalOpen = ref(false);
const isCGVModalOpen = ref(false);
const isPrivacyModalOpen = ref(false);

// Ouvrir la modale des mentions légales
const openLegalModal = () => {
  console.log('Ouverture modale légale');
  isLegalModalOpen.value = true;
  document.body.style.overflow = "hidden"; // Empêcher le défilement du fond
};

// Fermer la modale des mentions légales
const closeLegalModal = () => {
  console.log('Fermeture modale légale');
  isLegalModalOpen.value = false;
  document.body.style.overflow = ""; // Rétablir le défilement
};

// Ouvrir la modale des CGV
const openCGVModal = () => {
  console.log('Ouverture modale CGV');
  isCGVModalOpen.value = true;
  document.body.style.overflow = "hidden";
};

// Fermer la modale des CGV
const closeCGVModal = () => {
  console.log('Fermeture modale CGV');
  isCGVModalOpen.value = false;
  document.body.style.overflow = "";
};

// Ouvrir la modale de la politique de confidentialité
const openPrivacyModal = () => {
  console.log('Ouverture modale privacy');
  isPrivacyModalOpen.value = true;
  document.body.style.overflow = "hidden";
};

// Fermer la modale de la politique de confidentialité
const closePrivacyModal = () => {
  console.log('Fermeture modale privacy');
  isPrivacyModalOpen.value = false;
  document.body.style.overflow = "";
};

// Effet de parallaxe au défilement - optimisé avec throttling
let ticking = false;
let lastScrollPosition = 0;

const handleScroll = () => {
  lastScrollPosition = window.scrollY;

  if (!ticking) {
    window.requestAnimationFrame(() => {
      const footerElements = document.querySelectorAll(".parallax-element");

      // Vérifier si la section est visible pour économiser des ressources
      const footer = document.querySelector("footer");
      if (footer) {
        const footerRect = footer.getBoundingClientRect();
        const isVisible = footerRect.top < window.innerHeight && footerRect.bottom >= 0;

        if (isVisible) {
          footerElements.forEach((element, index) => {
            const speed = 0.05 + index * 0.01;
            const yPos = lastScrollPosition * speed;
            element.style.transform = `translateY(${-yPos}px)`;
          });
        }
      }

      ticking = false;
    });

    ticking = true;
  }
};

// Scroll vers le haut optimisé
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

// Scroll vers le bas (section contact)
const scrollToBottom = () => {
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: 'smooth'
  });
};

// Animation au chargement
onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, 300);

  window.addEventListener("scroll", handleScroll);

  // Nettoyage de l'event listener
  return () => {
    window.removeEventListener("scroll", handleScroll);
  };
});
</script>

<style scoped>
/* Animation d'entrée */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Effet de flottement pour les formes décoratives - optimisé */
.parallax-element {
  animation: float 8s ease-in-out infinite;
  will-change: transform;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Styles responsifs */
@media (max-width: 768px) {
  .parallax-element {
    display: none; /* Masquer les formes sur mobile pour les performances */
  }
}
</style> 