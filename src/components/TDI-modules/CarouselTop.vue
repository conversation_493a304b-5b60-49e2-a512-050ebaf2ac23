<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { useModalState } from "../../store/modalStore";

const route = useRoute();
const isAtTop = ref(true);
const { isAnyModalOpen } = useModalState();

const handleScroll = () => {
  // Sur toutes les pages, masquer le carousel si on n'est pas en haut
  isAtTop.value = window.scrollY === 0;
};

onMounted(() => {
  handleScroll(); // Vérifier immédiatement
  window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<template>
  <div v-if="isAtTop && !isAnyModalOpen" class="fixed top-0 left-0 w-full bg-black z-[100]">
    <div class="backdrop-blur-md bg-black rounded-lg px-4 py-2">
      <Vue3Marquee
        class="text-lg font-medium tracking-wide opacity-90"
        :duration="50"
        gradiant="true"
        pauseOnHover
        clone
      >
        <span class="text-b6 font-semibold text-xl">
          ✦ Développez votre présence en ligne ✦</span
        >
        <span class="text-b3 mx-4">|</span>
        <span class="text-b3 text-lg">✦ Sites vitrines modernes et performants ✦</span>
        <span class="text-b4 mx-4">|</span>
        <span class="text-b4 text-md">✦ Supports marketing sur mesure ✦</span>
        <span class="text-white mx-4">|</span>
        <span class="text-white text-base">✦ Stratégie digitale efficace ✦</span>
        <span class="text-white mx-4">|</span>
      </Vue3Marquee>
    </div>
  </div>
</template>
