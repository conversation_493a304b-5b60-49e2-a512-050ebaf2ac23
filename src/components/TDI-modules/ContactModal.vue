<template>
  <form
    v-if="isOpen"
    name="demande-devis"
    method="POST"
    data-netlify="true"
    netlify-honeypot="bot-field"
    @submit.prevent="submitForm"
    class="modal-container flex justify-start sm:justify-center items-start sm:items-center flex-col flex-grow custom-scrollbar px-1 py-0 md:py-2 fixed inset-0 z-90 bg-gradient-to-br from-b1 via-b2/99 to-b2/95 overflow-y-auto overflow-x-hidden"
    aria-labelledby="contact-form-title"
    role="dialog"
    aria-modal="true"
  >
    <div class="max-w-xl w-full md:p-8 p-12 md:pt-8 mt-4 sm:mt-0">
      <!-- Bouton de fermeture accessible -->
      <button
        @click="closeModal"
        class="fixed top-2 md:top-4 right-2 md:right-4 text-white bg-b3/30 hover:bg-b3/50 hover:scale-110 p-2 rounded-full transition-all duration-300 z-50 cursor-pointer"
        aria-label="Fermer le formulaire"
        title="Fermer le formulaire de contact"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>

      <div class="relative z-10 mb-6 text-center mt-4 sm:mt-0">
        <!-- Badge moderne -->
        <div
          class="inline-flex items-center px-3 sm:px-4 py-1.5 sm:p2 rounded-full bg-gradient-to-r from-b5/10 to-b6/10 text-b3 text-xs sm:text-sm font-medium mb-4 sm:mb-8 shadow-sm "
        >
          <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
          Demande de devis
          <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
        </div>

        <h2
          id="contact-form-title"
          class="text-xl sm:text-2xl text-center sm:text-start font-bold my-2 sm:my-4 bg-gradient-to-r from-white via-b4 to-b6 text-transparent bg-clip-text"
        >
          Parlons de votre projet web
        </h2>
        <p class="text-white/60 text-xs sm:text-sm text-center sm:text-start mb-6 sm:mb-4" id="form-description">
          Remplissez le formulaire ci-dessous pour obtenir un devis personnalisé pour votre site web
        </p>

        <!-- Bouton de retour accessible -->
        <button
          type="button"
          @click="closeModal"
          class="absolute -top-16 sm:top-0 -left-10 sm:left-0 px-3 py-1.5 hover:bg-white/5 hover:text-white hover:border-white/20 text-white/80 text-xs font-medium rounded-lg transition-all duration-300 flex items-center shadow-sm border border-white/10 cursor-pointer"
          aria-label="Retour à la page précédente"
          title="Retour à la page précédente"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-3.5 w-3.5 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          Retour
        </button>
      </div>
      <input type="hidden" name="form-name" value="demande-devis" />
      <input type="hidden" name="bot-field" />

      <div class="relative z-10 flex-grow">
        <!-- Informations personnelles - Prénom et Nom -->
        <div class="space-y-3 sm:space-y-4 mb-3 sm:mb-4 pr-10 sm:pr-12" aria-labelledby="personal-info-section">
          <!-- Prénom -->
          <div class="relative">
            <label for="prenom" class="block text-white/90 text-xs font-medium mb-1.5"
              >Prénom <span class="text-b6" title="Champ obligatoire">*</span></label
            >
            <div class="relative">
              <input
                type="text"
                id="prenom"
                name="prenom"
                v-model="formData.prenom"
                required
                @input="validateField('prenom')"
                class="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:border-b3 text-white text-sm transition-colors"
                aria-required="true"
                autocomplete="given-name"
                placeholder="Votre prénom"
              />
              <div
                class="absolute -right-10 top-1/2 transform -translate-y-1/2 p-1 rounded-full border transition-colors duration-300"
                :class="fieldValidation.prenom ? 'text-b5 bg-b5/10 border-b5/20' : 'text-gray-300/30 bg-transparent border-transparent'"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>

          <!-- Nom -->
          <div class="relative">
            <label for="nom" class="block text-white/90 text-xs font-medium mb-1.5"
              >Nom <span class="text-b6" title="Champ obligatoire">*</span></label
            >
            <div class="relative">
              <input
                type="text"
                id="nom"
                name="nom"
                v-model="formData.nom"
                required
                @input="validateField('nom')"
                class="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:border-b3 text-white text-sm transition-colors"
                aria-required="true"
                autocomplete="family-name"
                placeholder="Votre nom"
              />
              <div
                class="absolute -right-10 top-1/2 transform -translate-y-1/2 p-1 rounded-full border transition-colors duration-300"
                :class="fieldValidation.nom ? 'text-b5 bg-b5/10 border-b5/20' : 'text-gray-300/30 bg-transparent border-transparent'"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Coordonnées de contact -->
        <div class="mb-4 relative pr-12" aria-labelledby="contact-info-section">
          <label for="email" class="block text-white/90 text-xs font-medium mb-1.5"
            >Email <span class="text-b6" title="Champ obligatoire">*</span></label
          >
          <div class="relative">
            <input
              type="email"
              id="email"
              name="email"
              v-model="formData.email"
              required
              @input="validateField('email')"
              class="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:border-b3 text-white text-sm transition-colors"
              aria-required="true"
              autocomplete="email"
              placeholder="<EMAIL>"
            />
            <div
              class="absolute -right-10 top-1/2 transform -translate-y-1/2 p-1 rounded-full border transition-colors duration-300"
              :class="fieldValidation.email ? 'text-b5 bg-b5/10 border-b5/20' : 'text-gray-300/30 bg-transparent border-transparent'"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- Entreprise -->
        <div class="mb-4 relative pr-12">
          <label for="entreprise" class="block text-white/90 text-xs font-medium mb-1.5"
            >Entreprise <span class="text-white/40 text-xs">(si applicable)</span></label
          >
          <div class="relative">
            <input
              type="text"
              id="entreprise"
              name="entreprise"
              v-model="formData.entreprise"
              @input="validateField('entreprise')"
              class="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:border-b3 text-white text-sm transition-colors"
              autocomplete="organization"
              placeholder="Nom de votre entreprise"
            />
            <div
              class="absolute -right-10 top-1/2 transform -translate-y-1/2 p-1 rounded-full border transition-colors duration-300"
              :class="fieldValidation.entreprise ? 'text-b5 bg-b5/10 border-b5/20' : 'text-gray-300/30 bg-transparent border-transparent'"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- Détails du projet -->
        <div class="mb-4 relative pr-12" aria-labelledby="project-details-section">
          <label for="type-site" class="block text-white/90 text-xs font-medium mb-1.5"
            >Quel type de site souhaitez-vous ? <span class="text-b6" title="Champ obligatoire">*</span></label
          >

          <!-- Menu déroulant personnalisé pour le type de site -->
          <div class="relative site-type-dropdown">
            <button
              type="button"
              @click="toggleSiteTypeMenu"
              class="w-full flex items-center justify-between px-3 py-2.5 bg-white/5 border border-white/10 rounded-lg text-white text-sm transition-colors hover:bg-white/10 focus:outline-none focus:border-b3"
              aria-haspopup="listbox"
              aria-expanded="false"
              aria-labelledby="type-site-label type-site-button"
              id="type-site-button"
            >
              <span>
                <template v-if="formData.typeSite === ''">
                  <span class="placeholder-text">Choisissez</span>
                </template>
                <template v-else-if="formData.typeSite === 'one-page'"
                  >Site One-page</template
                >
                <template v-else-if="formData.typeSite === 'multi-page'"
                  >Site multi-pages</template
                >
                <template v-else-if="formData.typeSite === 'je-ne-sais-pas'"
                  >Je ne sais pas encore</template
                >
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 transition-transform"
                :class="{ 'rotate-180': siteTypeMenuOpen }"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            <!-- Icône de validation -->
            <div
              class="absolute -right-10 top-1/2 transform -translate-y-1/2 p-1 rounded-full border transition-colors duration-300"
              :class="fieldValidation.typeSite ? 'text-b5 bg-b5/10 border-b5/20' : 'text-gray-300/30 bg-transparent border-transparent'"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>

            <!-- Menu déroulant -->
            <div
              v-show="siteTypeMenuOpen"
              class="absolute z-50 w-full mt-1 py-2 bg-b1/30 backdrop-blur-sm border border-white/10 rounded-lg shadow-xl max-h-[40vh] overflow-y-auto custom-scrollbar"
              role="listbox"
              aria-labelledby="type-site-label"
              id="type-site-listbox"
            >
              <div
                class="px-3 py-2 hover:bg-white/5 transition-colors cursor-pointer"
                @click="selectSiteType('one-page')"
                role="option"
                aria-selected="false"
                id="type-site-option-one-page"
              >
                <div class="text-white/80 text-sm">Site One-page</div>
              </div>
              <div
                class="px-3 py-2 hover:bg-white/5 transition-colors cursor-pointer"
                @click="selectSiteType('multi-page')"
                role="option"
                aria-selected="false"
                id="type-site-option-multi-page"
              >
                <div class="text-white/80 text-sm">Site multi-pages</div>
              </div>
              <div
                class="px-3 py-2 hover:bg-white/5 transition-colors cursor-pointer"
                @click="selectSiteType('je-ne-sais-pas')"
                role="option"
                aria-selected="false"
                id="type-site-option-unknown"
              >
                <div class="text-white/80 text-sm">Je ne sais pas encore</div>
              </div>
            </div>

            <!-- Champ caché pour la soumission du formulaire -->
            <input type="hidden" id="type-site" name="type-site" v-model="formData.typeSite" required aria-required="true" />
          </div>
        </div>

        <!-- Description du projet (optionnel) -->
        <div class="mb-4 relative pr-12">
          <label for="projet" class="block text-white/90 text-xs font-medium mb-1.5"
            >Quelques mots sur votre projet
            <span class="text-white/40 text-xs">(optionnel)</span></label
          >
          <div class="relative">
            <textarea
              id="projet"
              name="projet"
              rows="3"
              v-model="formData.projet"
              @input="validateField('projet')"
              placeholder="Parlez-moi de votre activité, vos besoins, vos envies…"
              class="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:border-b3 text-white text-sm transition-colors"
              aria-describedby="projet-description"
            ></textarea>
            <div id="projet-description" class="sr-only">Décrivez votre projet ou vos besoins pour nous aider à mieux comprendre votre demande</div>
            <div
              class="absolute -right-10 top-1/2 transform -translate-y-1/2 p-1 rounded-full border transition-colors duration-300"
              :class="fieldValidation.projet ? 'text-b5 bg-b5/10 border-b5/20' : 'text-gray-300/30 bg-transparent border-transparent'"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- Options complémentaires -->
        <div class="mb-5 relative pr-12">
          <label class="block text-white/90 text-xs font-medium mb-1.5"
            >Options complémentaires souhaitées</label
          >

          <!-- Menu déroulant pour les options -->
          <div class="relative options-dropdown">
            <!-- Icône de validation -->
            <div
              class="absolute -right-10 top-1/2 transform -translate-y-1/2 p-1 rounded-full border transition-colors duration-300"
              :class="fieldValidation.options ? 'text-b5 bg-b5/10 border-b5/20' : 'text-gray-300/30 bg-transparent border-transparent'"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <button
              type="button"
              @click="toggleOptionsMenu"
              class="w-full flex items-center justify-between px-3 py-2.5 bg-white/5 border border-white/10 rounded-lg text-white text-sm transition-colors hover:bg-white/10 focus:outline-none focus:border-b3"
            >
              <span>
                <template v-if="isNoneOptionSelected">Je ne sais pas encore</template>
                <template v-else-if="formData.selectedOptions.length"
                  >{{ formData.selectedOptions.length }} option(s)
                  sélectionnée(s)</template
                >
                <template v-else>
                  <span class="placeholder-text">Sélectionnez des options</span>
                </template>
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 transition-transform"
                :class="{ 'rotate-180': optionsMenuOpen }"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            <!-- Menu déroulant -->
            <div
              v-show="optionsMenuOpen"
              class="absolute z-50 w-full mt-1 py-2 bg-b1/30 backdrop-blur-sm border border-white/10 rounded-lg shadow-xl max-h-[40vh] overflow-y-auto custom-scrollbar"
            >
              <!-- Option "Je ne sais pas encore" en premier -->
              <div
                class="px-3 py-2 border-b border-white/10 mb-1 cursor-pointer hover:bg-white/5 transition-colors"
                @click="toggleNoneOption"
              >
                <div class="flex items-start">
                  <div class="mt-1 mr-3 h-4 w-4 relative flex-shrink-0">
                    <input
                      type="checkbox"
                      id="option-none"
                      name="options[]"
                      value="Je ne sais pas encore"
                      v-model="formData.selectedOptions"
                      @change="handleNoneOptionChange"
                      class="absolute opacity-0 w-0 h-0"
                    />
                    <div
                      class="w-4 h-4 rounded border border-white/10 bg-white/5 flex items-center justify-center"
                    >
                      <svg
                        v-if="isNoneOptionSelected"
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-3 w-3 text-b6"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="text-white/90 text-sm cursor-pointer flex-grow">
                    <div class="font-medium leading-4">Je ne sais pas encore</div>
                    <p class="text-white/60 text-xs mt-1 leading-3">
                      Je souhaite discuter des options avec vous
                    </p>
                  </div>
                </div>
              </div>

              <!-- Autres options -->
              <div
                v-for="(option, index) in additionalOptions"
                :key="index"
                class="px-3 py-2 hover:bg-white/5 transition-colors cursor-pointer"
                @click="selectOption(option.title)"
              >
                <div class="flex items-start">
                  <div class="mt-1 mr-3 h-4 w-4 relative flex-shrink-0">
                    <input
                      type="checkbox"
                      :id="`option-${index}`"
                      :name="`options[]`"
                      :value="option.title"
                      v-model="formData.selectedOptions"
                      @change="handleOptionChange"
                      class="absolute opacity-0 w-0 h-0"
                    />
                    <div
                      class="w-4 h-4 rounded border border-white/10 bg-white/5 flex items-center justify-center"
                    >
                      <svg
                        v-if="formData.selectedOptions.includes(option.title)"
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-3 w-3 text-b6"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                  <div
                    :class="`text-sm cursor-pointer flex-grow ${
                      isNoneOptionSelected ? 'text-white/40' : 'text-white/80'
                    }`"
                  >
                    <div class="font-medium leading-4">{{ option.title }}</div>
                    <div class="text-white/60 text-xs mt-1 leading-3">
                      {{ option.description }}
                    </div>
                    <div class="text-white/60 text-xs mt-0.5 leading-3">
                      {{ option.price }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Affichage des options sélectionnées -->
          <div
            v-if="formData.selectedOptions.length > 0 && !isNoneOptionSelected"
            class="mt-2 flex flex-wrap gap-1"
          >
            <div
              v-for="option in formData.selectedOptions.filter(
                (opt) => opt !== 'Je ne sais pas encore'
              )"
              :key="option"
              class="inline-flex items-center px-2 py-1 bg-b3/10 text-white/80 text-xs rounded-md"
            >
              {{ option }}
            </div>
          </div>
        </div>

        <!-- Option de rappel téléphonique -->
        <div class="mb-5 relative pr-12">
          <div class="flex mb-3">
            <label for="rappel" class="flex items-center text-white/90 text-sm font-medium cursor-pointer">
              <input
                type="checkbox"
                id="rappel"
                name="rappel"
                v-model="formData.rappel"
                @change="formData.rappel ? null : (formData.telephone = '')"
                class="custom-checkbox mr-2"
              />
              <span>Souhaitez-vous être rappelé(e) ?</span>
            </label>
          </div>

          <!-- Champ téléphone (visible uniquement si rappel est coché) -->
          <div v-if="formData.rappel" class="relative">
            <label for="telephone" class="block text-white/90 text-xs font-medium mb-1.5"
              >Numéro de téléphone <span class="text-b6" title="Champ obligatoire">*</span></label
            >
            <div class="relative">
              <input
                type="tel"
                id="telephone"
                name="telephone"
                v-model="formData.telephone"
                @input="validateField('telephone')"
                class="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:border-b3 text-white text-sm transition-colors"
                aria-required="true"
                autocomplete="tel"
                placeholder="+33 6 12 34 56 78"
              />
              <div
                class="absolute -right-10 top-1/2 transform -translate-y-1/2 p-1 rounded-full border transition-colors duration-300"
                :class="fieldValidation.telephone ? 'text-b5 bg-b5/10 border-b5/20' : 'text-gray-300/30 bg-transparent border-transparent'"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <p class="text-white/50 text-xs mt-1">Format: +33 6 12 34 56 78 ou 06 12 34 56 78</p>
          </div>
        </div>

        <!-- Message de succès -->
        <div
          v-if="formSubmitted"
          class="mb-4 p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400"
          role="alert"
          aria-live="polite"
        >
          <div class="flex items-center mb-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2 text-green-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Votre demande a été envoyée avec succès !</span>
          </div>
          <div class="space-y-2">
            <button 
              @click="closeModal(); formSubmitted = false"
              class="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors">
              Fermer
            </button>
            <p class="text-center text-green-300 text-sm">
              Cette fenêtre se fermera automatiquement dans quelques secondes
            </p>
          </div>
        </div>

        <!-- Message d'erreur -->
        <div
          v-if="formError"
          class="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400"
          role="alert"
          aria-live="assertive"
        >
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2 text-red-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Une erreur est survenue. Veuillez réessayer.</span>
          </div>
        </div>
      </div>

      <!-- Bouton d'envoi -->
      <div v-if="!formSubmitted" class="w-full">
        <button
          type="submit"
          :disabled="formSubmitting"
          class="w-full px-6 py-3 bg-gradient-to-r from-b3 to-b6 text-white font-medium rounded-lg shadow-lg transition-all duration-300 hover:shadow-b3/20 hover:shadow-lg transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
          aria-label="Envoyer la demande de devis"
          title="Envoyer votre demande de devis"
        >
          <span v-if="formSubmitting" class="flex items-center justify-center">
            <svg
              class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            Envoi en cours...
          </span>
          <span v-else>Envoyer ma demande</span>
        </button>
      </div>
    </div>
  </form>
</template>

<script setup>
/**
 * Formulaire de contact pour demande de devis
 * Ce composant affiche un formulaire modal permettant aux utilisateurs de soumettre une demande de devis
 * pour la création d'un site web.
 */
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import { setModalOpen } from "../../store/modalStore";

// Définition des props et des émissions d'événements
const emit = defineEmits(["close"]);
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
    required: true,
  },
});

// État des menus déroulants
const optionsMenuOpen = ref(false);
const siteTypeMenuOpen = ref(false);

// Fonction pour basculer l'affichage du menu déroulant des options
const toggleOptionsMenu = (event) => {
  // Empêcher la propagation de l'événement pour éviter que handleClickOutside ne soit déclenché
  if (event) {
    event.stopPropagation();
  }

  // Fermer l'autre menu s'il est ouvert
  if (siteTypeMenuOpen.value) {
    siteTypeMenuOpen.value = false;
  }

  // Basculer l'état du menu
  optionsMenuOpen.value = !optionsMenuOpen.value;
};

// Fonction pour basculer l'affichage du menu déroulant du type de site
const toggleSiteTypeMenu = (event) => {
  // Empêcher la propagation de l'événement
  if (event) {
    event.stopPropagation();
  }

  // Fermer l'autre menu s'il est ouvert
  if (optionsMenuOpen.value) {
    optionsMenuOpen.value = false;
  }

  // Basculer l'état du menu
  siteTypeMenuOpen.value = !siteTypeMenuOpen.value;
};

// Fonction pour sélectionner un type de site
const selectSiteType = (type) => {
  formData.value.typeSite = type;
  validateField("typeSite");
  siteTypeMenuOpen.value = false;
};

// Vérifier si l'option "Je ne sais pas encore" est sélectionnée
const isNoneOptionSelected = computed(() => {
  return formData.value.selectedOptions.includes("Je ne sais pas encore");
});

// Basculer l'option "Je ne sais pas encore"
const toggleNoneOption = () => {
  const noneOption = "Je ne sais pas encore";
  const isSelected = formData.value.selectedOptions.includes(noneOption);

  if (isSelected) {
    // Si déjà sélectionné, on le désélectionne
    formData.value.selectedOptions = formData.value.selectedOptions.filter(
      (opt) => opt !== noneOption
    );
  } else {
    // Si pas sélectionné, on le sélectionne et on supprime toutes les autres options
    formData.value.selectedOptions = [noneOption];
  }

  // Valider le champ des options
  validateField('options');

  // Fermer le menu déroulant
  optionsMenuOpen.value = false;
};

// Sélectionner une option spécifique
const selectOption = (optionTitle) => {
  // Si "Je ne sais pas encore" est sélectionné, on le supprime
  if (isNoneOptionSelected.value) {
    const index = formData.value.selectedOptions.indexOf("Je ne sais pas encore");
    if (index > -1) {
      formData.value.selectedOptions.splice(index, 1);
    }
  }

  // Basculer l'option sélectionnée
  const optionIndex = formData.value.selectedOptions.indexOf(optionTitle);
  if (optionIndex > -1) {
    // Si déjà sélectionnée, on la désélectionne
    formData.value.selectedOptions.splice(optionIndex, 1);
  } else {
    // Sinon, on l'ajoute
    formData.value.selectedOptions.push(optionTitle);
  }

  // Valider le champ des options
  validateField('options');
};

// Gérer le changement de l'option "Je ne sais pas encore"
const handleNoneOptionChange = () => {
  if (isNoneOptionSelected.value) {
    // Si "Je ne sais pas encore" est coché, supprimer toutes les autres options
    formData.value.selectedOptions = ["Je ne sais pas encore"];
  }

  // Valider le champ des options
  validateField('options');
};

// Gérer le changement des autres options
const handleOptionChange = () => {
  // Si une autre option est cochée, supprimer "Je ne sais pas encore"
  if (
    formData.value.selectedOptions.length > 0 &&
    formData.value.selectedOptions.includes("Je ne sais pas encore")
  ) {
    const index = formData.value.selectedOptions.indexOf("Je ne sais pas encore");
    if (index > -1) {
      formData.value.selectedOptions.splice(index, 1);
    }
  }

  // Valider le champ des options
  validateField('options');
};

// Fermer les menus déroulants si on clique ailleurs
const handleClickOutside = (e) => {
  // Vérifier si le menu des options est ouvert et si le clic est en dehors du menu
  if (optionsMenuOpen.value && !e.target.closest(".options-dropdown")) {
    optionsMenuOpen.value = false;
  }

  // Vérifier si le menu du type de site est ouvert et si le clic est en dehors du menu
  if (siteTypeMenuOpen.value && !e.target.closest(".site-type-dropdown")) {
    siteTypeMenuOpen.value = false;
  }
};

onMounted(() => {
  // Utiliser setTimeout pour s'assurer que l'événement est ajouté après le rendu
  setTimeout(() => {
    document.addEventListener("mousedown", handleClickOutside);
  }, 0);

  // Empêcher le défilement du body quand la modale est ouverte
  if (props.isOpen) {
    preventBodyScroll();
    // Mettre à jour l'état global pour masquer la navbar
    setModalOpen(true);
  }
});

onUnmounted(() => {
  document.removeEventListener("mousedown", handleClickOutside);
  // Réactiver le défilement du body quand le composant est démonté
  enableBodyScroll();
  // Rétablir l'affichage de la navbar
  setModalOpen(false);
});

// Observer les changements de la propriété isOpen
watch(
  () => props.isOpen,
  (newValue) => {
    if (newValue) {
      preventBodyScroll();
      // Mettre à jour l'état global pour masquer la navbar
      setModalOpen(true);
    } else {
      enableBodyScroll();
      // Mettre à jour l'état global pour afficher la navbar
      setModalOpen(false);
    }
  }
);

// Options complémentaires disponibles
const additionalOptions = [
  {
    title: "Modifications ponctuelles",
    description: "Modifications sur un site déjà réalisé, rapides et sur mesure",
    price: "Sur devis",
  },
  {
    title: "Visuels personnalisés",
    description: "Logo, bannière, illustration, visuel digital…",
    price: "+120€ / visuel",
  },
  {
    title: "Page supplémentaire",
    description: "Blog, galerie, mentions légales… avec design personnalisé",
    price: "Sur devis",
  },
  {
    title: "Maintenance",
    description: "Corrections de bugs, ajustements, mises à jour, SEO",
    price: "+180€",
  },
  {
    title: "Nom de domaine personnalisé",
    description: "www.nom-entreprise.com (domaine à acheter séparément ~12€/an)",
    price: "+150€",
  },
  {
    title: "Adresse e-mail professionnelle",
    description: "<EMAIL> (Google Workspace ~8,10€/mois)",
    price: "+120€",
  },
];

// État du formulaire
const formData = ref({
  prenom: "",
  nom: "",
  email: "",
  entreprise: "",
  typeSite: "",
  projet: "",
  selectedOptions: [],
  rappel: false,
  telephone: "",
});

// État de validation des champs
const fieldValidation = ref({
  prenom: false,
  nom: false,
  email: false,
  entreprise: false,
  typeSite: false,
  projet: false,
  options: false,
  telephone: false,
});

/**
 * Valide un champ du formulaire selon des règles spécifiques
 * @param {string} field - Le nom du champ à valider
 */
const validateField = (field) => {
  switch (field) {
    case "prenom":
      // Le prénom doit contenir au moins 2 caractères
      fieldValidation.value.prenom = formData.value.prenom.trim().length >= 2;
      break;
    case "nom":
      // Le nom doit contenir au moins 2 caractères
      fieldValidation.value.nom = formData.value.nom.trim().length >= 2;
      break;
    case "email":
      // L'email doit correspondre à un format valide
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      fieldValidation.value.email = emailRegex.test(formData.value.email);
      break;
    case "entreprise":
      // Le nom de l'entreprise doit être rempli s'il est fourni
      fieldValidation.value.entreprise = formData.value.entreprise.trim().length > 0;
      break;
    case "typeSite":
      // Un type de site doit être sélectionné
      fieldValidation.value.typeSite = formData.value.typeSite !== "";
      break;
    case "projet":
      // La description du projet doit être remplie si elle est fournie
      fieldValidation.value.projet = formData.value.projet.trim().length > 0;
      break;
    case "options":
      // Au moins une option doit être sélectionnée
      fieldValidation.value.options = formData.value.selectedOptions.length > 0;
      break;
    case "telephone":
      // Le téléphone doit être valide si l'option rappel est sélectionnée
      if (formData.value.rappel) {
        // Validation simple d'un numéro de téléphone français
        const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
        fieldValidation.value.telephone = phoneRegex.test(formData.value.telephone);
      } else {
        // Si rappel non demandé, le champ est considéré comme valide
        fieldValidation.value.telephone = true;
      }
      break;
  }
};

const formSubmitting = ref(false);
const formSubmitted = ref(false);
const formError = ref(false);

// Variables pour sauvegarder l'état du scroll
let scrollY = 0;

// Fonction pour empêcher le défilement du body
const preventBodyScroll = () => {
  // Sauvegarder la position de scroll actuelle
  scrollY = window.scrollY;
  
  // Ajouter la classe CSS pour bloquer le scroll
  document.body.classList.add('modal-open');
  
  // Appliquer les styles pour bloquer le scroll
  document.body.style.position = 'fixed';
  document.body.style.top = `-${scrollY}px`;
  document.body.style.width = '100%';
  document.body.style.overflow = 'hidden';
  
  // Empêcher le scroll sur les appareils tactiles
  document.addEventListener('touchmove', preventTouchMove, { passive: false });
  document.addEventListener('wheel', preventWheelScroll, { passive: false });
};

// Fonction pour réactiver le défilement du body
const enableBodyScroll = () => {
  // Retirer la classe CSS
  document.body.classList.remove('modal-open');
  
  // Restaurer les styles du body
  document.body.style.position = '';
  document.body.style.top = '';
  document.body.style.width = '';
  document.body.style.overflow = '';
  
  // Restaurer la position de scroll
  window.scrollTo(0, scrollY);
  
  // Retirer les événements
  document.removeEventListener('touchmove', preventTouchMove);
  document.removeEventListener('wheel', preventWheelScroll);
};

// Fonction pour empêcher le scroll tactile
const preventTouchMove = (e) => {
  // Permettre le scroll uniquement à l'intérieur du formulaire
  if (!e.target.closest('.custom-scrollbar')) {
    e.preventDefault();
  }
};

// Fonction pour empêcher le scroll avec la molette
const preventWheelScroll = (e) => {
  // Permettre le scroll uniquement à l'intérieur du formulaire
  if (!e.target.closest('.custom-scrollbar')) {
    e.preventDefault();
  }
};

// Fermer la modale
const closeModal = () => {
  enableBodyScroll();
  // Mettre à jour l'état global pour afficher la navbar
  setModalOpen(false);
  emit("close");
};

/**
 * Soumet le formulaire à Netlify et gère les états de succès/erreur
 * @param {Event} event - L'événement de soumission du formulaire
 */
const submitForm = (event) => {
  // Validation manuelle des champs obligatoires
  if (!formData.value.prenom.trim()) {
    alert('Le prénom est obligatoire')
    return
  }
  
  if (!formData.value.nom.trim()) {
    alert('Le nom est obligatoire')
    return
  }
  
  if (!formData.value.email.trim()) {
    alert('L\'email est obligatoire')
    return
  }
  
  // Validation format email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(formData.value.email)) {
    alert('Veuillez entrer un email valide')
    return
  }
  
  if (!formData.value.typeSite) {
    alert('Veuillez sélectionner un type de site')
    return
  }
  
  if (formData.value.selectedOptions.length === 0) {
    alert('Veuillez sélectionner au moins une option')
    return
  }
  
  // Validation du téléphone si rappel demandé
  if (formData.value.rappel && !formData.value.telephone.trim()) {
    alert('Le numéro de téléphone est obligatoire si vous souhaitez être rappelé')
    return
  }
  
  if (formData.value.rappel && formData.value.telephone.trim()) {
    const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/
    if (!phoneRegex.test(formData.value.telephone)) {
      alert('Veuillez entrer un numéro de téléphone valide (format français)')
      return
    }
  }

  // Mettre à jour les états du formulaire
  formSubmitting.value = true;
  formError.value = false;

  // Récupérer les données du formulaire
  const myForm = event.target;
  const formDataObj = new FormData(myForm);

  // Ajouter les options sélectionnées au formData
  if (formData.value.selectedOptions.length > 0) {
    formDataObj.append('options', formData.value.selectedOptions.join(', '));
  }

  // Ajouter les informations de rappel
  formDataObj.append('rappel', formData.value.rappel ? 'Oui' : 'Non');
  if (formData.value.rappel && formData.value.telephone) {
    formDataObj.append('telephone', formData.value.telephone);
  }

  // Débogage - Afficher les données du formulaire
  console.log('Données du formulaire à envoyer:');
  for (const pair of formDataObj.entries()) {
    console.log(pair[0] + ': ' + pair[1]);
  }

  // Suivre exactement l'exemple de la documentation Netlify
  fetch("/", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: new URLSearchParams(formDataObj).toString()
  })
    .then(() => {
      console.log("Formulaire envoyé avec succès");
      // Succès : afficher le message et réinitialiser
      formSubmitted.value = true;
      myForm.reset();

      // Réinitialiser les validations
      Object.keys(fieldValidation.value).forEach(key => {
        fieldValidation.value[key] = false;
      });

      // Réinitialiser les options sélectionnées
      formData.value.selectedOptions = [];
      
      // Réinitialiser complètement le formulaire
      formData.value = {
        prenom: "",
        nom: "",
        email: "",
        entreprise: "",
        typeSite: "",
        projet: "",
        selectedOptions: [],
        rappel: false,
        telephone: "",
      };

      // Fermer la modale après 3 secondes
      setTimeout(() => {
        closeModal();
        formSubmitted.value = false;
      }, 3000);
    })
    .catch(error => {
      console.error("Erreur lors de l'envoi du formulaire:", error);
      formError.value = true;
      formSubmitting.value = false;
    })
    .finally(() => {
      formSubmitting.value = false;
    });
};
</script>

<style scoped>
/**
 * Styles pour le formulaire de contact
 * Optimisés pour les performances et l'accessibilité
 */

/* Variables de couleur */
/* b2: #57737A */

/* Animation d'entrée et de sortie pour la modale - optimisée pour les performances */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s, transform 0.3s;
  will-change: opacity, transform;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Personnalisation de la scrollbar pour une meilleure expérience utilisateur */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Amélioration de l'accessibilité des éléments interactifs */
input[type="checkbox"], .custom-checkbox {
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.25rem;
  width: 1rem;
  height: 1rem;
  display: inline-block;
  position: relative;
  vertical-align: -0.125em;
  margin-right: 0.5rem;
}

input[type="checkbox"]:checked, .custom-checkbox:checked {
  background-color: #B7E4E8; /* b5 */
  border-color: #B7E4E8; /* b5 */
}

input[type="checkbox"]:checked::after, .custom-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

input[type="checkbox"]:focus, .custom-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(183, 228, 232, 0.3); /* b5 avec opacité */
}

/* Focus visible pour l'accessibilité */
input:focus,
select:focus,
textarea:focus,
button:focus {
  box-shadow: 0 0 0 2px rgba(var(--color-b3-rgb), 0.2);
  outline: none;
}

/* Style pour tous les éléments cliquables */
button,
a,
[role="button"],
.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Optimisation pour les appareils mobiles */
@media (max-width: 640px) {
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }
}

/* Uniformisation des placeholders */
input::placeholder,
textarea::placeholder {
  color: rgba(255, 255, 255, 0.4) !important;
  opacity: 1 !important;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.4) !important;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: rgba(255, 255, 255, 0.4) !important;
  opacity: 1 !important;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.4) !important;
}

/* Placeholders pour les champs de type select personnalisés */
.placeholder-text {
  color: rgba(255, 255, 255, 0.4) !important;
}

/* Empêcher le scroll sur le body quand la modale est ouverte */
body.modal-open {
  position: fixed !important;
  width: 100% !important;
  overflow: hidden !important;
}

/* S'assurer que la modale gère correctement le scroll */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}
</style>
