<template>
  <!-- Hero section pour la page formules -->
  <section class="relative pt-52 pb-10 bg-gradient-to-b from-black to-transparent">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div
        class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-b3/10 to-b6/10 text-b3 text-sm font-medium mb-8 shadow-sm"
      >
        <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
        Nos formules sur-mesure
        <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
      </div>

      <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
        <span
          class="bg-gradient-to-r from-white via-b4 to-b6 text-transparent bg-clip-text"
        >
          Formules & Tarifs
        </span>
      </h1>

      <p class="text-xl text-white/70 max-w-3xl mx-auto mb-8">
        Découvrez nos différentes formules de création de sites web, adaptées à vos
        besoins et à votre budget.
      </p>
    </div>
  </section>
  <section
    id="pricing"
    class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"
    aria-labelledby="pricing-title"
    itemscope
    itemtype="https://schema.org/PriceSpecification"
  >
    <!-- En-tête de section avec plus d'espace -->

    <!-- Pricing Table - Design moderne et compact (version desktop) -->
    <div
      class="hidden md:block max-w-7xl mx-auto bg-white/5 backdrop-blur-md rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform"
    >
      <!-- En-tête du tableau -->
      <div class="grid grid-cols-4 border-b border-white/10">
        <div class="p-6 col-span-1 border-r border-white/10">
          <h3 class="text-xl font-bold text-white mb-3">Formule</h3>
          <div
            class="bg-gradient-to-r from-b3/20 to-transparent p-3 rounded-lg border-l-2 border-b6"
          >
            <p class="text-white/90 text-sm font-medium">
              <span class="text-b6 font-semibold">Trois formules</span>, une seule
              ambition,
              <span
                class="bg-gradient-to-r from-white via-b4 to-b6 text-transparent bg-clip-text font-bold"
                >booster votre impact en ligne</span
              >
            </p>
          </div>
        </div>

        <!-- En-têtes des colonnes -->
        <div
          v-for="(plan, index) in plans"
          :key="`header-${index}`"
          class="p-6 col-span-1 relative"
          :class="{ 'bg-gradient-to-b from-b3/20 to-transparent': plan.isPopular }"
        >
          <!-- Badge populaire -->
          <div
            v-if="plan.isPopular"
            class="absolute -top-0 -right-0 bg-b3 text-white text-xs font-bold py-1 px-3 rounded-bl-lg shadow-lg"
          >
            POPULAIRE
          </div>

          <h3 class="text-xl font-bold text-white mb-1">{{ plan.name }}</h3>
          <div class="mb-2">
            <div v-if="plan.originalPrice" class="flex items-baseline mb-1">
              <p class="text-white/90 text-xs italic mr-2">à partir de</p>
              <span class="text-2xl font-extrabold text-white">{{ plan.price }}</span>
            </div>
            <div v-if="plan.originalPrice" class="flex items-center space-x-2 mb-1">
              <span class="text-sm text-white/60 line-through">{{
                plan.originalPrice
              }}</span>
              <span class="text-xs bg-b6 text-white px-2 py-1 rounded-full font-medium"
                >OFFRE LIMITÉE</span
              >
            </div>
            <div v-if="!plan.originalPrice" class="flex items-baseline">
              <span class="text-2xl font-extrabold text-white">{{ plan.price }}</span>
            </div>
            <p v-if="plan.promoText" class="text-xs text-b6 font-medium">
              {{ plan.promoText }}
            </p>
          </div>
          <p class="text-white/90 text-xs italic">
            {{
              index === 0
                ? "L'essentiel pour une présence en ligne efficace"
                : index === 1
                ? "Un site complet pour présenter votre activité en détail"
                : "Une solution 100% personnalisée, pensée pour convaincre"
            }}
          </p>
        </div>
      </div>

      <!-- Catégories de fonctionnalités -->
      <template
        v-for="(category, cIndex) in ['structure', 'design', 'technique', 'support']"
        :key="`category-${cIndex}`"
      >
        <!-- En-tête de catégorie -->
        <div
          class="grid grid-cols-4 border-b border-white/10 bg-gradient-to-r from-b3/30 to-b2/10"
        >
          <div class="p-4 col-span-1 border-r border-white/10 flex items-center">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-b6 rounded-full mr-3"></div>
              <span class="text-white font-semibold tracking-wide uppercase text-sm">
                {{
                  category === "structure"
                    ? "Détails du forfait"
                    : category === "design"
                    ? "Design & UX"
                    : category === "technique"
                    ? "Aspects techniques"
                    : "Support & maintenance"
                }}
              </span>
            </div>
          </div>
          <div class="col-span-3 bg-gradient-to-r from-b3/10 to-transparent"></div>
        </div>

        <!-- Lignes des fonctionnalités par catégorie -->
        <div
          v-for="(feature, fIndex) in commonFeatures.filter(
            (f) => f.category === category
          )"
          :key="`${category}-${fIndex}`"
          class="grid grid-cols-4 border-b border-white/10"
          :class="{ 'bg-white/5': fIndex % 2 === 0 }"
        >
          <div class="p-5 col-span-1 border-r border-white/10 flex items-center">
            <span class="text-white/90">{{ feature.text }}</span>
          </div>

          <!-- Valeurs pour chaque plan -->
          <!-- TODO: Ajouter un pictogramme ou une coche par avantage dans la version visuelle du site pour faciliter la lecture -->
          <div
            v-for="(plan, pIndex) in plans"
            :key="`${category}-${fIndex}-${pIndex}`"
            class="p-5 col-span-1 flex items-center justify-center"
            :class="{ 'bg-b3/10': plan.isPopular && fIndex % 2 === 0 }"
          >
            <div v-if="typeof feature.values[pIndex] === 'boolean'">
              <svg
                v-if="feature.values[pIndex]"
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-b6"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
              <svg
                v-else
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-white/30"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <span v-else class="text-white/90 text-center text-sm">{{
              feature.values[pIndex]
            }}</span>
          </div>
        </div>
      </template>

      <!-- Ligne d'actions unifiée - Nouveau design -->
      <div class="grid grid-cols-4">
        <div class="col-span-1 border-r border-white/10 p-6 flex items-center">
          <div class="flex items-center">
            <div class="w-1 h-6 bg-b6 rounded-full mr-3"></div>
            <span class="text-white font-semibold tracking-wide uppercase text-sm"
              >Me contacter</span
            >
          </div>
        </div>

        <!-- Conteneur des boutons d'action avec fond légèrement différent -->
        <div class="col-span-3 p-6 bg-gradient-to-r from-b3/10 to-transparent">
          <div class="flex justify-around items-center">
            <!-- Bouton Devis -->
            <div class="flex flex-col items-center pricing-devis">
              <button
                @click="openContactModal"
                class="group relative w-16 h-16 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 transform hover:scale-110 cursor-pointer bg-gradient-to-br from-b3 to-b6 hover:shadow-b6/30 mb-2 pricing-devis"
              >
                <div
                  class="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 rounded-full transition-opacity duration-300 pricing-devis"
                ></div>
                <EnvelopeIcon class="h-7 w-7 text-white relative pricing-devis" />
              </button>
              <span class="text-white/80 text-xs font-medium pricing-devis"
                >Devis gratuit</span
              >
            </div>

            <!-- Bouton WhatsApp -->
            <div class="flex flex-col items-center pricing-whatsapp">
              <a
                href="https://wa.me/33601273775?text=Bonjour,%20je%20souhaite%20des%20informations%20sur%20vos%20services%20de%20cr%C3%A9ation%20de%20site%20web."
                target="_blank"
                rel="noopener noreferrer"
                class="group relative w-16 h-16 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 transform hover:scale-110 cursor-pointer bg-gradient-to-br from-b6 to-b6/80 hover:shadow-b6/30 mb-2 pricing-whatsapp"
              >
                <svg
                  class="h-7 w-7 text-white relative pricing-whatsapp"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z"
                  />
                </svg>
              </a>
              <span class="text-white/80 text-xs font-medium pricing-whatsapp"
                >WhatsApp</span
              >
            </div>

            <!-- Bouton Email -->
            <div class="flex flex-col items-center pricing-email">
              <a
                href="mailto:<EMAIL>?subject=Demande%20d'information%20-%20The%20Dev%20Impact&body=Bonjour,%0A%0AJe%20vous%20contacte%20suite%20à%20ma%20visite%20sur%20votre%20site%20web.%20Je%20souhaiterais%20obtenir%20plus%20d'informations%20concernant%20vos%20services.%0A%0AMerci%20d'avance%20pour%20votre%20réponse.%0A%0ACordialement,"
                class="group relative w-16 h-16 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 transform hover:scale-110 cursor-pointer bg-gradient-to-br from-b2 to-b3 hover:shadow-b3/30 mb-2 pricing-email"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-7 w-7 text-white relative pricing-email"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </a>
              <span class="text-white/80 text-xs font-medium pricing-email">Email</span>
            </div>

            <!-- Bouton Téléphone -->
            <div class="flex flex-col items-center pricing-telephone">
              <a
                href="tel:+33601273775"
                class="group relative w-16 h-16 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 transform hover:scale-110 cursor-pointer bg-gradient-to-br from-b3 to-b4 hover:shadow-b4/30 mb-2 pricing-telephone"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-7 w-7 text-white relative pricing-telephone"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                  />
                </svg>
              </a>
              <span class="text-white/80 text-xs font-medium pricing-telephone"
                >Téléphone</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Version mobile des cartes de prix (affichage compact) -->
    <div
      class="md:hidden space-y-4 sm:space-y-6 max-w-md mx-auto"
      itemprop="offers"
      itemscope
      itemtype="https://schema.org/AggregateOffer"
    >
      <div
        v-for="(plan, index) in plans"
        :key="`mobile-${index}`"
        class="bg-white/5 backdrop-blur-md rounded-xl overflow-hidden shadow-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl"
        itemscope
        itemtype="https://schema.org/Offer"
        itemprop="itemOffered"
        :class="{ 'border-2 border-b6': plan.isPopular }"
      >
        <!-- En-tête avec badge et prix -->
        <div
          class="relative p-5"
          :class="
            plan.isPopular
              ? 'bg-gradient-to-r from-b3/20 to-b2/20'
              : 'bg-gradient-to-r from-b1/30 to-b2/20'
          "
        >
          <!-- Badge populaire -->
          <div
            v-if="plan.isPopular"
            class="absolute -top-1 -right-1 bg-b6 text-white text-xs font-bold py-1 px-3 rounded-bl-lg shadow-lg"
          >
            POPULAIRE
          </div>

          <h3 class="text-lg sm:text-xl font-bold text-white mb-1" itemprop="name">
            {{ plan.name }}
          </h3>
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="flex items-baseline mb-1">
                <span
                  class="text-xl sm:text-2xl font-extrabold text-white"
                  itemprop="price"
                  >{{ plan.price }}</span
                >
                <span
                  v-if="plan.originalPrice"
                  class="ml-2 text-sm text-white/60 line-through"
                  >{{ plan.originalPrice }}</span
                >
                <span
                  v-if="plan.originalPrice"
                  class="ml-2 text-xs bg-b6 text-white px-2 py-1 rounded-full font-medium"
                  >PROMO</span
                >
              </div>
              <p v-if="plan.promoText" class="text-xs text-b6 font-medium mb-2">
                {{ plan.promoText }}
              </p>
            </div>
            <div class="flex items-center text-white/70 text-sm ml-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1 text-b3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>{{
                index === 0 ? "10 jours" : index === 1 ? "15 jours" : "Sur devis"
              }}</span>
            </div>
          </div>
          <p class="text-white/90 text-xs italic mt-2" itemprop="description">
            {{
              index === 0
                ? "L'essentiel pour une présence en ligne efficace"
                : index === 1
                ? "Un site complet pour présenter votre activité en détail"
                : "Une solution 100% personnalisée, pensée pour convaincre"
            }}
          </p>
        </div>

        <!-- Contenu principal -->
        <div class="p-3 sm:p-5 space-y-3 sm:space-y-4">
          <!-- Fonctionnalités principales par catégorie -->
          <div class="space-y-2 sm:space-y-4">
            <template
              v-for="(category, cIndex) in [
                'structure',
                'design',
                'technique',
                'support',
              ]"
              :key="`mobile-category-${index}-${cIndex}`"
            >
              <!-- En-tête de catégorie -->
              <div
                class="mb-2 sm:mb-3 pb-1 bg-gradient-to-r from-b3/20 to-transparent rounded-lg"
              >
                <div class="flex items-center p-2">
                  <div class="w-1 h-4 bg-b6 rounded-full mr-2"></div>
                  <h4 class="text-white font-semibold tracking-wide uppercase text-xs">
                    {{
                      category === "structure"
                        ? "Détails du forfait"
                        : category === "design"
                        ? "Design & UX"
                        : category === "technique"
                        ? "Aspects techniques"
                        : "Support & maintenance"
                    }}
                  </h4>
                </div>
              </div>

              <!-- Fonctionnalités de cette catégorie -->
              <div class="space-y-1.5 sm:space-y-2.5 mb-2 sm:mb-4">
                <div
                  v-for="(feature, fIndex) in commonFeatures.filter(
                    (f) => f.category === category
                  )"
                  :key="`mobile-feature-${index}-${category}-${fIndex}`"
                  class="flex items-center text-xs sm:text-sm"
                >
                  <div
                    v-if="typeof feature.values[index] === 'boolean'"
                    class="flex items-start"
                  >
                    <div class="flex-shrink-0 mr-2">
                      <svg
                        v-if="feature.values[index]"
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 sm:h-5 sm:w-5 text-b6 flex-shrink-0"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                      <svg
                        v-else
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 sm:h-5 sm:w-5 text-white/30 flex-shrink-0"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <span class="text-white/80 font-medium">{{ feature.text }}</span>
                    </div>
                  </div>
                  <div v-else class="flex items-start">
                    <div
                      class="h-5 w-5 mr-2 flex-shrink-0 flex items-center justify-center"
                    >
                      <div class="h-1.5 w-1.5 rounded-full bg-b3"></div>
                    </div>
                    <div>
                      <span class="text-white/80 font-medium">{{ feature.text }}:</span>
                      <span class="text-white ml-1">{{ feature.values[index] }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- Boutons d'action pour ce forfait -->
        <div class="px-3 sm:px-5 pb-4 sm:pb-5 pt-3 sm:pt-4 border-t border-white/10">
          <div class="flex justify-around">
            <!-- Bouton En savoir plus -->
            <div class="flex flex-col items-center pricing-en-savoir-plus">
              <a
                href="#faq"
                class="group relative w-12 h-12 sm:w-14 sm:h-14 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 transform hover:scale-110 cursor-pointer bg-gradient-to-br from-b2 to-b3 hover:shadow-b3/30 mb-1 sm:mb-2 pricing-en-savoir-plus"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 sm:h-6 sm:w-6 text-white relative pricing-en-savoir-plus"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </a>
              <span
                class="text-white/80 text-xs font-medium text-center pricing-en-savoir-plus"
                >En savoir plus</span
              >
            </div>

            <!-- Bouton Recevoir un devis -->
            <div class="flex flex-col items-center pricing-devis">
              <button
                @click="openContactModal"
                class="group relative w-12 h-12 sm:w-14 sm:h-14 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 transform hover:scale-110 cursor-pointer bg-gradient-to-br from-b3 to-b6 hover:shadow-b6/30 mb-1 sm:mb-2 pricing-devis"
              >
                <EnvelopeIcon
                  class="h-5 w-5 sm:h-6 sm:w-6 text-white relative pricing-devis"
                />
              </button>
              <span class="text-white/80 text-xs font-medium text-center pricing-devis"
                >Recevoir un devis</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Modale de contact -->
  <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
</template>

<script setup>
import { ref } from "vue";
import { EnvelopeIcon } from "@heroicons/vue/24/outline";
import ContactModal from "./ContactModal.vue";

// État de la modale de contact
const isContactModalOpen = ref(false);

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true;
};

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false;
};

// Fonctionnalités communes pour le tableau de comparaison
const commonFeatures = [
  // Détails du forfait
  {
    text: "Structure",
    values: [
      "1 page (type One-page)",
      "Jusqu'à 3 pages (Accueil, Services, À propos, etc.)",
      "Jusqu'à 6 pages personnalisées",
    ],
    category: "structure",
  },

  {
    text: "Contenu par page",
    values: [
      "5 sections max / page",
      "3 sections max / page",
      "6 sections max / page",
    ],
    category: "structure",
  },
  {
    text: "Navigation",
    values: [
      "Menu ancré (défilement sur la même page)",
      "Menu classique avec pages distinctes",
      "Navigation complète avec organisation poussée",
    ],
    category: "structure",
  },

  // Design et expérience utilisateur
  {
    text: "Design",
    values: [
      "Personnalisé à votre image",
      true,
      "Design entièrement adapté à votre activité et vos cibles",
    ],
    category: "design",
  },
  {
    text: "Affichage responsive",
    values: ["Mobile, tablette, ordinateur", true, true],
    category: "design",
  },
  {
    text: "Réseaux sociaux",
    values: ["Icônes cliquables", true, true],
    category: "design",
  },

  // Aspects techniques
  {
    text: "Hébergement & nom de domaine",
    values: [
      "Hébergement inclus sur Netlify (https://nom-entreprise.netlify.app)\nDomaine personnalisé en option",
      "Hébergement inclus + domaine personnalisé inclus (www.nom-entreprise.com)",
      "Hébergement complet + domaine personnalisé inclus (www.nom-entreprise.com)",
    ],
    category: "technique",
  },
  {
    text: "Référencement SEO",
    values: [
      "Balises de base (titres, descriptions, structure claire)",
      "Optimisation des titres, descriptions, structure + vitesse",
      "Optimisation des contenus, performances, mots-clés fournis",
    ],
    category: "technique",
  },

  // Support et maintenance
  {
    text: "Maintenance",
    values: [
      "1 mois inclus (fix bugs, mises à jour, optimisations)",
      "2 mois inclus",
      "3 mois inclus",
    ],
    category: "support",
  },
  {
    text: "Modifications incluses",
    values: [
      "3 changements mineurs pendant 1 mois",
      "6 changements mineurs pendant 2 mois",
      "Modifications illimitées pendant 3 mois",
    ],
    category: "support",
  },
];

// Plans de tarification
const plans = ref([
  {
    name: "Starter",
    price: "1350€",
    originalPrice: "1500€",
    promoText: "Valable sur les 3 prochaines commandes",
    buttonColor: "bg-b3 text-white",
    buttonText: "En savoir plus",
    includedPackage:
      "1 page (type One-page) avec 5 sections essentielles pour présenter votre activité",
    features: [
      { text: "Design personnalisé", included: true },
      { text: "Hébergement Netlify & configuration", included: true },
      {
        text: "Nom de domaine : gratuit (netlify.app) ou personnalisé (.com) en option",
        included: true,
      },
      { text: "Expérience optimale sur mobile, tablette et ordinateur", included: true },
      { text: "Optimisation SEO (balises et structure optimisées)", included: true },
      { text: "Liens vers les réseaux sociaux", included: true },
      { text: "1 mois de maintenance inclus", included: true },
      { text: "Jusqu'à 3 changements mineurs offerts (pendant 1 mois)", included: true },
    ],
  },
  {
    name: "Premium",
    price: "2250€",
    originalPrice: "2500€",
    promoText: "Valable sur les 2 prochaines commandes",
    isPopular: true,
    buttonColor: "bg-b1 text-white",
    buttonText: "En savoir plus",
    includedPackage:
      "Jusqu'à 3 pages (Accueil, Services, À propos, etc.) avec menu classique",
    features: [
      { text: "Design personnalisé", included: true },
      { text: "Hébergement Netlify & configuration", included: true },
      {
        text: "Nom de domaine www.nom-entreprise.com inclus selon disponibilité",
        included: true,
      },
      { text: "Expérience optimale sur mobile, tablette et ordinateur", included: true },
      { text: "Optimisation SEO avancée", included: true },
      { text: "Liens vers les réseaux sociaux", included: true },
      { text: "2 mois de maintenance inclus", included: true },
      { text: "Jusqu'à 6 changements mineurs offerts (pendant 2 mois)", included: true },
    ],
  },
  {
    name: "IMPACT PRO+",
    price: "Sur devis",
    gradient: "from-white via-white to-b1",
    buttonColor: "bg-b2",
    buttonText: "Recevoir un devis",
    includedPackage: "Jusqu'à 6 pages personnalisées",
    features: [
      { text: "Design entièrement sur-mesure", included: true },
      { text: "Hébergement Netlify & configuration avancée", included: true },
      {
        text: "Nom de domaine www.nom-entreprise.com inclus selon disponibilité",
        included: true,
      },
      {
        text: "Expérience optimale sur mobile, tablette et ordinateur",
        included: true,
      },
      {
        text: "Site 100% optimisé SEO (mots-clés, balises, performances)",
        included: true,
      },
      { text: "Liens vers les réseaux sociaux", included: true },
      {
        text: "3 mois de maintenance inclus",
        included: true,
      },
      {
        text: "Modifications mineures illimitées pendant 3 mois",
        included: true,
      },
    ],
  },
]);
</script>

<style scoped>
/* Effet de transition pour les cartes */
.transform {
  transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
}

/* Responsive design amélioré */
@media (max-width: 768px) {
  .grid-cols-1 > div {
    margin-bottom: 1rem;
  }

  /* Optimisation pour la version mobile */
  .mb-4 {
    margin-bottom: 0.75rem;
  }

  .mb-6 {
    margin-bottom: 1rem;
  }

  .mb-8 {
    margin-bottom: 1.5rem;
  }

  .p-5 {
    padding: 0.75rem;
  }

  .space-y-6 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }
}

/* Optimisation pour les très petits écrans */
@media (max-width: 370px) {
  .text-xs {
    font-size: 0.65rem;
  }

  .w-12 {
    width: 2.5rem;
  }

  .h-12 {
    height: 2.5rem;
  }

  .h-5,
  .w-5 {
    height: 1.1rem;
    width: 1.1rem;
  }
}
</style>
