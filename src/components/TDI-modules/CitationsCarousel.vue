<template>
  <section
    aria-label="Témoignages clients"
    class="relative w-full py-16 md:py-24 bg-b1 border-b border-black bg-gradient-to-t from-b2/10 to-b3 overflow-hidden"
  >
    <!-- Background pattern -->

    <!-- Fond avec motif et formes -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- Moti<PERSON> de grille -->
      <div
        class="absolute inset-0 opacity-5"
        style="
          background-size: 50px 50px;
          background-image: linear-gradient(
              to right,
              rgba(255, 255, 255, 0.1) 1px,
              transparent 1px
            ),
            linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
        "
      ></div>

      <!-- Formes décoratives -->
      <div
        class="absolute top-20 -left-20 w-96 h-96 bg-b3/10 rounded-full filter blur-3xl"
      ></div>
      <div
        class="absolute bottom-20 right-0 w-80 h-80 bg-b6/10 rounded-full filter blur-3xl"
      ></div>
      <div
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full"
      >
        <div
          class="absolute top-0 left-1/4 w-64 h-64 bg-b2/5 rounded-full filter blur-3xl"
        ></div>
        <div
          class="absolute bottom-1/4 right-1/3 w-48 h-48 bg-b4/5 rounded-full filter blur-3xl"
        ></div>
      </div>
    </div>

    <div class="container relative z-10 min-w-full overflow-hidden">
      <!-- Section header -->
      <header class="text-center mb-12 md:mb-16 relative px-3">
        <!-- Subtle glow effect behind title -->
        <div
          class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-b6/20 rounded-full blur-3xl opacity-30"
        ></div>

        <h2 class="text-2xl xs:text-3xl md:text-4xl font-bold text-white mb-4 relative">
          <span
            class="bg-gradient-to-r from-white via-b6 to-b4 text-transparent bg-clip-text"
            >Pourquoi investir dans un site sur-mesure ?</span
          >
        </h2>
        <p class="text-white/70 text-sm xs:text-base max-w-2xl mx-auto">
          Découvrez ce qui fait la différence entre un site standard et ce que je vous
          propose.
        </p>
      </header>

      <!-- Custom carousel -->
      <div class="relative carousel-container overflow-hidden">
        <Vue3Marquee pauseOnHover :duration="200" clone class="py-4 overflow-hidden">
          <div class="flex items-stretch overflow-visible">
            <!-- Spacer div to ensure proper spacing -->

            <div
              v-for="(citation, index) in citations"
              :key="index"
              class="w-80 md:w-96 flex-shrink-0 overflow-hidden shadow-xl transform transition-all duration-500 hover:shadow-b3/30 hover:-translate-y-1 citation-card"
              :class="`citation-card-${index % 3}`"
              style="margin-right: 1rem"
              itemscope
              itemtype="https://schema.org/Review"
            >
              <!-- Card header with quote icon -->
              <div class="p-5 md:p-6 flex flex-col h-full relative">
                <!-- Subtle accent in corner -->
                <div class="absolute top-0 right-0 w-16 h-16 opacity-10">
                  <div
                    class="absolute top-0 right-0 w-full h-full bg-gradient-to-bl from-white/20 to-transparent rounded-bl-[100px]"
                  ></div>
                </div>

                <div class="mb-4 relative">
                  <div
                    class="absolute -left-1 -top-1 w-10 h-10 bg-b6/10 rounded-full blur-md"
                  ></div>
                  <svg
                    class="w-8 h-8 text-b6/80 relative"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"
                    />
                  </svg>
                </div>

                <!-- Citation text -->
                <div class="flex-grow relative">
                  <p
                    class="text-white/90 text-base md:text-lg leading-relaxed font-medium mb-4"
                    itemprop="reviewBody"
                  >
                    {{ citation.text }}
                  </p>
                  <!-- Subtle quote mark in background -->
                  <div
                    class="absolute -right-2 bottom-0 text-white/5 text-8xl font-serif leading-none pointer-events-none opacity-30"
                  >
                    "
                  </div>
                </div>

                <!-- Key point highlight -->
                <div
                  class="mt-4 bg-gradient-to-r from-b3/20 to-b3/10 rounded-lg p-3 border-l-2 border-b6/40 relative overflow-hidden"
                >
                  <!-- Subtle accent -->
                  <div
                    class="absolute top-0 right-0 w-12 h-12 bg-white/5 rounded-bl-full"
                  ></div>
                  <p class="text-b6 text-sm font-semibold relative" itemprop="name">
                    {{
                      citation.highlight ||
                      "Un investissement rentable pour votre entreprise"
                    }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Vue3Marquee>
      </div>

      <!-- CTA ComeUp -->
      <div class="text-center mt-12 md:mt-16 citations-comeup">
        <a href="https://www.comeup.com/fr/@thedevimpact" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-3 xs:px-6 py-3 text-base font-medium rounded-lg text-gray-800 bg-gradient-to-r from-[#FEEF6C] to-b6 hover:from-[#FEEF6C]/90 hover:to-b6/90 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-b6/20 cursor-pointer citations-comeup">
          Retrouvez nos services sur COMEUP
          <svg class="ml-2 h-5 w-5 citations-comeup" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from "vue";

const citations = ref([
  {
    text: "Le temps que j'investis rend chaque projet unique et performant.",
    highlight: "Un site qui devient un véritable levier de croissance",
  },
  {
    text: "Chaque détail compte dans une première impression réussie.",
    highlight: "Une première impression qui fait toute la différence",
  },
  {
    text: "Créer pour durer prend du temps, mais c’est un vrai investissement.",
    highlight: "Un investissement durable plutôt qu'une solution éphémère",
  },
  {
    text: "Le travail standardisé se voit. Le sur-mesure vous distingue.",
    highlight: "La qualité qui vous démarque de la concurrence",
  },
  {
    text: "Ce n’est pas le prix, mais l’expertise qui crée la vraie valeur.",
    highlight: "L'expertise qui transforme votre site en outil de réussite",
  },
  {
    text: "Je crée des sites qui vous ressemblent et qui convertissent.",
    highlight: "Un site qui convertit vos visiteurs en clients fidèles",
  },
]);
</script>

<style scoped>
/* Animation subtile pour les cartes */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px); /* Réduit l'amplitude de l'animation */
  }
  100% {
    transform: translateY(0px);
  }
}

/* Variations de couleurs pour les cartes */
.citation-card {
  animation: float 6s ease-in-out infinite;
  height: 280px; /* Hauteur fixe pour toutes les cartes */
  display: flex;
  flex-direction: column;
  margin-top: 10px; /* Espace pour l'animation sans débordement */
  margin-bottom: 10px; /* Espace pour l'animation sans débordement */
}

.citation-card-0 {
  border-top: 1px solid rgba(var(--color-b3-rgb), 0.3);
  animation-delay: 0s;
}

.citation-card-1 {
  border-top: 1px solid rgba(var(--color-b6-rgb), 0.3);
  animation-delay: 2s;
}

.citation-card-2 {
  border-top: 1px solid rgba(var(--color-b4-rgb), 0.3);
  animation-delay: 4s;
}

.hover\:shadow-b3\/30:hover {
  box-shadow: 0 10px 25px -5px rgba(var(--color-b3-rgb), 0.3),
    0 8px 10px -6px rgba(var(--color-b3-rgb), 0.2);
}

.carousel-container {
  overflow: hidden;
  width: 100%;
  height: auto;
  padding: 10px 0; /* Espace supplémentaire pour l'animation */
}
</style>
