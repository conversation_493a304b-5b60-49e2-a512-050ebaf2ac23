<template>
  <div class="min-h-screen bg-white">
    
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-slate-200/50 sticky top-0 z-50">
      <div class="container mx-auto px-6 lg:px-8">
        <div class="flex items-center justify-between h-20">
          <!-- Logo -->
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-b3 to-b6 rounded-xl flex items-center justify-center mr-3">
              <span class="text-white font-bold text-lg">TD</span>
            </div>
            <span class="text-2xl font-black text-slate-900">TheDevImpact</span>
          </div>
          
          <!-- Navigation -->
          <nav class="hidden md:flex items-center space-x-8">
            <a href="#accueil" class="text-slate-700 hover:text-b3 font-semibold transition-colors">Accueil</a>
            <a href="#collections" class="text-slate-700 hover:text-b3 font-semibold transition-colors">Collections</a>
            <a href="#about" class="text-slate-700 hover:text-b3 font-semibold transition-colors">À propos</a>
            <a href="#contact" class="text-slate-700 hover:text-b3 font-semibold transition-colors">Contact</a>
          </nav>
          
          <!-- Actions -->
          <div class="flex items-center space-x-4">
            <button class="p-2 text-slate-600 hover:text-b3 transition-colors">
              <i class="fas fa-search text-lg"></i>
            </button>
            <button class="relative p-2 text-slate-600 hover:text-b3 transition-colors">
              <i class="fas fa-shopping-cart text-lg"></i>
              <span class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-b3 to-b6 rounded-full flex items-center justify-center text-white text-xs font-bold">3</span>
            </button>
            <button class="md:hidden p-2 text-slate-600">
              <i class="fas fa-bars text-lg"></i>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section id="accueil" class="relative py-20 lg:py-32 bg-gradient-to-br from-slate-50 to-white overflow-hidden">
      <!-- Formes décoratives -->
      <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-b3/5 to-b6/5 rounded-full blur-3xl"></div>
        <div class="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-b4/5 to-b5/5 rounded-full blur-3xl"></div>
      </div>
      
      <div class="container mx-auto px-6 lg:px-8 relative z-10">
        <div class="grid lg:grid-cols-2 gap-16 items-center">
          
          <!-- Contenu -->
          <div class="space-y-8">
            <div class="inline-flex items-center px-4 py-2 bg-b3/10 rounded-full border border-b3/20">
              <div class="w-2 h-2 bg-b3 rounded-full mr-3 animate-pulse"></div>
              <span class="text-b3 font-semibold text-sm uppercase tracking-wider">Nouveau Collection</span>
            </div>
            
            <h1 class="text-5xl lg:text-7xl font-black text-slate-900 leading-tight">
              Sites Web
              <span class="bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text block">
                Exceptionnels
              </span>
            </h1>
            
            <p class="text-xl text-slate-600 leading-relaxed max-w-lg">
              Découvrez notre collection de sites web premium, conçus avec passion pour sublimer votre présence digitale et captiver vos visiteurs.
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4">
              <button class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-b3 to-b6 text-white font-bold rounded-2xl hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                <span>Voir les produits</span>
                <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform"></i>
              </button>
              
              <button class="inline-flex items-center px-8 py-4 bg-white text-slate-700 font-bold rounded-2xl border-2 border-slate-200 hover:border-b3 hover:shadow-lg transition-all duration-300">
                <i class="fas fa-play mr-3"></i>
                Voir la démo
              </button>
            </div>
            
            <!-- Stats -->
            <div class="flex items-center gap-8 pt-8">
              <div class="text-center">
                <div class="text-3xl font-black text-slate-900">500+</div>
                <div class="text-sm text-slate-500 font-semibold">Sites créés</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-black bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">98%</div>
                <div class="text-sm text-slate-500 font-semibold">Satisfaction</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-black text-slate-900">24h</div>
                <div class="text-sm text-slate-500 font-semibold">Livraison</div>
              </div>
            </div>
          </div>
          
          <!-- Visuel -->
          <div class="relative">
            <div class="relative bg-white rounded-3xl shadow-2xl border border-slate-200/50 p-8 transform rotate-2 hover:rotate-0 transition-transform duration-500">
              <div class="aspect-[4/3] bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl flex items-center justify-center">
                <div class="text-center">
                  <div class="w-20 h-20 bg-gradient-to-br from-b3 to-b6 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-laptop-code text-3xl text-white"></i>
                  </div>
                  <p class="text-slate-600 font-semibold">Aperçu Site Premium</p>
                </div>
              </div>
            </div>
            
            <!-- Éléments flottants -->
            <div class="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-b4 to-b5 rounded-2xl flex items-center justify-center shadow-lg">
              <i class="fas fa-star text-2xl text-white"></i>
            </div>
            
            <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-b6 to-b3 rounded-xl flex items-center justify-center shadow-lg">
              <i class="fas fa-heart text-xl text-white"></i>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Collections Section -->
    <section id="collections" class="py-20 lg:py-32 bg-white">
      <div class="container mx-auto px-6 lg:px-8">
        
        <!-- Header -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-4 py-2 bg-b3/10 rounded-full border border-b3/20 mb-8">
            <span class="text-b3 font-semibold text-sm uppercase tracking-wider">Nos Collections</span>
          </div>
          
          <h2 class="text-4xl lg:text-5xl font-black text-slate-900 mb-6">
            Explorez nos 
            <span class="bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">catégories</span>
          </h2>
          
          <p class="text-xl text-slate-600 max-w-3xl mx-auto">
            Chaque collection est soigneusement conçue pour répondre aux besoins spécifiques de votre secteur d'activité
          </p>
        </div>

        <!-- Grid Collections -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          <!-- Collection 1 -->
          <div class="group relative bg-white rounded-3xl shadow-lg border border-slate-200/50 overflow-hidden hover:shadow-xl transition-all duration-500">
            <div class="aspect-[4/3] bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
              <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
                <i class="fas fa-briefcase text-3xl text-white"></i>
              </div>
            </div>
            
            <div class="p-8">
              <h3 class="text-2xl font-bold text-slate-900 mb-3">Business</h3>
              <p class="text-slate-600 mb-6">Sites professionnels pour entreprises et startups modernes</p>
              
              <div class="flex items-center justify-between">
                <span class="text-sm font-semibold text-slate-500">12 modèles</span>
                <button class="text-b3 font-semibold hover:text-b6 transition-colors">
                  Découvrir <i class="fas fa-arrow-right ml-1"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Collection 2 -->
          <div class="group relative bg-white rounded-3xl shadow-lg border border-slate-200/50 overflow-hidden hover:shadow-xl transition-all duration-500">
            <div class="aspect-[4/3] bg-gradient-to-br from-purple-50 to-purple-100 flex items-center justify-center">
              <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center">
                <i class="fas fa-palette text-3xl text-white"></i>
              </div>
            </div>
            
            <div class="p-8">
              <h3 class="text-2xl font-bold text-slate-900 mb-3">Créatif</h3>
              <p class="text-slate-600 mb-6">Portfolios et sites artistiques pour créateurs passionnés</p>
              
              <div class="flex items-center justify-between">
                <span class="text-sm font-semibold text-slate-500">8 modèles</span>
                <button class="text-b3 font-semibold hover:text-b6 transition-colors">
                  Découvrir <i class="fas fa-arrow-right ml-1"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Collection 3 -->
          <div class="group relative bg-white rounded-3xl shadow-lg border border-slate-200/50 overflow-hidden hover:shadow-xl transition-all duration-500">
            <div class="aspect-[4/3] bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center">
              <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
                <i class="fas fa-shopping-bag text-3xl text-white"></i>
              </div>
            </div>
            
            <div class="p-8">
              <h3 class="text-2xl font-bold text-slate-900 mb-3">E-commerce</h3>
              <p class="text-slate-600 mb-6">Boutiques en ligne optimisées pour maximiser vos ventes</p>
              
              <div class="flex items-center justify-between">
                <span class="text-sm font-semibold text-slate-500">15 modèles</span>
                <button class="text-b3 font-semibold hover:text-b6 transition-colors">
                  Découvrir <i class="fas fa-arrow-right ml-1"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Promotion Section -->
    <section class="py-20 bg-gradient-to-r from-b3 to-b6 relative overflow-hidden">
      <!-- Formes décoratives -->
      <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-40 h-40 bg-white/10 rounded-full blur-2xl"></div>
        <div class="absolute bottom-10 right-10 w-60 h-60 bg-white/5 rounded-full blur-3xl"></div>
      </div>
      
      <div class="container mx-auto px-6 lg:px-8 relative z-10">
        <div class="text-center">
          <div class="inline-flex items-center px-4 py-2 bg-white/20 rounded-full border border-white/30 mb-8">
            <i class="fas fa-fire text-white mr-2"></i>
            <span class="text-white font-semibold text-sm uppercase tracking-wider">Offre Limitée</span>
          </div>
          
          <h2 class="text-4xl lg:text-5xl font-black text-white mb-6">
            -30% sur votre premier site
          </h2>
          
          <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Profitez de notre offre de lancement et créez votre présence digitale avec un site web professionnel à prix réduit
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <button class="inline-flex items-center px-8 py-4 bg-white text-b3 font-bold rounded-2xl hover:shadow-xl transform hover:scale-105 transition-all duration-300">
              <i class="fas fa-tag mr-3"></i>
              Profiter de l'offre
            </button>
            
            <div class="flex items-center text-white/90">
              <i class="fas fa-clock mr-2"></i>
              <span class="font-semibold">Offre valable jusqu'au 31 décembre</span>
            </div>
          </div>
          
          <!-- Countdown -->
          <div class="flex justify-center gap-4">
            <div class="bg-white/20 rounded-2xl p-4 backdrop-blur-sm">
              <div class="text-2xl font-black text-white">15</div>
              <div class="text-sm text-white/80">Jours</div>
            </div>
            <div class="bg-white/20 rounded-2xl p-4 backdrop-blur-sm">
              <div class="text-2xl font-black text-white">08</div>
              <div class="text-sm text-white/80">Heures</div>
            </div>
            <div class="bg-white/20 rounded-2xl p-4 backdrop-blur-sm">
              <div class="text-2xl font-black text-white">42</div>
              <div class="text-sm text-white/80">Minutes</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 lg:py-32 bg-gradient-to-br from-slate-50 to-white">
      <div class="container mx-auto px-6 lg:px-8">
        <div class="grid lg:grid-cols-2 gap-16 items-center">
          
          <!-- Contenu -->
          <div class="space-y-8">
            <div class="inline-flex items-center px-4 py-2 bg-b3/10 rounded-full border border-b3/20">
              <span class="text-b3 font-semibold text-sm uppercase tracking-wider">Notre Mission</span>
            </div>
            
            <h2 class="text-4xl lg:text-5xl font-black text-slate-900 leading-tight">
              Créer des sites web qui 
              <span class="bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">
                marquent les esprits
              </span>
            </h2>
            
            <div class="space-y-6 text-lg text-slate-600 leading-relaxed">
              <p>
                Chez TheDevImpact, nous croyons que chaque entreprise mérite une présence digitale exceptionnelle. 
                Notre passion pour le design et la technologie nous pousse à créer des sites web qui ne se contentent 
                pas d'être beaux, mais qui convertissent réellement.
              </p>
              
              <p>
                Chaque modèle de notre collection est pensé pour offrir une expérience utilisateur optimale, 
                tout en reflétant l'identité unique de votre marque. Nous combinons créativité, performance 
                et facilité d'utilisation pour vous donner l'avantage concurrentiel que vous méritez.
              </p>
            </div>
            
            <div class="flex items-center gap-8">
              <div class="text-center">
                <div class="text-3xl font-black text-slate-900">5+</div>
                <div class="text-sm text-slate-500 font-semibold">Années d'expérience</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-black bg-gradient-to-r from-b3 to-b6 text-transparent bg-clip-text">500+</div>
                <div class="text-sm text-slate-500 font-semibold">Projets réalisés</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-black text-slate-900">24/7</div>
                <div class="text-sm text-slate-500 font-semibold">Support client</div>
              </div>
            </div>
          </div>
          
          <!-- Visuel -->
          <div class="relative">
            <div class="aspect-square bg-gradient-to-br from-b3/10 to-b6/10 rounded-3xl flex items-center justify-center">
              <div class="text-center">
                <div class="w-32 h-32 bg-gradient-to-br from-b3 to-b6 rounded-3xl flex items-center justify-center mx-auto mb-6">
                  <i class="fas fa-rocket text-5xl text-white"></i>
                </div>
                <p class="text-2xl font-bold text-slate-700">Innovation & Excellence</p>
              </div>
            </div>
            
            <!-- Badges flottants -->
            <div class="absolute -top-4 -right-4 bg-white rounded-2xl shadow-lg p-4 border border-slate-200/50">
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-sm font-semibold text-slate-700">En ligne</span>
              </div>
            </div>
            
            <div class="absolute -bottom-4 -left-4 bg-white rounded-2xl shadow-lg p-4 border border-slate-200/50">
              <div class="flex items-center gap-2">
                <i class="fas fa-shield-alt text-b3"></i>
                <span class="text-sm font-semibold text-slate-700">Sécurisé</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-slate-900 text-white py-16">
      <div class="container mx-auto px-6 lg:px-8">
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          
          <!-- Logo & Description -->
          <div class="lg:col-span-2">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-b3 to-b6 rounded-xl flex items-center justify-center mr-4">
                <span class="text-white font-bold text-xl">TD</span>
              </div>
              <span class="text-3xl font-black">TheDevImpact</span>
            </div>
            
            <p class="text-slate-300 text-lg leading-relaxed mb-8 max-w-md">
              Créez une présence digitale exceptionnelle avec nos sites web premium. 
              Design moderne, performance optimale, satisfaction garantie.
            </p>
            
            <!-- Réseaux sociaux -->
            <div class="flex gap-4">
              <a href="#" class="w-12 h-12 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-b3 transition-colors">
                <i class="fab fa-facebook-f text-lg"></i>
              </a>
              <a href="#" class="w-12 h-12 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-b3 transition-colors">
                <i class="fab fa-twitter text-lg"></i>
              </a>
              <a href="#" class="w-12 h-12 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-b3 transition-colors">
                <i class="fab fa-instagram text-lg"></i>
              </a>
              <a href="#" class="w-12 h-12 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-b3 transition-colors">
                <i class="fab fa-linkedin-in text-lg"></i>
              </a>
            </div>
          </div>
          
          <!-- Liens rapides -->
          <div>
            <h3 class="text-xl font-bold mb-6">Liens rapides</h3>
            <ul class="space-y-4">
              <li><a href="#" class="text-slate-300 hover:text-white transition-colors">Accueil</a></li>
              <li><a href="#" class="text-slate-300 hover:text-white transition-colors">Collections</a></li>
              <li><a href="#" class="text-slate-300 hover:text-white transition-colors">À propos</a></li>
              <li><a href="#" class="text-slate-300 hover:text-white transition-colors">Blog</a></li>
              <li><a href="#" class="text-slate-300 hover:text-white transition-colors">Contact</a></li>
            </ul>
          </div>
          
          <!-- Contact -->
          <div>
            <h3 class="text-xl font-bold mb-6">Contact</h3>
            <ul class="space-y-4">
              <li class="flex items-center">
                <i class="fas fa-envelope w-5 mr-3 text-b3"></i>
                <span class="text-slate-300"><EMAIL></span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-phone w-5 mr-3 text-b3"></i>
                <span class="text-slate-300">+33 1 23 45 67 89</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt w-5 mr-3 text-b3"></i>
                <span class="text-slate-300">Paris, France</span>
              </li>
            </ul>
          </div>
        </div>
        
        <!-- Bottom -->
        <div class="border-t border-slate-800 pt-8">
          <div class="flex flex-col md:flex-row justify-between items-center gap-4">
            <p class="text-slate-400">
              © 2024 TheDevImpact. Tous droits réservés.
            </p>
            
            <div class="flex gap-6">
              <a href="#" class="text-slate-400 hover:text-white transition-colors text-sm">
                Mentions légales
              </a>
              <a href="#" class="text-slate-400 hover:text-white transition-colors text-sm">
                Politique de confidentialité
              </a>
              <a href="#" class="text-slate-400 hover:text-white transition-colors text-sm">
                CGV
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Réactivité pour les animations
const isVisible = ref(false)

onMounted(() => {
  isVisible.value = true
})
</script>

<style scoped>
/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

/* Effet de dégradé pour les textes */
.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Transitions fluides */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
  transition: color 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* Hover effects */
.group:hover .group-hover\:translate-x-1 {
  transform: translateX(0.25rem);
}

/* Effet de blur pour les formes décoratives */
.blur-3xl {
  filter: blur(64px);
}

.blur-2xl {
  filter: blur(40px);
}

/* Responsive */
@media (max-width: 768px) {
  .text-5xl {
    font-size: 2.5rem;
  }
  
  .text-7xl {
    font-size: 3.5rem;
  }
}

/* Smooth scroll */
html {
  scroll-behavior: smooth;
}

/* Backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(8px);
}
</style> 