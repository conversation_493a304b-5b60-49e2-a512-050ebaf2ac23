<script setup>
import { onMounted, ref } from 'vue';
import { SparklesIcon } from '@heroicons/vue/24/outline';
import ContactModal from "./ContactModal.vue";

// Références pour la vidéo
const videoRef = ref(null);
const playbackSpeed = 0.5; // Vitesse réduite pour un effet plus élégant
const videoLoaded = ref(false);
const videoError = ref(false);

// Fonction pour gérer le chargement de la vidéo
const handleVideoLoaded = () => {
  // S'assurer que la vitesse de lecture est bien appliquée
  if (videoRef.value) {
    videoRef.value.playbackRate = playbackSpeed;
  }
  videoLoaded.value = true;
};

// Fonction pour gérer les erreurs de vidéo
const handleVideoError = () => {
  videoError.value = true;
};

// Animation du texte
const textVisible = ref(false);
const ctaVisible = ref(false);

// État de la modale de contact
const isContactModalOpen = ref(false);

// Ouvrir la modale de contact
const openContactModal = () => {
  isContactModalOpen.value = true;
};

// Fermer la modale de contact
const closeContactModal = () => {
  isContactModalOpen.value = false;
};

onMounted(() => {
  // Configuration de la vidéo (ANCIEN)
  if (videoRef.value) {
    videoRef.value.playbackRate = playbackSpeed;

    // Précharger la vidéo
    videoRef.value.load();

    // Écouteurs d'événements pour la vidéo
    videoRef.value.addEventListener('loadeddata', handleVideoLoaded);
    videoRef.value.addEventListener('play', () => {
      videoRef.value.playbackRate = playbackSpeed;
    });
    videoRef.value.addEventListener('error', handleVideoError);

    // Forcer le démarrage de la vidéo
    const playPromise = videoRef.value.play();
    if (playPromise !== undefined) {
      playPromise.catch(() => {
        // Auto-play a été bloqué, marquer comme erreur
        videoError.value = true;
      });
    }
  }

  // Animation séquentielle des éléments
  setTimeout(() => { textVisible.value = true; }, 180);
  setTimeout(() => { ctaVisible.value = true; }, 300);
});
</script>

<template>
  <section
    id="accueil"
    aria-label="Accueil et présentation de TheDevImpact"
    class="relative overflow-hidden bg-b1 min-h-screen flex items-center justify-center pt-16 sm:pt-20"
    style="min-height: calc(100vh - 0px);"
  >
    <!-- Conteneur pour la vidéo et le fond de secours (ANCIEN) -->
    <div class="absolute inset-0">
      <!-- Fond de secours (gradient) qui s'affiche pendant le chargement de la vidéo -->
      <div class="absolute inset-0 bg-gradient-to-br from-b1 via-b3/20 to-b1 opacity-90"></div>

      <!-- Vidéo d'arrière-plan -->
      <video
        ref="videoRef"
        autoplay
        loop
        muted
        playsinline
        preload="auto"
        @loadeddata="handleVideoLoaded"
        @play="$event.target.playbackRate = playbackSpeed"
        class="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-1000" :class="{ 'opacity-90': videoLoaded }"
        aria-label="Vidéo d'arrière-plan montrant le développement web"
        title="Développement web professionnel"
      >
        <source src="../../assets/devwebb.mp4" type="video/mp4" />
        <track kind="captions" src="../../assets/captions-fr.vtt" srclang="fr" label="Français" />
        Your browser does not support the video tag.
      </video>
    </div>

    <!-- Overlay avec motif subtil (ANCIEN) -->
    <div class="absolute inset-0 bg-gradient-to-b from-b1/50 to-b1/80 z-0">
      <!-- Motif de points -->
      <div class="absolute inset-0 opacity-5"
           style="background-size: 30px 30px; background-image: radial-gradient(circle, rgba(255,255,255,0.4) 1px, transparent 1px);">
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="relative z-10 w-full px-3 sm:px-6 lg:px-8">
      <div class="max-w-7xl w-full mx-auto" role="banner">
        <!-- Section titre avec animation -->
        <div class="text-center max-w-4xl mx-auto mt-4 sm:mt-8 lg:mt-12">
          <!-- Badge modernisé -->
          <div class="inline-flex items-center px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 rounded-full bg-gradient-to-r from-b3/20 to-b6/20 border border-b3/30 backdrop-blur-sm mb-4 sm:mb-6 lg:mb-8 opacity-0" :class="{ 'animate-fade-in': textVisible }">
            <SparklesIcon class="h-3 sm:h-4 w-3 sm:w-4 text-b3 mr-1.5 sm:mr-2" />
            <span class="text-b3 text-xs sm:text-sm font-medium">Créatrice de sites vitrines & artisanaux</span>
          </div>

          <!-- Titre principal simplifié -->
          <h1 class="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight mb-4 sm:mb-6 lg:mb-8 opacity-0 leading-tight" :class="{ 'animate-fade-in-delayed': textVisible }">
            <span class="block text-white">Sites web</span>
            <span class="block mt-1 sm:mt-2 bg-gradient-to-r from-b3 via-b4 to-b6 text-transparent bg-clip-text">
              uniques
            </span>
            <span class="block mt-1 sm:mt-2 text-white">à haute valeur esthétique</span>
          </h1>

          <!-- Texte de présentation -->
          <p class="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-white/90 max-w-3xl mx-auto mb-6 sm:mb-8 lg:mb-12 opacity-0 px-2" :class="{ 'animate-fade-in-delayed': textVisible }">
            Chaque création web est pensée comme une <span class="gradient-text font-semibold">œuvre d'art digitale</span>,
            alliant beauté et performance pour révéler l'essence de votre marque.
          </p>

          <!-- CTA principal -->
          <div class="opacity-0 px-2" :class="{ 'animate-fade-in-delayed-more': ctaVisible }">
            <a href="#deux-choix" class="inline-flex items-center justify-center px-4 sm:px-6 lg:px-8 py-3 sm:py-4 bg-gradient-to-r from-b3 to-b6 text-white text-sm sm:text-base lg:text-lg font-medium rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 w-full sm:w-auto max-w-sm mx-auto">
              <span class="text-center">Découvrir notre Univers Créatif</span>
              <svg class="ml-2 h-4 sm:h-5 w-4 sm:w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Modale de contact -->
  <ContactModal :is-open="isContactModalOpen" @close="closeContactModal" />
</template>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.6s ease-out 0.3s forwards;
}

.animate-fade-in-delayed-more {
  animation: fadeIn 0.6s ease-out 0.6s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Glass morphism effect (pour la card boutique) */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hover effects pour la card boutique */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--color-b3), var(--color-b4), var(--color-b6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
</style>
