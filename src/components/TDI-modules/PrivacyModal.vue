<template>
  <!-- <PERSON>dale de la politique de confidentialité -->
  <div
    v-if="isOpen"
    class="fixed inset-0 z-[9999] overflow-y-auto bg-gradient-to-br from-b1 via-b2/99 to-b2/95"
    role="dialog"
    aria-modal="true"
    aria-labelledby="privacy-modal-title"
  >
    <div class="flex items-start justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-black opacity-75"></div>
      </div>

      <!-- Conteneur de la modale -->
      <div class="inline-block align-bottom bg-white/5 backdrop-blur-md rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl w-full">
        <!-- Bouton de fermeture accessible -->
        <button
          @click="closeModal"
          class="absolute top-2 sm:top-4 right-2 sm:right-4 text-white bg-b3/30 hover:bg-b3/50 hover:scale-110 p-2 rounded-full transition-all duration-300 z-[10000] cursor-pointer"
          aria-label="Fermer la politique de confidentialité"
          title="Fermer la politique de confidentialité"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        <div class="p-6 sm:p-8">
          <!-- En-tête -->
          <div class="text-center mb-6">
            <h2 id="privacy-modal-title" class="text-2xl font-bold text-white mb-2">Politique de confidentialité</h2>
            <div class="w-16 h-1 bg-gradient-to-r from-b3 to-b6 mx-auto rounded-full"></div>
            <p class="text-white/70 text-sm mt-2">Dernière mise à jour : 10 juillet 2025</p>
          </div>

          <!-- Contenu de la politique de confidentialité -->
          <div class="text-white/90 space-y-6 text-sm sm:text-base">
            <p>
              La présente politique de confidentialité vous informe sur la manière dont The Dev Impact, activité hébergée juridiquement par la coopérative Coopilote, collecte et traite vos données personnelles dans le cadre de ses prestations de création de sites web et de supports marketing digitaux.
            </p>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">1. Responsable du traitement</h3>
              <p>Le responsable du traitement est :</p>
              <div class="bg-white/5 p-4 rounded-lg mt-2">
                <p class="font-semibold">The Dev Impact, activité portée par</p>
                <p class="mt-1">Coopilote – SCOP SA à capital variable</p>
                <p class="mt-1">RCS Belfort 452 207 077</p>
                <p class="mt-1">SIRET : 44298140300086</p>
                <p class="mt-2">Siège social : 13 avenue Léon Blum, 25200 Montbéliard</p>
                <p class="mt-1">Activité exercée au sein de l'établissement Coopilote Besançon – 7 Rue Alfred de Vigny, 25000 Besançon</p>
                <p class="mt-2">E-mail : <EMAIL></p>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">2. Données personnelles collectées</h3>
              <p>
                Les données collectées sont exclusivement celles que vous transmettez volontairement via le formulaire de contact ou par e-mail :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Nom / prénom</li>
                <li>Adresse e-mail</li>
                <li>Objet de la demande</li>
                <li>Contenu de votre message</li>
                <li>Toute autre information communiquée spontanément</li>
              </ul>
              <p class="mt-2 font-medium">
                Aucun cookie ni traceur publicitaire n'est utilisé sur ce site.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">3. Finalités du traitement</h3>
              <p>
                Les données sont collectées pour les finalités suivantes :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Répondre à vos demandes (devis, informations, contact)</li>
                <li>Suivre la relation client ou commerciale</li>
                <li>Exécuter les prestations convenues</li>
                <li>Respecter les obligations comptables et fiscales de la coopérative Coopilote</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">4. Base légale</h3>
              <p>
                Le traitement est fondé sur :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Votre consentement (formulaire de contact)</li>
                <li>L'exécution de mesures précontractuelles ou contractuelles</li>
                <li>Le respect d'obligations légales (ex. : conservation comptable)</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">5. Destinataires des données</h3>
              <p>
                Vos données sont traitées exclusivement par The Dev Impact dans le cadre de son activité.
                Certaines informations peuvent être accessibles à la coopérative Coopilote, en charge de la gestion administrative et comptable.
              </p>
              <p class="mt-2">
                Aucune donnée n'est vendue, louée ni transmise à des tiers sans votre consentement, sauf obligation légale.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">6. Durée de conservation</h3>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>12 mois pour les demandes sans suite commerciale</li>
                <li>Jusqu'à 5 ans pour les échanges ayant abouti à une prestation (conformément aux obligations légales de Coopilote)</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">7. Sécurité des données</h3>
              <p>
                Des mesures de sécurité appropriées (chiffrement, accès restreint, sécurisation des e-mails) sont mises en œuvre pour protéger vos données.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">8. Vos droits</h3>
              <p>
                Conformément au RGPD, vous disposez des droits suivants :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Droit d'accès, de rectification, d'effacement</li>
                <li>Droit d'opposition ou de limitation du traitement</li>
                <li>Droit à la portabilité</li>
                <li>Droit de retirer votre consentement à tout moment</li>
              </ul>
              <p class="mt-2">
                Pour exercer vos droits, contactez :
              </p>
              <div class="bg-white/5 p-3 rounded-lg mt-2">
                <p>📩 <EMAIL></p>
                <p class="mt-1">ou écrivez à Coopilote à l'adresse postale indiquée.</p>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">9. Réclamation</h3>
              <p>
                En cas de désaccord, vous pouvez introduire une réclamation auprès de la CNIL : www.cnil.fr
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">10. Modification de la politique</h3>
              <p>
                Cette politique peut être modifiée à tout moment. La version en vigueur est celle disponible sur ce site à la date de votre consultation.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  isOpen: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits(['close']);

const closeModal = () => {
  emit('close');
};
</script>

<style scoped>
/* Style pour les liens */
a {
  text-decoration: none;
  transition: all 0.3s ease;
}

/* Style pour les éléments cliquables */
button, 
a, 
[role="button"],
.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}
</style>
