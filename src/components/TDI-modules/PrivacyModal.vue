<template>
  <!-- <PERSON><PERSON> de la politique de confidentialité -->
  <div
    v-if="isOpen"
    class="fixed inset-0 z-90 overflow-y-auto bg-gradient-to-br from-b1 via-b2/99 to-b2/95"
    role="dialog"
    aria-modal="true"
    aria-labelledby="privacy-modal-title"
  >
    <div class="flex items-start justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-black opacity-75"></div>
      </div>

      <!-- Conteneur de la modale -->
      <div class="inline-block align-bottom bg-white/5 backdrop-blur-md rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl w-full">
        <!-- Bouton de fermeture accessible -->
        <button
          @click="closeModal"
          class="absolute top-2 sm:top-4 right-2 sm:right-4 text-white bg-b3/30 hover:bg-b3/50 hover:scale-110 p-2 rounded-full transition-all duration-300 z-50 cursor-pointer"
          aria-label="Fermer la politique de confidentialité"
          title="Fermer la politique de confidentialité"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        <div class="p-6 sm:p-8">
          <!-- En-tête -->
          <div class="text-center mb-6">
            <h2 id="privacy-modal-title" class="text-2xl font-bold text-white mb-2">Politique de confidentialité</h2>
            <div class="w-16 h-1 bg-gradient-to-r from-b3 to-b6 mx-auto rounded-full"></div>
            <p class="text-white/70 text-sm mt-2">Dernière mise à jour : {{ currentDate }}</p>
          </div>

          <!-- Contenu de la politique de confidentialité -->
          <div class="text-white/90 space-y-6 text-sm sm:text-base">
            <p>
              La présente politique de confidentialité vise à vous informer sur la manière dont The Dev Impact collecte, utilise et protège vos données personnelles dans le cadre de son activité de création de sites web et de supports digitaux.
            </p>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">1. Identité du responsable de traitement</h3>
              <div class="bg-white/5 p-4 rounded-lg">
                <p>The Dev Impact</p>
                <p>Adresse : sur demande</p>
                <p>E-mail : <EMAIL></p>
                <p>Entreprise en cours d'immatriculation</p>
              </div>
              <p class="mt-2">
                Le responsable du traitement est la personne en charge de l'exploitation du site www.thedevimpact.com.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">2. Données personnelles collectées</h3>
              <p>
                Le site ne collecte aucune donnée personnelle automatiquement (pas de cookies ni traceurs).
              </p>
              <p class="mt-2">
                Les seules données personnelles collectées sont celles que vous transmettez volontairement via le formulaire de contact :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Nom / prénom</li>
                <li>Adresse e-mail</li>
                <li>Toute autre information que vous choisissez de transmettre dans votre message</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">3. Finalité de la collecte</h3>
              <p>
                Ces données sont collectées exclusivement afin de :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Répondre à vos demandes de contact ou devis</li>
                <li>Assurer le suivi commercial ou la relation client</li>
                <li>Gérer les prestations convenues</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">4. Base légale du traitement</h3>
              <p>
                Les traitements de données reposent sur :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Votre consentement (envoi volontaire via le formulaire)</li>
                <li>L'exécution précontractuelle (devis, demandes d'informations)</li>
                <li>Ou l'exécution d'un contrat (prestation de service)</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">5. Destinataires des données</h3>
              <p>
                Les données sont exclusivement traitées par The Dev Impact.
                Elles ne sont jamais revendues, échangées ni transmises à des tiers, sauf obligation légale ou judiciaire.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">6. Durée de conservation</h3>
              <p>
                Les données sont conservées :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>12 mois en cas de simple contact sans suite commerciale</li>
                <li>Jusqu'à 5 ans pour les clients ou prospects ayant donné suite à une prestation (obligations comptables, preuve de contrat, etc.)</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">7. Sécurité des données</h3>
              <p>
                Des mesures techniques et organisationnelles sont mises en place pour protéger vos données contre tout accès non autorisé, perte, destruction ou divulgation.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">8. Vos droits</h3>
              <p>
                Conformément au RGPD (Règlement Général sur la Protection des Données), vous disposez des droits suivants :
              </p>
              <ul class="list-disc pl-5 mt-2 space-y-1">
                <li>Droit d'accès à vos données</li>
                <li>Droit de rectification ou d'effacement</li>
                <li>Droit à la limitation ou à l'opposition au traitement</li>
                <li>Droit à la portabilité des données</li>
                <li>Droit de retirer votre consentement à tout moment</li>
              </ul>
              <p class="mt-2">
                Pour exercer vos droits, contactez : <EMAIL>
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">9. Réclamation</h3>
              <p>
                Si vous estimez que vos droits ne sont pas respectés, vous pouvez introduire une réclamation auprès de la CNIL (Commission Nationale de l'Informatique et des Libertés) :
                <a href="https://www.cnil.fr" target="_blank" rel="noopener noreferrer" class="text-b3 hover:text-b6 transition-colors duration-300">www.cnil.fr</a>
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-white mb-2">10. Modification de la politique</h3>
              <p>
                Cette politique de confidentialité peut être mise à jour à tout moment, notamment en cas d'évolution légale ou technique.
                La version en vigueur est celle publiée sur le site à la date de votre consultation.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

defineProps({
  isOpen: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits(['close']);

const closeModal = () => {
  emit('close');
};

// Formater la date actuelle
const currentDate = ref(new Date().toLocaleDateString('fr-FR', {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
}));
</script>

<style scoped>
/* Style pour les liens */
a {
  text-decoration: none;
  transition: all 0.3s ease;
}

/* Style pour les éléments cliquables */
button, 
a, 
[role="button"],
.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}
</style>
