<template>
  <!-- <PERSON><PERSON> de demande de démo -->
  <div v-if="isOpen" 
       class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
       @click="closeModal">
    <div class="bg-white rounded-3xl p-5 lg:p-6 w-[450px] lg:w-[500px] max-h-[85vh] my-4 mx-4 transform transition-all duration-300 scale-100 overflow-y-auto"
         @click.stop>
      
      <!-- Bouton de fermeture -->
      <div class="flex justify-end mb-2">
        <button 
          @click="closeModal"
          class="w-7 h-7 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
          title="Fermer">
          <i class="fas fa-times text-gray-600 text-sm"></i>
        </button>
      </div>
      
      <!-- Formulaire de demande -->
      <div v-if="!formSubmitted">
        <!-- Header -->
        <div class="text-center mb-5">
          <div class="w-12 h-12 bg-gradient-to-r from-b3 to-b6 rounded-full flex items-center justify-center mx-auto mb-3">
            <i class="fas fa-eye text-lg text-white"></i>
          </div>
          <h3 class="text-xl font-bold text-slate-900 mb-2">Accéder à la démo</h3>
          <p class="text-slate-600 text-sm">Remplissez ce formulaire pour recevoir le lien de démo de <strong>{{ product.name }}</strong> par email</p>
        </div>

        <!-- Formulaire -->
        <form
          name="demande-demo"
          method="POST"
          data-netlify="true"
          netlify-honeypot="bot-field"
          @submit.prevent="submitDemoRequest" 
          class="space-y-4">
          
          <!-- Champs cachés pour Netlify -->
          <input type="hidden" name="form-name" value="demande-demo" />
          <input type="hidden" name="bot-field" />
          <input type="hidden" name="produit" :value="product.name" />

          <!-- Nom et Prénom -->
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-xs font-semibold text-slate-700 mb-1">
                Prénom <span class="text-red-500">*</span>
              </label>
              <input 
                v-model="contactForm.firstName"
                type="text" 
                name="prenom"
                required
                class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-b3 focus:border-transparent transition-all text-slate-900 bg-white text-sm"
                placeholder="Votre prénom"
              />
            </div>
            <div>
              <label class="block text-xs font-semibold text-slate-700 mb-1">
                Nom <span class="text-red-500">*</span>
              </label>
              <input 
                v-model="contactForm.lastName"
                type="text" 
                name="nom"
                required
                class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-b3 focus:border-transparent transition-all text-slate-900 bg-white text-sm"
                placeholder="Votre nom"
              />
            </div>
          </div>

          <!-- Email -->
          <div>
            <label class="block text-xs font-semibold text-slate-700 mb-1">
              Email <span class="text-red-500">*</span>
            </label>
            <input 
              v-model="contactForm.email"
              type="email" 
              name="email"
              required
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-b3 focus:border-transparent transition-all text-slate-900 bg-white text-sm"
              placeholder="<EMAIL>"
            />
          </div>

          <!-- Téléphone -->
          <div>
            <label class="block text-xs font-semibold text-slate-700 mb-1">
              Téléphone <span class="text-slate-500">(optionnel)</span>
            </label>
            <input 
              v-model="contactForm.phone"
              type="tel" 
              name="telephone"
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-b3 focus:border-transparent transition-all text-slate-900 bg-white text-sm"
              placeholder="06 12 34 56 78"
            />
          </div>

          <!-- Message -->
          <div>
            <label class="block text-xs font-semibold text-slate-700 mb-1">
              Message <span class="text-slate-500">(optionnel)</span>
            </label>
            <textarea 
              v-model="contactForm.message"
              name="message"
              rows="3"
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-b3 focus:border-transparent transition-all text-slate-900 bg-white text-sm resize-none"
              placeholder="Décrivez votre projet, vos besoins spécifiques..."
            ></textarea>
          </div>

          <!-- Message d'erreur -->
          <div
            v-if="formError"
            class="p-2 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm"
            role="alert"
          >
            <div class="flex items-center">
              <i class="fas fa-exclamation-triangle mr-2 text-xs"></i>
              <span>Une erreur est survenue. Veuillez réessayer.</span>
            </div>
          </div>

          <!-- Boutons -->
          <div class="space-y-2 pt-3">
            <button 
              type="submit"
              :disabled="isSubmittingForm"
              class="w-full bg-gradient-to-r from-b3 to-b6 hover:from-b4 hover:to-b3 text-white font-bold py-3 px-4 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-sm">
              <div v-if="isSubmittingForm" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              <i v-else class="fas fa-paper-plane mr-2 text-xs"></i>
              {{ isSubmittingForm ? 'Envoi en cours...' : 'Recevoir le lien de démo' }}
            </button>
            
            <button 
              type="button"
              @click="closeModal" 
              class="w-full bg-slate-100 hover:bg-slate-200 text-slate-700 font-semibold py-2 px-4 rounded-xl transition-all duration-300 text-sm">
              Annuler
            </button>
          </div>
        </form>
      </div>

      <!-- Message de confirmation -->
      <div v-else class="text-center">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-check text-2xl text-green-600"></i>
        </div>
        <h3 class="text-lg font-bold text-slate-900 mb-3">Demande envoyée !</h3>
        <p class="text-slate-600 mb-4 text-sm">
          Merci <strong>{{ contactForm.firstName }}</strong> ! Vous allez recevoir le lien de démo de <strong>{{ product.name }}</strong> à l'adresse <strong>{{ contactForm.email }}</strong> dans quelques minutes.
        </p>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <p class="text-blue-800 text-xs">
            <i class="fas fa-info-circle mr-2"></i>
            Pensez à vérifier vos spams si vous ne recevez pas l'email.
          </p>
        </div>
        <div class="space-y-2">
          <button 
            @click="closeModal" 
            class="w-full bg-gradient-to-r from-b3 to-b6 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:shadow-lg text-sm">
            Fermer
          </button>
          <p class="text-center text-slate-500 text-xs">
            Cette fenêtre se fermera automatiquement dans quelques secondes
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['close'])

// État réactif
const formSubmitted = ref(false)
const isSubmittingForm = ref(false)
const formError = ref(false)
const contactForm = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  message: ''
})

// Méthodes
function closeModal() {
  emit('close')
  formSubmitted.value = false
  // Reset form
  contactForm.value = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    message: ''
  }
}

function submitDemoRequest(event) {
  // Validation manuelle des champs obligatoires
  if (!contactForm.value.firstName.trim()) {
    alert('Le prénom est obligatoire')
    return
  }

  if (!contactForm.value.lastName.trim()) {
    alert('Le nom est obligatoire')
    return
  }

  if (!contactForm.value.email.trim()) {
    alert('L\'email est obligatoire')
    return
  }

  // Validation format email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(contactForm.value.email)) {
    alert('Veuillez entrer un email valide')
    return
  }

  // Mettre à jour les états du formulaire
  isSubmittingForm.value = true
  formError.value = false

  // Récupérer les données du formulaire
  const myForm = event.target
  const formDataObj = new FormData(myForm)

  // Débogage - Afficher les données du formulaire
  console.log('Données du formulaire de démo à envoyer:')
  for (const pair of formDataObj.entries()) {
    console.log(pair[0] + ': ' + pair[1])
  }

  // Suivre exactement l'exemple de la documentation Netlify (même logique que ContactModal.vue)
  fetch("/", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: new URLSearchParams(formDataObj).toString()
  })
  .then(() => {
    console.log("Formulaire de démo envoyé avec succès")
    // Succès : afficher le message et réinitialiser
    formSubmitted.value = true
    myForm.reset()
    
    // Réinitialiser les données du formulaire
    contactForm.value = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      message: ''
    }
    
    // Fermer la modale après 3 secondes
    setTimeout(() => {
      closeModal()
    }, 3000)
  })
  .catch(error => {
    console.error("Erreur lors de l'envoi du formulaire de démo:", error)
    formError.value = true
  })
  .finally(() => {
    isSubmittingForm.value = false
  })
}
</script> 