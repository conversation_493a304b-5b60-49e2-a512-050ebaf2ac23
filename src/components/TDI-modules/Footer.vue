<script setup>
import { ref, onMounted } from "vue";
import {
  GlobeAltIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  ArrowUpIcon,
  CodeBracketIcon,
  PaintBrushIcon,
  RocketLaunchIcon,
  ShieldCheckIcon,
} from "@heroicons/vue/24/outline";
import LegalModal from "./LegalModal.vue";
import CGVModal from "./CGVModal.vue";
import PrivacyModal from "./PrivacyModal.vue";

// Animation des éléments
const isVisible = ref(false);
const currentYear = new Date().getFullYear();

// État des modales
const isLegalModalOpen = ref(false);
const isCGVModalOpen = ref(false);
const isPrivacyModalOpen = ref(false);

// Ouvrir la modale des mentions légales
const openLegalModal = () => {
  isLegalModalOpen.value = true;
  document.body.style.overflow = "hidden"; // Empêcher le défilement du fond
};

// Fermer la modale des mentions légales
const closeLegalModal = () => {
  isLegalModalOpen.value = false;
  document.body.style.overflow = ""; // Rétablir le défilement
};

// Ouvrir la modale des CGV
const openCGVModal = () => {
  isCGVModalOpen.value = true;
  document.body.style.overflow = "hidden";
};

// Fermer la modale des CGV
const closeCGVModal = () => {
  isCGVModalOpen.value = false;
  document.body.style.overflow = "";
};

// Ouvrir la modale de la politique de confidentialité
const openPrivacyModal = () => {
  isPrivacyModalOpen.value = true;
  document.body.style.overflow = "hidden";
};

// Fermer la modale de la politique de confidentialité
const closePrivacyModal = () => {
  isPrivacyModalOpen.value = false;
  document.body.style.overflow = "";
};

// Effet de parallaxe au défilement - optimisé avec throttling
let ticking = false;
let lastScrollPosition = 0;

const handleScroll = () => {
  lastScrollPosition = window.scrollY;

  if (!ticking) {
    window.requestAnimationFrame(() => {
      const footerElements = document.querySelectorAll(".parallax-element");

      // Vérifier si la section est visible pour économiser des ressources
      const footer = document.querySelector("footer");
      if (footer) {
        const footerRect = footer.getBoundingClientRect();
        const isVisible = footerRect.top < window.innerHeight && footerRect.bottom >= 0;

        if (isVisible) {
          footerElements.forEach((element, index) => {
            const speed = 0.05 + index * 0.01;
            const yPos = lastScrollPosition * speed;
            element.style.transform = `translateY(${-yPos}px)`;
          });
        }
      }

      ticking = false;
    });

    ticking = true;
  }
};

// Scroll vers le haut optimisé
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

// Scroll vers le bas (section contact)
const scrollToBottom = () => {
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: 'smooth'
  });
};

// Animation au chargement
onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, 300);

  window.addEventListener("scroll", handleScroll);

  // Nettoyage de l'event listener
  return () => {
    window.removeEventListener("scroll", handleScroll);
  };
});
</script>

<template>
  <footer class="relative overflow-hidden" role="contentinfo" aria-label="Informations de contact et liens utiles">
    <!-- Vague supérieure décorative -->
    <!-- <div
      class="absolute top-0 left-0 w-full overflow-hidden line-height-0 transform rotate-180"
    >
      <svg
        class="relative block w-full h-20 md:h-32"
        data-name="Layer 1"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
      >
        <path
          d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z"
          class="fill-b1"
        ></path>
      </svg>
    </div> -->

    <!-- Fond avec motif et dégradé -->
    <div class="bg-gradient-to-b from-b1 to-black pt-32 pb-16 relative">
      <!-- Motif de points -->
      <div
        class="absolute inset-0 opacity-9"
        style="
          background-size: 30px 30px;
          background-image: radial-gradient(
            circle,
            rgba(255, 255, 255, 0.4) 1px,
            transparent 1px
          );
        "
      ></div>

      <!-- Formes décoratives avec effet parallaxe -->
      <div
        class="absolute top-20 left-10 w-64 h-64 bg-b3/5 rounded-full filter blur-3xl parallax-element"
      ></div>
      <div
        class="absolute bottom-40 right-10 w-80 h-80 bg-b6/5 rounded-full filter blur-3xl parallax-element"
      ></div>
      <div
        class="absolute top-40 right-1/4 w-40 h-40 bg-b2/5 rounded-full filter blur-3xl parallax-element"
      ></div>

      <!-- Contenu principal -->
      <div class="max-w-7xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Section supérieure avec logo et coordonnées -->
        <div
          class="grid grid-cols-1 lg:grid-cols-5 gap-8 xs:gap-12 mb-12 xs:mb-16 pb-12 xs:pb-16 border-b border-white/10"
        >
          <!-- Logo et description -->
          <div
            class="lg:col-span-2 flex flex-col items-center text-center lg:items-start lg:text-left"
          >
            <div class="mb-6 relative group">
              <div
                class="absolute inset-0 bg-gradient-to-r from-b3/30 to-b6/30 rounded-full blur-md group-hover:blur-lg transition-all duration-500"
              ></div>
              <img
                class="h-12 xs:h-16 w-auto relative z-10 object-contain"
                src="/src/assets/logo-TheDI-removeBG.png"
                alt="TheDevImpact logo"
                width="64"
                height="64"
                loading="lazy"
              />
            </div>
            <h3 class="text-xl xs:text-2xl font-bold text-white mb-3 xs:mb-4">
              The Dev Impact
            </h3>
            <p class="text-white/70 max-w-md text-sm xs:text-base">
              Nous créons des sites web performants et élégants qui transforment vos
              visiteurs en clients. Notre approche sur mesure garantit un résultat unique
              qui vous ressemble.
            </p>

            <!-- Réseaux sociaux -->
            <!-- <div class="flex space-x-4 mt-6">
              <a
                href="#"
                class="w-8 h-8 xs:w-10 xs:h-10 rounded-full bg-white/5 hover:bg-b6/20 flex items-center justify-center transition-colors duration-300"
                aria-label="Facebook"
                rel="noopener noreferrer"
              >
                <svg
                  class="w-4 h-4 xs:w-5 xs:h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white/5 hover:bg-b6/20 flex items-center justify-center transition-colors duration-300"
                aria-label="Instagram"
                rel="noopener noreferrer"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                  ></path>
                </svg>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white/5 hover:bg-b6/20 flex items-center justify-center transition-colors duration-300"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white/5 hover:bg-b6/20 flex items-center justify-center transition-colors duration-300"
                aria-label="YouTube"
                rel="noopener noreferrer"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
            </div> -->
          </div>

          <!-- Coordonnées et contact -->
          <div class="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Coordonnées -->
            <div class="flex flex-col items-center text-center md:items-start md:text-left">
              <h4 class="text-white text-lg font-semibold mb-4 flex items-center justify-center md:justify-start">
                <MapPinIcon class="w-5 h-5 mr-2 text-b6" />
                Coordonnées
              </h4>
              <ul class="space-y-3 text-white/70">
                <li class="flex items-start justify-center md:justify-start">
                  <GlobeAltIcon class="w-5 h-5 mr-2 text-b3 flex-shrink-0 mt-0.5" />
                  <a
                    href="https://thedevimpact.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="text-white/70 hover:text-white transition-colors duration-300 cursor-pointer"
                  >
                    thedevimpact.com
                  </a>
                </li>
                <li class="flex items-start justify-center md:justify-start footer-email">
                  <EnvelopeIcon class="w-5 h-5 mr-2 text-b3 flex-shrink-0 mt-0.5 footer-email" />
                  <a
                    href="mailto:<EMAIL>?subject=Demande%20d'information%20-%20The%20Dev%20Impact&body=Bonjour,%0A%0AJe%20vous%20contacte%20suite%20à%20ma%20visite%20sur%20votre%20site%20web.%20Je%20souhaiterais%20obtenir%20plus%20d'informations%20concernant%20vos%20services.%0A%0AMerci%20d'avance%20pour%20votre%20réponse.%0A%0ACordialement,"
                    class="text-white/70 hover:text-white transition-colors duration-300 cursor-pointer footer-email"
                    aria-label="Envoyer un email à <EMAIL>"
                  >
                    <EMAIL>
                  </a>
                </li>

                <li class="flex items-start justify-center md:justify-start footer-telephone">
                  <PhoneIcon class="w-5 h-5 mr-2 text-b3 flex-shrink-0 mt-0.5 footer-telephone" />
                  <a
                    href="tel:+33601273775"
                    class="text-white/70 hover:text-white transition-colors duration-300 cursor-pointer footer-telephone"
                    aria-label="Appeler le +33 6 01 27 37 75"
                  >
                    (+33) 06 01 27 37 75
                  </a>
                </li>
                <li class="flex items-start justify-center md:justify-start mt-6 pt-4 border-t border-white/10 footer-comeup">
                  <svg class="w-5 h-5 mr-2 text-[#FEEF6C] flex-shrink-0 mt-0.5 footer-comeup" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                  </svg>
                  <a
                    href="https://www.comeup.com/fr/@thedevimpact"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="text-[#FEEF6C] hover:text-[#FEEF6C]/80 transition-colors duration-300 cursor-pointer font-medium footer-comeup"
                    aria-label="Me retrouver sur ComeUp"
                  >
                    Retrouvez-nous sur COMEUP
                  </a>
                </li>
              </ul>
            </div>

            <!-- Services -->
            <div class="flex flex-col items-center text-center md:items-start md:text-left">
              <h4 class="text-white text-lg font-semibold mb-4 flex items-center justify-center md:justify-start">
                <RocketLaunchIcon class="w-5 h-5 mr-2 text-b6" />
                Services
              </h4>
              <ul class="space-y-2">
                <li>
                  <a
                    href="/formules"
                    class="text-white/70 hover:text-white transition-colors duration-300 flex items-center justify-center md:justify-start"
                  >
                    <SparklesIcon class="w-4 h-4 mr-2 text-b3" />
                    <span>Site sur-mesure</span>
                  </a>
                </li>
                <li>
                  <a
                    href="/boutique"
                    class="text-white/70 hover:text-white transition-colors duration-300 flex items-center justify-center md:justify-start"
                  >
                    <ShoppingCartIcon class="w-4 h-4 mr-2 text-b3" />
                    <span>La Collection</span>
                  </a>
                </li>
                <li>
                  <a
                    href="/options"
                    class="text-white/70 hover:text-white transition-colors duration-300 flex items-center justify-center md:justify-start"
                  >
                    <PuzzlePieceIcon class="w-4 h-4 mr-2 text-b3" />
                    <span>Options & modules</span>
                  </a>
                </li>
                <li>
                  <a
                    href="/community-management"
                    class="text-white/70 hover:text-white transition-colors duration-300 flex items-center justify-center md:justify-start"
                  >
                    <MegaphoneIcon class="w-4 h-4 mr-2 text-b3" />
                    <span>Community management</span>
                  </a>
                </li>
              </ul>
            </div>

            <!-- Navigation -->
            <div class="flex flex-col items-center text-center md:items-start md:text-left">
              <h4 class="text-white text-lg font-semibold mb-4 flex items-center justify-center md:justify-start">
                <ArrowUpIcon class="w-5 h-5 mr-2 text-b6" />
                Navigation
              </h4>
              <ul class="space-y-2">
                <li class="flex justify-center md:justify-start footer-accueil">
                  <a
                    href="/"
                    class="text-white/70 hover:text-white transition-colors duration-300 footer-accueil"
                    >Accueil</a
                  >
                </li>
                <li class="flex justify-center md:justify-start footer-formules">
                  <a
                    href="/formules"
                    class="text-white/70 hover:text-white transition-colors duration-300 footer-formules"
                    >Création sur-mesure</a
                  >
                </li>
                <li class="flex justify-center md:justify-start footer-boutique">
                  <a
                    href="/boutique"
                    class="text-white/70 hover:text-white transition-colors duration-300 footer-boutique"
                    >La Collection</a
                  >
                </li>
                <li class="flex justify-center md:justify-start footer-accompagnement">
                  <a
                    href="/accompagnement"
                    class="text-white/70 hover:text-white transition-colors duration-300 footer-accompagnement"
                    >Accompagnement horaire</a
                  >
                </li>
                <li class="flex justify-center md:justify-start footer-pack">
                  <a
                    href="/pack-creation"
                    class="text-white/70 hover:text-white transition-colors duration-300 footer-pack"
                    >Pack création</a
                  >
                </li>
                <li class="flex justify-center md:justify-start footer-contact">
                  <button
                    @click="scrollToBottom"
                    class="text-white/70 hover:text-white transition-colors duration-300 footer-contact cursor-pointer"
                    >Contact</button
                  >
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Section inférieure avec newsletter et copyright -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <!-- Newsletter -->
          <div class="space-y-4 relative pointer-events-none overflow-hidden p-6 pt-8 rounded-lg border border-white/10 mt-4 flex flex-col items-center text-center md:items-start md:text-left">
            <!-- Overlay semi-transparent pour renforcer l'effet inactif -->

            <div class="absolute top-4 right-4 border text-white text-xs font-semibold px-3 py-1 rounded-full shadow-md transform z-20">
              Bientôt disponible
            </div>
            <h4 class="text-white/70 text-lg font-semibold relative z-20 opacity-50">Restez informé</h4>
            <p class="text-white/50 relative z-20 opacity-50">
              Abonnez-vous à notre newsletter pour recevoir nos actualités et offres
              spéciales
            </p>
            <div class="flex max-w-md relative z-20 opacity-50">
              <input
                type="email"
                placeholder="Votre email"
                class="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-l-lg focus:outline-none focus:border-b3 text-white opacity-50"
                disabled
              />
              <button
                class="px-4 py-2 bg-b6 text-white rounded-r-lg hover:bg-b6/90 transition-colors duration-300 opacity-50"
                disabled
              >
                S'abonner
              </button>
            </div>
          </div>

          <!-- Copyright et mentions légales -->
          <div class="flex flex-col items-center text-center md:items-end md:text-right space-y-4">
            <div class="flex space-x-4 justify-center md:justify-end">
              <button
                @click="openLegalModal"
                class="text-white/70 hover:text-white text-sm transition-colors duration-300 cursor-pointer"
                aria-label="Voir les mentions légales"
              >
                Mentions légales
              </button>
              <button
                @click="openPrivacyModal"
                class="text-white/70 hover:text-white text-sm transition-colors duration-300 cursor-pointer"
                aria-label="Voir la politique de confidentialité"
              >
                Politique de confidentialité
              </button>
              <button
                @click="openCGVModal"
                class="text-white/70 hover:text-white text-sm transition-colors duration-300 cursor-pointer"
                aria-label="Voir les conditions générales de vente"
              >
                CGV
              </button>
            </div>
            <p class="text-white/50 text-sm text-center md:text-right">
              © {{ currentYear }} The Dev Impact. Tous droits réservés.
            </p>
          </div>
        </div>
      </div>

      <!-- Bouton de retour en haut -->
      <button
        @click="scrollToTop"
        class="absolute bottom-2 left-8 w-12 h-12 rounded-full bg-b6/90 hover:bg-b6 text-white flex items-center justify-center shadow-lg transition-all duration-300 hover:scale-110 group cursor-pointer"
        aria-label="Retour en haut de page"
      >
        <ArrowUpIcon class="w-6 h-6" />

        <!-- Tooltip -->
        <div
          class="absolute left-full ml-3 px-3 py-2 bg-white text-gray-800 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
          role="tooltip"
          aria-hidden="true"
        >
          Retour en haut
          <!-- Petite flèche pointant vers le bouton -->
          <div
            class="absolute top-1/2 left-0 transform -translate-x-1/2 -translate-y-1/2 rotate-45 w-2 h-2 bg-white"
          ></div>
        </div>
      </button>
    </div>

    <!-- Modales -->
    <LegalModal :is-open="isLegalModalOpen" @close="closeLegalModal" />
    <CGVModal :is-open="isCGVModalOpen" @close="closeCGVModal" />
    <PrivacyModal :is-open="isPrivacyModalOpen" @close="closePrivacyModal" />
  </footer>
</template>

<style scoped>
/* Animation d'entrée */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Effet de flottement pour les formes décoratives - optimisé */
.parallax-element {
  animation: float 8s ease-in-out infinite;
  will-change: transform;
}

@media (prefers-reduced-motion: reduce) {
  .parallax-element {
    animation: none;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}
</style>
