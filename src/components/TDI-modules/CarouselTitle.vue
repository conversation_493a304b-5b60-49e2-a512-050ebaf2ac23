<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
});
</script>

<template>
  <div class="bg-transparent -rotate-2">
    <Vue3Marquee
      class="text-base font-medium tracking-wide"
      pauseOnHover
      :duration="40"
      clone
    >
      <h2
        class="text-xl sm:text-xl font-bold bg-gradient-to-l from-b4 via-b2 to-b4 text-transparent bg-clip-text text-center "
      >
        {{ props.title }}
      </h2>
      <span
        class="text-xl sm:text-xl font-bold bg-gradient-to-r from-white to-b4 text-transparent bg-clip-text text-center  mx-4"
        >-</span
      >
    </Vue3Marquee>
  </div>
</template>
