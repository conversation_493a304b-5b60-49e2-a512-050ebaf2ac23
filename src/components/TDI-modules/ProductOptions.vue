<template>
  <section id="options" class="relative z-10 bg-white" aria-labelledby="options-title">
    <div class="py-16 sm:py-20 md:py-28">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header Section -->
        <div class="mb-12 md:mb-16 text-center">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-gray-100 border border-gray-200 text-gray-700 text-sm font-medium mb-6">
            <span class="w-2 h-2 rounded-full bg-b3 mr-2"></span>
            Options additionnelles
            <span class="w-2 h-2 rounded-full bg-b6 ml-2"></span>
          </div>

          <h2 id="options-title" class="text-3xl md:text-4xl font-bold mb-6">
            <span class="bg-gradient-to-r from-b3 via-b4 to-b6 text-transparent bg-clip-text">
              Services complémentaires
            </span>
          </h2>
          <p class="text-gray-600 text-lg max-w-3xl mx-auto leading-relaxed">
            Personnalisez votre projet avec ces services complémentaires pour répondre
            parfaitement à vos besoins.
          </p>
        </div>

        <!-- Options Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div
            v-for="(option, index) in options"
            :key="index"
            class="group relative bg-white rounded-2xl border border-gray-200 overflow-hidden transition-all duration-500 hover:border-b3 hover:shadow-xl hover:scale-[1.02]"
            itemscope itemtype="https://schema.org/Service"
          >
            <!-- Top accent line -->
            <div class="h-1 w-full bg-gradient-to-r from-b3 via-b4 to-b6"></div>

            <!-- Content -->
            <div class="p-6">
              <!-- Header with icon and title -->
              <div class="flex items-start mb-4">
                <div class="flex-shrink-0 w-12 h-12 rounded-xl bg-gradient-to-br from-b3/20 to-b6/20 flex items-center justify-center mr-4 border border-b3/30">
                  <component
                    :is="option.icon"
                    class="h-6 w-6 text-b3"
                    v-if="option.icon"
                  />
                  <span class="text-xl text-b3 font-bold" v-else>+</span>
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="text-lg font-bold text-gray-900 mb-1 leading-tight" itemprop="name">
                    {{ option.title }}
                  </h3>
                  <div class="flex items-center">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200">
                      {{ getCategoryBadge(option.title) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- Description -->
              <p class="text-gray-600 text-sm leading-relaxed mb-4" itemprop="description">
                {{ option.description }}
              </p>

              <!-- Features list -->
              <div v-if="option.features" class="mb-4 space-y-2">
                <div
                  v-for="(feature, fIndex) in option.features"
                  :key="fIndex"
                  class="flex items-center text-sm"
                >
                  <div class="flex-shrink-0 w-5 h-5 rounded-full bg-b2 flex items-center justify-center mr-3">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <span class="text-gray-700">{{ feature }}</span>
                </div>
              </div>

              <!-- Detail section -->
              <div
                v-if="option.detail"
                class="mb-4 p-3 rounded-xl bg-gray-50 border border-gray-200"
              >
                <p class="text-gray-600 text-sm leading-relaxed italic">
                  {{ option.detail }}
                </p>
              </div>

              <!-- Delay info -->
              <div
                v-if="option.delay"
                class="mb-4 flex items-center text-gray-500 text-xs"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{{ option.delay }}</span>
              </div>

              <!-- Price badge and action button -->
              <div class="mt-6 flex items-center justify-between">
                <div class="inline-flex items-center px-4 py-2 rounded-xl bg-gradient-to-r from-b3/20 to-b6/20 border border-b3/30 text-b3 font-semibold text-sm">
                  <span itemprop="price">{{ option.price }}</span>
                </div>
                
                <!-- Action button based on option type -->
                <button 
                  v-if="option.price !== 'Sur devis' && !option.price.includes('/heure')"
                  @click="addToCart(option)"
                  class="inline-flex items-center px-4 py-2 rounded-xl bg-b3 hover:bg-b4 text-white font-semibold text-sm transition-all duration-300 hover:scale-105"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                  </svg>
                  Ajouter
                </button>
                
                <button 
                  v-else
                  @click="contactForQuote(option)"
                  class="inline-flex items-center px-4 py-2 rounded-xl bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold text-sm transition-all duration-300 hover:scale-105"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  Contact
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from "vue";
import { useCartStore } from "../../store/cart.js";
import {
  PaintBrushIcon,
  DocumentPlusIcon,
  WrenchScrewdriverIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
} from "@heroicons/vue/24/outline";

const cartStore = useCartStore();

// Fonction pour obtenir le badge de catégorie
const getCategoryBadge = (title) => {
  const categories = {
    "Pack Création d'entreprise": "Pack complet",
    "Accompagnement web & marketing": "Formation",
    "Modifications ponctuelles": "Service",
    "Visuels personnalisés": "Design",
    "Page supplémentaire": "Développement",
    "Maintenance": "Support",
    "Nom de domaine personnalisé": "Infrastructure",
    "Adresse e-mail professionnelle": "Infrastructure"
  };
  return categories[title] || "Service";
};

// Options additionnelles avec métadonnées pour le SEO
const options = ref([
  {
    title: "Pack Création d'entreprise",
    description:
      "Pour bien démarrer ! Un pack complet pour lancer votre entreprise avec tous les éléments essentiels.",
    price: "Sur devis",
    icon: BuildingOfficeIcon,
    bgColor: "bg-b3",
    borderColor: "border-b3",
    iconColor: "text-b3",
    features: [
      "Création logo + carte de visite",
      "Page Google Business + adresse mail pro",
      "Réseaux sociaux (Instagram & Facebook)",
      "Site one-page personnalisé",
    ],
    detail:
      "Pack idéal pour les entrepreneurs qui démarrent et veulent une présence digitale complète et professionnelle.",
    delay: "Livraison en 7-10 jours",
  },
  {
    title: "Accompagnement web & marketing",
    description:
      "On travaille ensemble sur tes besoins web & com : contenus, visuels, stratégie, outils, organisation, etc.",
    price: "70€/heure TTC",
    icon: UserGroupIcon,
    bgColor: "bg-b4",
    borderColor: "border-b4",
    iconColor: "text-b4",
    features: [
      "Accompagnement en direct (visio ou présentiel)",
      "Réalisation avec toi des tâches web/marketing",
      "Explication claire pour que tu puisses refaire",
    ],
    detail:
      "Idéal pour les entrepreneurs autonomes qui veulent apprendre tout en avançant.",
    delay: "Sur rendez-vous",
  },
  {
    title: "Modifications ponctuelles",
    description:
      "Besoin de modifications sur un site que nous avons déjà réalisé ? Nous vous proposons un service de modifications ponctuelles, rapide et sur mesure.",
    price: "Sur devis",
    icon: WrenchScrewdriverIcon,
    bgColor: "bg-b5",
    borderColor: "border-b5",
    iconColor: "text-b5",
    detail:
      "Idéal pour mettre à jour votre contenu, ajuster une section, ou intégrer un nouvel élément.",
    delay: "Délai supplémentaire de 4 jours",
  },
  {
    title: "Visuels personnalisés",
    description:
      "Logo, bannière, illustration, visuel digital… Création sur-mesure adaptée à votre identité et à vos supports.",
    price: "+120€ / visuel",
    icon: PaintBrushIcon,
    bgColor: "bg-b2",
    borderColor: "border-b2",
    iconColor: "text-b2",
    detail:
      "Idéal pour renforcer votre image de marque avec des éléments graphiques cohérents et professionnels.",
    delay: "Délai supplémentaire de 4 jours",
  },
  {
    title: "Page supplémentaire",
    description:
      "Besoin d'ajouter une page à votre site (ex : blog, galerie, mentions légales…) ?",
    price: "Sur devis",
    icon: DocumentPlusIcon,
    bgColor: "bg-b1",
    borderColor: "border-b1",
    iconColor: "text-b1",
    detail:
      "Chaque page comprend son design personnalisé et une optimisation de base pour le SEO.",
    delay: "Délai supplémentaire de 4 jours",
  },
  {
    title: "Maintenance",
    description: "Assurez la stabilité de votre site en continu.",
    price: "+180€",
    icon: WrenchScrewdriverIcon,
    bgColor: "bg-b6",
    borderColor: "border-b6",
    iconColor: "text-b6",
    features: [
      "Corrections de bugs",
      "Ajustements mineurs (textes, images)",
      "Mises à jour",
      "Optimisation SEO",
    ],
    delay: "Délai supplémentaire de 4 jours",
  },
  {
    title: "Nom de domaine personnalisé",
    description:
      "www.nom-entreprise.com, c'est plus professionnel et rassurant pour vos clients.",
    price: "+150€",
    icon: GlobeAltIcon,
    bgColor: "bg-b4",
    borderColor: "border-b4",
    iconColor: "text-b4",
    detail:
      "Nous nous occupons de la configuration. Le nom de domaine est à acheter séparément (comptez environ 12 €/an).",
    delay: "Délai supplémentaire de 4 jours",
  },
  {
    title: "Adresse e-mail professionnelle",
    description:
      "<EMAIL> donne immédiatement une image sérieuse et pro.",
    price: "+120€",
    icon: EnvelopeIcon,
    bgColor: "bg-b6",
    borderColor: "border-b6",
    iconColor: "text-b6",
    detail:
      "Nous créons et configurons pour vous un e-mail pro avec Google Workspace. L'abonnement Google est à votre charge (environ 8,10 €/mois)",
    delay: "Délai supplémentaire de 2 jours",
  },
]);

// Fonctions pour les actions
const addToCart = (option) => {
  cartStore.addAdditionalOption(option, 1);
};

const contactForQuote = (option) => {
  // Émettre un événement pour ouvrir la modale de contact
  // ou rediriger vers la page de contact
  window.location.href = '/#contact';
};
</script>

<style scoped>
/* Effet de transition pour les cartes */
.transform {
  transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
}

/* Responsive design amélioré */
@media (max-width: 768px) {
  .grid-cols-1 > div {
    margin-bottom: 1rem;
  }

  /* Optimisation pour la version mobile */
  .mb-4 {
    margin-bottom: 0.75rem;
  }

  .mb-6 {
    margin-bottom: 1rem;
  }

  .mb-8 {
    margin-bottom: 1.5rem;
  }

  .mb-16 {
    margin-bottom: 2rem;
  }

  .mt-28 {
    margin-top: 1.5rem;
  }
}

/* Optimisation pour les très petits écrans */
@media (max-width: 370px) {
  .text-xs {
    font-size: 0.65rem;
  }

  .p-4 {
    padding: 0.75rem;
  }
}
</style> 