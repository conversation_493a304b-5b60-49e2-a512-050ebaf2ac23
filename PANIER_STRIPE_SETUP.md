# Configuration du Système de Panier et Stripe

## 🛒 Fonctionnalités du Panier

### ✅ Fonctionnalités Implémentées

- **Ajout d'articles** : Depuis ProductCard et ProductDetailView
- **Gestion des quantités** : Augmenter/diminuer depuis le panier
- **Suppression d'articles** : Avec confirmation
- **Vider le panier** : Avec confirmation
- **Persistance** : Sauvegarde automatique dans localStorage
- **Notifications** : Toast notifications pour toutes les actions
- **États visuels** : Indicateurs de chargement et d'état
- **Sidebar responsive** : Panier latéral avec overlay
- **Calculs automatiques** : Sous-total, TVA (20%), total
- **Validation** : Gestion d'erreurs et validation des données

### 🎨 Interface Utilisateur

- **CartSidebar** : Panier latéral moderne avec animations
- **ToastNotification** : Système de notifications élégant
- **États des boutons** : Feedback visuel pour toutes les actions
- **Responsive design** : Adapté mobile et desktop

## 💳 Configuration Stripe

### 1. Créer un compte Stripe

1. Allez sur [stripe.com](https://stripe.com)
2. Créez un compte développeur
3. Récupérez vos clés API dans le dashboard

### 2. Configuration des Variables d'Environnement

Créez un fichier `.env` à la racine du projet :

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_votre_cle_secrete_ici
STRIPE_PUBLIC_KEY=pk_test_votre_cle_publique_ici
```

### 3. Mise à Jour du Service Stripe

Modifiez `src/services/stripe.js` :

```javascript
// Remplacez la clé publique par la vôtre
const STRIPE_PUBLIC_KEY = process.env.STRIPE_PUBLIC_KEY || 'pk_test_votre_cle';
```

### 4. Configuration Netlify

Pour déployer sur Netlify :

1. Ajoutez vos variables d'environnement dans Netlify Dashboard
2. La fonction `netlify/functions/create-checkout.js` est prête
3. Activez les Netlify Functions

## 🚀 Utilisation

### Ajouter un Article au Panier

```javascript
import { useCartStore } from '@/store/cart.js'

const cartStore = useCartStore()

// Ajouter un produit
cartStore.addItem(product, quantity)

// Vérifier si un produit est dans le panier
cartStore.isInCart(productId)

// Obtenir la quantité d'un produit
cartStore.getItemQuantity(productId)
```

### Gérer le Panier

```javascript
// Ouvrir/fermer le panier
cartStore.openCart()
cartStore.closeCart()
cartStore.toggleCart()

// Modifier les quantités
cartStore.updateQuantity(productId, newQuantity)

// Supprimer un article
cartStore.removeItem(productId)

// Vider le panier
cartStore.clearCart()
```

### Notifications Toast

```javascript
// Afficher une notification
window.dispatchEvent(new CustomEvent('show-toast', {
  detail: {
    type: 'success', // success, error, warning, info
    title: 'Titre',
    message: 'Message de la notification'
  }
}))
```

## 📦 Structure des Fichiers

```
src/
├── store/
│   └── cart.js                 # Store Pinia du panier
├── services/
│   └── stripe.js              # Service Stripe
├── components/
│   ├── TDI-modules/
│   │   ├── CartSidebar.vue    # Panier latéral
│   │   ├── ToastNotification.vue # Notifications
│   │   └── ProductCard.vue    # Carte produit
│   └── views/
│       ├── ProductDetailView.vue # Page produit
│       └── SuccessPage.vue    # Page de succès
└── router.js                  # Configuration des routes

netlify/
└── functions/
    └── create-checkout.js     # Fonction Stripe Checkout
```

## 🔧 Personnalisation

### Modifier la TVA

Dans `src/store/cart.js` :

```javascript
const tax = computed(() => {
  return subtotal.value * 0.20 // Changez 0.20 pour 20%
})
```

### Ajouter des Devises

Dans `src/services/stripe.js` :

```javascript
export function formatPrice(price, currency = 'EUR') {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: currency
  }).format(price);
}
```

### Personnaliser les Notifications

Modifiez `src/components/TDI-modules/ToastNotification.vue` pour changer :
- Durée d'affichage (défaut: 5000ms)
- Position (défaut: top-right)
- Styles et couleurs

## 🐛 Dépannage

### Erreur "Stripe is not defined"

Vérifiez que la clé publique Stripe est correctement configurée dans `src/services/stripe.js`.

### Le panier ne se sauvegarde pas

Vérifiez que localStorage est disponible et que les données ne sont pas trop volumineuses.

### Erreur de paiement

1. Vérifiez vos clés Stripe (test vs production)
2. Contrôlez les logs dans Netlify Functions
3. Assurez-vous que les montants sont en centimes

### Notifications qui ne s'affichent pas

Vérifiez que `ToastNotification.vue` est bien importé dans `App.vue`.

## 📈 Améliorations Futures

- [ ] Codes promo et réductions
- [ ] Sauvegarde des favoris en base de données
- [ ] Historique des commandes
- [ ] Paiement en plusieurs fois
- [ ] Gestion des stocks
- [ ] Email de confirmation automatique
- [ ] Dashboard administrateur

## 🔒 Sécurité

- ✅ Validation côté client et serveur
- ✅ Gestion d'erreurs robuste
- ✅ Sanitisation des données
- ✅ Clés API sécurisées (variables d'environnement)
- ✅ HTTPS obligatoire pour Stripe

---

**Le système de panier est maintenant complètement fonctionnel et prêt pour la production !** 🎉 