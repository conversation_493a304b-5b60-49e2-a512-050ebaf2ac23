// netlify/functions/create-checkout.js

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

exports.handler = async (event) => {
  // Headers CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  // Gérer les requêtes OPTIONS (preflight)
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Méthode non autorisée' }),
    };
  }

  try {
    const { line_items, success_url, cancel_url } = JSON.parse(event.body);

    // Validation des données
    if (!line_items || !Array.isArray(line_items) || line_items.length === 0) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Articles du panier manquants' }),
      };
    }

    // Créer la session Stripe Checkout
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'payment',
      line_items: line_items,
      success_url: success_url || 'https://thedevimpact.com/success',
      cancel_url: cancel_url || 'https://thedevimpact.com/boutique',
      shipping_address_collection: {
        allowed_countries: ['FR', 'BE', 'LU', 'CH', 'CA'],
      },
      billing_address_collection: 'required',
      metadata: {
        source: 'thedevimpact_website',
        timestamp: new Date().toISOString(),
      },
    });

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ 
        url: session.url,
        session_id: session.id 
      }),
    };
  } catch (error) {
    console.error('Erreur Stripe:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Erreur lors de la création de la session de paiement',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      }),
    };
  }
};
